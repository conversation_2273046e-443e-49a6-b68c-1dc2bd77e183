package report

/* import (
	"context"
	"strconv"

	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/adapter_data"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/config"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
)

// 上报
func Report(ctx context.Context, adapterDataClient adapter_data.AdapterDataClient, key string, uid int64, data map[string]string) {
	req := &adapter_data.QzaReportReq{
		Data: data,
	}
	switch config.GlobalConfig.Client.GRPC.Metadata["app-name"] {
	case "kg":
		req.NewReport = true
	case "music":
		// _app: kugou、qqmusic、qmkege、kuwo、lanren
		req.NewReportQmusic = true
		req.Data["_app"] = "qqmusic"
	case "kugou":
		req.NewReportQmusic = true
		req.Data["_app"] = "kugou"
	case "wx":
		req.NewReportQmusic = true
		req.Data["_app"] = "wechatminigame"
	}
	req.Data["key"] = key
	req.Data["uid"] = strconv.FormatInt(uid, 10)
	_, err := adapterDataClient.QzaReport(ctx, req)
	if err != nil {
		log.Error("report fail", "req", req, "err", err)
	}
	log.Debug("report suc", "req", req)
}
*/
