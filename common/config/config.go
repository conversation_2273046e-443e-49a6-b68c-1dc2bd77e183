package config

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/redis"
	v8 "github.com/go-redis/redis/v8"
)

type ConfigService[T any] struct {
	redisCli        *redis.Client
	configIDSetKey  string //id的key;set
	configKeyPrefix string //配置的key
}

// NewConfigService 配置service
func NewConfigService[T any](redisCli *redis.Client, configIDSetKey, configKeyPrefix string) ConfigService[T] {
	return ConfigService[T]{
		redisCli:        redisCli,
		configIDSetKey:  configIDSetKey,
		configKeyPrefix: configKeyPrefix,
	}
}

func (c *ConfigService[T]) getConfigKey(id int64) string {
	return fmt.Sprintf("%s_%d", c.configKeyPrefix, id)
}

// Store 存储
func (c *ConfigService[T]) Store(ctx context.Context, id int64, t T) error {
	configKey := c.getConfigKey(id)
	if err := c.redisCli.SetArgs(ctx, configKey, t, v8.SetArgs{}).Err(); err != nil {
		return fmt.Errorf("set fail, configKey=%s, err=%w", configKey, err)
	}
	log.Infof("dao suc, key=%s, t=%+v", configKey, t)
	if err := c.redisCli.SAdd(ctx, c.configIDSetKey, id).Err(); err != nil {
		return fmt.Errorf("sadd fail, id=%d, err=%w", id, err)
	}
	return nil
}

/* todo func (c *ConfigService[T]) Delete(ctx context.Context, id int64) error {
	if err := c.redisCli.SRem(ctx, c.configIDSetKey, id).Err(); err != nil {
		return fmt.Errorf("srem fail, key=%s, id=%d, err=%w", c.configIDSetKey, id, err)
	}
	configKey := c.getConfigKey(id)
	if err := c.redisCli.Del(ctx, configKey).Err(); err != nil {
		return fmt.Errorf("del fail, configKey=%s, err=%w", configKey, err)
	}
	log.Infof("del suc, key=%s", configKey)
	return nil
} */

// LoadAll
func (c *ConfigService[T]) LoadAll(ctx context.Context) (map[int64]T, error) {
	strList, err := c.redisCli.SMembers(ctx, c.configIDSetKey).Result()
	if err != nil {
		return nil, fmt.Errorf("get all id fail, err: %w", err)
	}
	log.Debugf("LoadAll strList=%+v", strList)
	idList := make([]int64, 0, len(strList))
	for _, str := range strList {
		id, _ := strconv.ParseInt(str, 10, 64)
		idList = append(idList, id)
	}
	return c.Load(ctx, idList)
}

func (c *ConfigService[T]) Load(ctx context.Context, idList []int64) (map[int64]T, error) {
	if len(idList) == 0 {
		return map[int64]T{}, nil
	}
	keys := make([]string, 0, len(idList))
	for _, id := range idList {
		keys = append(keys, c.getConfigKey(id))
	}
	retSlice, err := c.redisCli.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, fmt.Errorf("redis query fail, err: %w", err)
	}
	log.Debugf("Load keys=%+v, retSlice=%+v", keys, retSlice)
	retMap := make(map[int64]T, len(idList))
	for idx, id := range idList {
		if idx >= len(retSlice) {
			return nil, fmt.Errorf("len(idList) != len(retSlice): %d != %d", len(idList), len(retSlice))
		}
		if retSlice[idx] == nil {
			continue
		}
		var data T
		err = json.Unmarshal([]byte(retSlice[idx].(string)), &data)
		if err != nil {
			return nil, fmt.Errorf("json unmarshal fail, err=%w, data=%v", err, retSlice[idx])
		}
		retMap[id] = data
	}
	return retMap, nil
}
