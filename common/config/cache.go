package config

import (
	"context"
	"fmt"
	"sync/atomic"
	"time"
)

type ConfigCache[T any] struct {
	cache           atomic.Value       //缓存
	beforeStoreFunc BeforeStoreFunc[T] //缓存储存前的操作

	flushDuration time.Duration //刷新时间
	configService *ConfigService[T]
}

type BeforeStoreFunc[T any] func(map[int64]T) map[int64]T

func NewConfigCache[T any](flushDuration time.Duration, configService *ConfigService[T], options ...CacheOption[T]) *ConfigCache[T] {
	confCache := &ConfigCache[T]{
		cache:         atomic.Value{},
		flushDuration: flushDuration,
		configService: configService,
	}
	for _, option := range options {
		option(confCache)
	}
	confCache.CacheTimer(context.Background())
	return confCache
}

// CacheTimer 缓存刷新定时器
func (c *ConfigCache[T]) CacheTimer(ctx context.Context) {
	if err := c.UpdateCache(ctx); err != nil {
		panic(fmt.Sprintf("updateCache fail, err=%v", err))
	}
	go func() {
		for range time.Tick(c.flushDuration) {
			c.UpdateCache(ctx)
		}
	}()
}

// UpdateCache 更新缓存
func (c *ConfigCache[T]) UpdateCache(ctx context.Context) error {
	retMap, err := c.configService.LoadAll(ctx)
	if err != nil {
		return err
	}
	if c.beforeStoreFunc != nil {
		retMap = c.beforeStoreFunc(retMap)
	}
	c.cache.Store(retMap)
	return nil
}

// GetAllCache 获取全部缓存
func (c *ConfigCache[T]) GetAll() map[int64]T {
	data := c.cache.Load()
	info, ok := data.(map[int64]T)
	if !ok {
		return map[int64]T{}
	}
	return info
}

func (c *ConfigCache[T]) GetOne(id int64) (T, bool) {
	cacheMap := c.GetAll()
	cache, ok := cacheMap[id]
	return cache, ok
}
