package lua

import (
	"context"
	"errors"
	"fmt"

	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/redis"
	v8 "github.com/go-redis/redis/v8"
)

func NxIncrBy(ctx context.Context, rd *redis.Client, billNo, scorekey string, score int64) (int64, error) {
	nxKey := fmt.Sprintf("{%s}_%s", scorekey, billNo)
	luaScript := `local billLock = redis.call('set', KEYS[1], 1, 'ex', 21600, 'nx')
										if billLock then
											return redis.call('incrby', KEYS[2], ARGV[1])
										else
											return redis.call('get', KEYS[2])
										end
										`
	curScore, err := rd.EvalScript(ctx, redis.NewScript(luaScript), []string{nxKey, scorekey}, score).Int64()
	if err != nil {
		log.Error("NxIncrBy fail", "err", err, "nxKey", nxKey, "socre", score)
		return 0, err
	}
	log.Info("NxIncrBy succ", "nxKey", nxKey, "score", score, "curScore", curScore)
	return curScore, nil
}

// NxZincrByWithScore 加ZINCRBY zset 方法 只需要传业务唯一的订单号 与 key  返回值是 setnx结果 与 返回加分之后的返回值
func NxZincrByWithScore(ctx context.Context, rd *redis.Client, billNo, rankkey string, score int64, member string) (int64, error) {
	nxKey := fmt.Sprintf("{%s}_%s", rankkey, billNo)
	luaScript := `local billLock = redis.call('set', KEYS[1], 1, 'ex', 21600, 'nx')
					if billLock then
						return redis.call('zincrby', KEYS[2], ARGV[1], ARGV[2])
					else
						return redis.call('zscore', KEYS[2], ARGV[2])
					end
					`
	score, err := rd.EvalScript(ctx, redis.NewScript(luaScript), []string{nxKey, rankkey}, score, member).Int64()
	if err != nil && !errors.Is(err, v8.Nil) {
		return -1, fmt.Errorf("NxZincrByWithScore error: %v, nxKey=%v, rankkey=%v", err, nxKey, rankkey)
	}
	return score, nil
}
