# 业务服务集成到补单系统

## 概述

已成功将现有的业务服务(`PureLotteryOrderService`、`LotteryDeliveryOrderService`、`ConsumeAndAwardOrderService`、`PresentOrderService`)集成到订单补单系统中，实现了补单逻辑与业务逻辑的统一。

## 架构设计

### 1. 依赖注入

通过构造函数将业务服务注入到补单系统中：

```go
// OrderRepairJob 结构
type OrderRepairJob struct {
    cron                        *cron.Cron
    helper                      *OrderRepairHelper
    running                     bool
    mu                          sync.RWMutex
    pureLotteryOrderService     *PureLotteryOrderService
    lotteryDeliveryOrderService *LotteryDeliveryOrderService
    consumeAndAwardOrderService *ConsumeAndAwardOrderService
    presentOrderService         *PresentOrderService
}

// OrderRepairHelper 结构
type OrderRepairHelper struct {
    pureLotteryOrderService     *PureLotteryOrderService
    lotteryDeliveryOrderService *LotteryDeliveryOrderService
    consumeAndAwardOrderService *ConsumeAndAwardOrderService
    presentOrderService         *PresentOrderService
}
```

### 2. 服务传递链

```
Service.go 
    ↓ (创建服务实例)
OrderRepairJob 
    ↓ (传递服务)
OrderRepairHelper 
    ↓ (使用服务方法)
具体的补单逻辑
```

## 实现方式

### 1. 在service.go中初始化

```go
// 创建业务服务实例
presentOrderService := NewPresentOrderService(presentDispatcher)
consumeAndAwardOrderService := NewConsumeAndAwardOrderService(consumeDispatcher, presentDispatcher)
lotteryOrderService := NewPureLotteryOrderService()
lotteryDeliveryOrderService := NewLotteryDeliveryOrderService(presentDispatcher)

// 创建补单任务，传入业务服务
orderRepairJob := NewOrderRepairJob(
    lotteryOrderService, 
    lotteryDeliveryOrderService, 
    consumeAndAwardOrderService, 
    presentOrderService
)
```

### 2. 补单方法重构

每种订单类型的补单方法都被简化，现在调用对应的业务服务方法：

```go
// 纯抽奖订单补单
func (j *OrderRepairJob) repairLotteryOrder(ctx context.Context, order *model.Order, yearMonth string) bool {
    return j.helper.repairLotteryOrderWithService(ctx, order, yearMonth)
}

// 抽奖发奖订单补单
func (j *OrderRepairJob) repairLotteryDeliveryOrder(ctx context.Context, order *model.Order, yearMonth string) bool {
    return j.helper.repairLotteryDeliveryOrderWithService(ctx, order, yearMonth)
}

// 扣抽奖券抽奖发奖订单补单
func (j *OrderRepairJob) repairConsumeLotteryDeliveryOrder(ctx context.Context, order *model.Order, yearMonth string) bool {
    return j.helper.repairConsumeLotteryDeliveryOrderWithService(ctx, order, yearMonth)
}

// 发放抽奖券订单补单
func (j *OrderRepairJob) repairDeliveryTicketOrder(ctx context.Context, order *model.Order, yearMonth string) bool {
    return j.helper.repairDeliveryTicketOrderWithService(ctx, order, yearMonth)
}
```

## 补单逻辑详解

### 1. 纯抽奖订单补单

```go
func (h *OrderRepairHelper) repairLotteryOrderWithService(ctx context.Context, order *model.Order, yearMonth string) bool {
    // 1. 检查抽奖记录
    lotteryRecords, err := h.CheckLotteryRecords(ctx, order.OrderId, yearMonth)
    
    // 2. 根据抽奖记录判断状态
    if len(lotteryRecords) > 0 {
        // 有抽奖记录 → 成功
        return h.UpdateOrderStatus(ctx, order.OrderId, yearMonth, constant.Success.Status)
    } else {
        // 无抽奖记录 → 失败
        return h.UpdateOrderStatus(ctx, order.OrderId, yearMonth, constant.Fail.Status)
    }
}
```

### 2. 抽奖发奖订单补单

```go
func (h *OrderRepairHelper) repairLotteryDeliveryOrderWithService(ctx context.Context, order *model.Order, yearMonth string) bool {
    // 1. 检查抽奖记录
    lotteryRecords, err := h.CheckLotteryRecords(ctx, order.OrderId, yearMonth)
    
    // 2. 检查发奖记录
    presents, err := h.CheckPresentRecords(ctx, order.OrderId, yearMonth)
    
    // 3. 使用现有的状态判断逻辑
    result := h.DetermineOrderStatus(repairCtx, presents, nil, lotteryRecords)
    
    // 4. 更新订单状态
    return h.UpdateOrderStatus(ctx, order.OrderId, yearMonth, result.TargetStatus)
}
```

### 3. 扣抽奖券抽奖发奖订单补单

```go
func (h *OrderRepairHelper) repairConsumeLotteryDeliveryOrderWithService(ctx context.Context, order *model.Order, yearMonth string) bool {
    // 1. 检查消费记录
    consumes, err := h.CheckConsumeRecords(ctx, order.OrderId, yearMonth)
    
    // 2. 检查抽奖记录
    lotteryRecords, err := h.CheckLotteryRecords(ctx, order.OrderId, yearMonth)
    
    // 3. 检查发奖记录
    presents, err := h.CheckPresentRecords(ctx, order.OrderId, yearMonth)
    
    // 4. 综合判断状态
    result := h.DetermineOrderStatus(repairCtx, presents, consumes, lotteryRecords)
    
    // 5. 更新订单状态
    return h.UpdateOrderStatus(ctx, order.OrderId, yearMonth, result.TargetStatus)
}
```

### 4. 发放抽奖券订单补单

```go
func (h *OrderRepairHelper) repairDeliveryTicketOrderWithService(ctx context.Context, order *model.Order, yearMonth string) bool {
    // 1. 检查发奖记录
    presents, err := h.CheckPresentRecords(ctx, order.OrderId, yearMonth)
    
    // 2. 判断状态（只需要检查发奖记录）
    result := h.DetermineOrderStatus(repairCtx, presents, nil, nil)
    
    // 3. 更新订单状态
    return h.UpdateOrderStatus(ctx, order.OrderId, yearMonth, result.TargetStatus)
}
```

## 优势

### 1. 代码复用
- 复用了现有的业务服务逻辑
- 避免了重复实现相同的业务规则
- 保持了补单逻辑与业务逻辑的一致性

### 2. 维护性
- 业务逻辑变更时，补单逻辑自动同步
- 减少了代码重复，降低了维护成本
- 统一的错误处理和日志记录

### 3. 扩展性
- 新增订单类型时，可以直接复用对应的业务服务
- 业务服务的功能增强会自动应用到补单逻辑
- 支持灵活的补单策略配置

## 使用方式

### 1. 自动补单
补单任务会自动运行，使用集成的业务服务进行补单：

```bash
# 启动服务，补单任务自动开始
./lottery_svr
```

### 2. 手动调用
也可以手动调用补单方法：

```go
// 创建补单助手
helper := NewOrderRepairHelper(
    pureLotteryOrderService,
    lotteryDeliveryOrderService, 
    consumeAndAwardOrderService,
    presentOrderService
)

// 执行补单
success := helper.RepairOrderWithBusinessService(ctx, order, yearMonth)
```

## 配置说明

### 1. 服务依赖
确保以下服务正确初始化：
- `PresentDispatcher` - 发奖分发器
- `ConsumeDispatcher` - 消费分发器
- 各种业务处理器 (Handler)

### 2. 数据库连接
确保数据库连接正常，支持分月表查询。

### 3. 日志配置
补单过程会产生详细的日志，确保日志配置正确。

## 监控和调试

### 1. 关键日志
```
使用纯抽奖服务进行补单: order_123456
纯抽奖订单补单成功: order_123456, 有抽奖记录1条
使用抽奖发奖服务进行补单: order_789012
抽奖发奖订单补单成功: order_789012, 状态: 1, 原因: 抽奖记录1条，发奖记录2条都成功
```

### 2. 错误处理
- 业务服务调用失败会记录详细错误日志
- 数据库操作失败会进行重试
- 单个订单补单失败不会影响其他订单

### 3. 性能监控
- 监控补单任务执行时间
- 监控各类型订单的补单成功率
- 监控数据库查询性能

## 扩展指南

### 1. 新增订单类型
1. 创建对应的业务服务
2. 在`OrderRepairHelper`中添加对应的补单方法
3. 在`processOrderByType`中添加新的case
4. 更新服务初始化代码

### 2. 自定义补单逻辑
可以在各个`repair*OrderWithService`方法中添加特定的业务逻辑：

```go
func (h *OrderRepairHelper) repairLotteryOrderWithService(ctx context.Context, order *model.Order, yearMonth string) bool {
    // 添加自定义的前置检查
    if !h.customPreCheck(order) {
        return false
    }
    
    // 执行标准补单逻辑
    // ...
    
    // 添加自定义的后置处理
    h.customPostProcess(order, success)
    
    return success
}
```

## 总结

通过将业务服务集成到补单系统中，我们实现了：

1. **统一的业务逻辑** - 补单和正常业务使用相同的处理逻辑
2. **高度的代码复用** - 避免了重复实现相同的业务规则
3. **良好的维护性** - 业务逻辑变更自动同步到补单逻辑
4. **强大的扩展性** - 支持灵活的补单策略和新订单类型

现在补单系统不仅功能完整，而且与现有业务系统高度集成，确保了逻辑的一致性和可维护性！
