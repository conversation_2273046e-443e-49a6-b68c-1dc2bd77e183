package main

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/common/middleware"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/config"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/service"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/lottery"
	lottery_admin "cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/lottery/admin"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/grpc/interceptor/logging"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/grpc/interceptor/metric"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/grpc/interceptor/recovery"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/karaoke"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/karaoke/transport/grpc"
	kghttp "cnb.tmeoa.com/tme_bmp/tme_bmp_plib/karaoke/transport/http"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log/accesslog"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log/paniclog"
	"flag"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	rpc "google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

func main() {
	flag.Parse()

	c, err := config.Load()
	if err != nil {
		panic(err)
	}

	log.Init(c.Log)
	accesslog.Init(c.AccessLog)
	paniclog.Init(c.PanicLog)

	svc, err := service.New(c)
	if err != nil {
		panic(err)
	}
	g := grpc.NewServer(
		grpc.Address(c.Server.GRPC.Addr),
		grpc.Options(rpc.ChainUnaryInterceptor(
			recovery.UnaryServerInterceptor(),
			logging.UnaryServerInterceptor(),
			metric.UnaryServerInterceptor(),
		)),
	)
	// 注册接口
	lottery.RegisterLotteryApiServer(g.Server, svc)
	lottery_admin.RegisterLotteryAdminApiServer(g.Server, svc)
	reflection.Register(g.Server)
	// 初始化调度器
	h := kghttp.NewServer(kghttp.Address(c.Server.HTTP.Addr))
	h.Handle("/metrics", promhttp.Handler())
	h.Handle("/lottery.LotteryApi/", middleware.CorsMiddleware(lottery.NewHandler(c.Server.GRPC.Addr)))
	h.Handle("/lottery_admin.lottery_admin_api/", middleware.CorsMiddleware(lottery_admin.NewHandler(c.Server.GRPC.Addr)))

	k := karaoke.New(karaoke.Server(g, h))
	if err := k.Run(); err != nil {
		panic(err)
	}
}
