# 订单补单功能完整实现说明

## 概述

已完成基于`github.com/robfig/cron/v3`的订单补单系统，支持根据不同订单类型查询不同的业务表，并实现相应的补单逻辑。

## 订单类型与表关系

### 1. 纯抽奖 (OrderTypeLottery = 1)
**写入表**: `t_order` + `t_lottery`
**补单逻辑**: 
- 有抽奖记录 → 补成成功状态
- 无抽奖记录 → 补成失败状态

### 2. 抽奖发奖 (OrderTypeLotteryDelivery = 2)  
**写入表**: `t_order` + `t_lottery` + `t_present`
**补单逻辑**:
- 有抽奖记录且发奖全部成功 → 补成成功状态
- 其他情况 → 补成失败状态

### 3. 扣抽奖券抽奖发奖 (OrderTypeConsumeLotteryDelivery = 3)
**写入表**: `t_order` + `t_lottery` + `t_present` + `t_consume`
**补单逻辑**:
- 消费成功且抽奖成功且发奖全部成功 → 补成成功状态
- 其他情况 → 补成失败状态

### 4. 发放抽奖券 (OrderTypeDeliveryTicket = 4)
**写入表**: `t_order` + `t_present`
**补单逻辑**:
- 发奖全部成功 → 补成成功状态
- 其他情况 → 补成失败状态

## 关键发现

### 表关联关系
- `t_order.order_id` = `t_lottery.draw_id` (抽奖记录通过draw_id关联)
- `t_order.order_id` = `t_present.order_id` (发奖记录通过order_id关联)
- `t_order.order_id` = `t_consume.order_id` (消费记录通过order_id关联)

## 实现的核心组件

### 1. DAO层扩展

#### 新增方法 - lottery_order.go
```go
SelectPendingOrdersInTimeRange(ctx, yearMonth, startMinutes, endMinutes, limit) ([]*model.Order, error)
```

#### 新增方法 - lottery_record.go
```go
SelectLotteryRecordsByOrderId(ctx, yearMonth, orderId) ([]*model.LotteryRecord, error)
```

#### 修复字段名
- `lottery_consume.go`: `orderId` → `order_id`
- `lottery_present.go`: `orderId` → `order_id`

### 2. 服务层实现

#### OrderRepairHelper - 辅助工具类
- `CheckLotteryRecords()` - 检查抽奖记录
- `CheckPresentRecords()` - 检查发奖记录  
- `CheckConsumeRecords()` - 检查消费记录
- `DetermineOrderStatus()` - 根据业务记录判断订单状态
- `UpdateOrderStatus()` - 更新订单状态

#### OrderRepairJob - 主要定时任务
- `repairLotteryOrder()` - 纯抽奖订单补单
- `repairLotteryDeliveryOrder()` - 抽奖发奖订单补单
- `repairConsumeLotteryDeliveryOrder()` - 扣抽奖券抽奖发奖订单补单
- `repairDeliveryTicketOrder()` - 发放抽奖券订单补单

## 补单流程

### 1. 定时执行
- 每5分钟执行一次 (cron表达式: `0 */5 * * * *`)
- 查询30分钟到5分钟前status=0的订单
- 每次最多处理100条订单

### 2. 按类型分组处理
```go
ordersByType := j.groupOrdersByType(orders)
for orderType, typeOrders := range ordersByType {
    j.processOrdersByType(ctx, orderType, typeOrders, yearMonth)
}
```

### 3. 根据订单类型查询相应表
```go
switch orderType {
case OrderTypeLottery:
    // 只查询抽奖记录
    lotteryRecords := CheckLotteryRecords()
case OrderTypeLotteryDelivery:
    // 查询抽奖记录和发奖记录
    lotteryRecords := CheckLotteryRecords()
    presents := CheckPresentRecords()
case OrderTypeConsumeLotteryDelivery:
    // 查询所有相关记录
    consumes := CheckConsumeRecords()
    lotteryRecords := CheckLotteryRecords()
    presents := CheckPresentRecords()
case OrderTypeDeliveryTicket:
    // 只查询发奖记录
    presents := CheckPresentRecords()
}
```

### 4. 状态判断和更新
```go
result := DetermineOrderStatus(ctx, presents, consumes, lotteryRecords)
success := UpdateOrderStatus(ctx, orderId, yearMonth, result.TargetStatus)
```

## 测试和验证

### 1. SQL测试脚本
- `test_repair_dao_methods.sql` - 验证DAO方法
- `test_order_repair_logic.sql` - 验证补单逻辑

### 2. 监控脚本
- `monitor_repair_job.sh` - 实时监控补单任务

### 3. 关键验证点
- 时间范围查询是否正确
- 各表关联查询是否正确
- 状态判断逻辑是否符合业务规则
- 订单状态更新是否成功

## 性能优化

### 建议的索引
```sql
-- 订单表：支持按状态和创建时间查询
ALTER TABLE t_order_202506 ADD INDEX idx_status_create_time (status, create_time);

-- 发奖表：支持按订单ID查询
ALTER TABLE t_present_202506 ADD INDEX idx_order_id (order_id);

-- 消费表：支持按订单ID查询  
ALTER TABLE t_consume_202506 ADD INDEX idx_order_id (order_id);

-- 抽奖表：支持按draw_id查询
ALTER TABLE t_lottery_202506 ADD INDEX idx_draw_id (draw_id);
```

## 日志监控

### 关键日志信息
```
订单补单定时任务启动成功 (每5分钟执行一次)
开始执行订单补单任务
开始处理年月202506的订单补单
年月202506找到5个需要补单的订单
开始处理订单类型: 抽奖发奖 (lottery_delivery), 订单数量: 3
处理抽奖发奖订单补单逻辑: order_123456
订单order_123456有抽奖记录1条，有发奖记录2条都成功，补成成功状态
抽奖发奖订单补单成功: order_123456, 状态: 1, 原因: 抽奖记录1条，发奖记录2条都成功
订单类型抽奖发奖处理完成, 成功: 2, 失败: 1
年月202506订单补单完成, 成功: 4, 失败: 1
订单补单任务执行完成
```

## 部署和启动

### 1. 编译项目
```bash
go build -o lottery_svr
```

### 2. 启动服务
```bash
./lottery_svr
```

### 3. 验证启动
```bash
# 查看补单任务启动日志
tail -f logs/slog.log | grep "订单补单"
```

## 注意事项

### 1. 数据安全
- 只更新status=0的订单
- 使用事务确保数据一致性
- 详细的错误日志记录

### 2. 性能考虑
- 批量处理限制（每次100条）
- 时间范围限制（30分钟到5分钟前）
- 索引优化查询性能

### 3. 业务逻辑
- 严格按照订单类型查询对应的表
- 状态判断逻辑符合业务规则
- 支持跨月查询（当前月和上个月）

## 扩展性

### 1. 新增订单类型
1. 在`constant/order_type.go`中定义新类型
2. 在`processOrdersByType`中添加新的case
3. 实现对应的补单方法
4. 更新状态判断逻辑

### 2. 自定义补单规则
可以在各个`repair*Order`方法中添加特定的业务逻辑，如：
- 特殊的状态判断条件
- 额外的业务验证
- 自定义的错误处理

## 总结

✅ **完成的功能**:
1. 基于cron的定时任务调度
2. 支持4种订单类型的差异化补单逻辑
3. 完整的DAO层方法实现
4. 详细的日志记录和监控
5. 完善的测试和验证工具

✅ **关键特性**:
1. 自动化执行，无需人工干预
2. 智能的状态判断逻辑
3. 高性能的批量处理
4. 完善的错误处理机制
5. 灵活的扩展性设计

现在订单补单功能已经完整实现，可以投入生产使用！
