# 订单补单功能说明

## 概述

订单补单功能是一个简单的定时任务，用于自动处理lottery_svr项目中因调用失败而导致订单状态未能正确更新的问题。当订单的业务逻辑执行成功但状态更新失败时，订单会长时间停留在初始化状态（status=0），补单程序会自动检测这些订单并将其状态更新为正确的最终状态。

## 功能特性

1. **标准cron调度**：使用`github.com/robfig/cron/v3`库，支持标准cron表达式
2. **自动定时执行**：默认每5分钟自动执行一次补单检查（cron表达式：`0 */5 * * * *`）
3. **智能时间范围**：只处理30分钟到5分钟前的订单，避免处理正在进行中的订单
4. **智能状态判断**：根据发奖记录和消费记录判断订单应该补成成功还是失败状态
5. **批量处理**：每次最多处理100条订单，避免对数据库造成过大压力
6. **跨月支持**：同时处理当前月份和上个月份的订单表
7. **详细日志**：记录补单过程的详细信息，便于监控和排查
8. **灵活配置**：支持自定义cron表达式，可根据需要调整执行频率

## 补单逻辑

### 订单筛选条件
- 订单状态为0（初始化状态）
- 订单创建时间在30分钟到5分钟前的时间范围内
- 每次最多处理100条订单

### 状态判断逻辑
1. **查询发奖记录**：检查对应的`t_present_*`表是否有该订单的发奖记录
2. **查询消费记录**：检查对应的`t_consume_*`表是否有该订单的消费记录
3. **状态决策**：
   - 如果有发奖记录：补成成功状态（status=1）
   - 如果有消费记录但无发奖记录：补成失败状态（status=2）
   - 如果既无发奖记录也无消费记录：补成失败状态（status=2）

### 状态更新
- 成功状态：调用`dao.LotteryOrderDao.UpdateWithSuccess()`
- 失败状态：调用`dao.LotteryOrderDao.UpdateWithFail()`

## 定时任务配置

- **调度器**：`github.com/robfig/cron/v3`
- **默认cron表达式**：`0 */5 * * * *` (每5分钟的第0秒执行)
- **执行频率**：每5分钟执行一次
- **时间范围**：30分钟到5分钟前
- **批次大小**：每次最多100条订单
- **处理表**：当前月份和上个月份的订单表

### Cron表达式说明
- 格式：`秒 分 时 日 月 周`
- `0 */5 * * * *`：每5分钟的第0秒执行
- `0 0 */1 * * *`：每小时执行一次
- `0 30 */2 * * *`：每2小时的30分执行一次
- `0 0 9-17 * * 1-5`：工作日9点到17点每小时执行

## 日志监控

### 关键日志信息
```
订单补单定时任务启动成功
开始执行订单补单任务
开始处理年月202506的订单补单
年月202506找到5个需要补单的订单
开始补单订单: order_123456
订单order_123456有发奖记录3条，补成成功状态
订单补单成功: order_123456, 原状态: 0, 新状态: 1
年月202506订单补单完成, 成功: 4, 失败: 1
订单补单任务执行完成
```

### 错误日志
```
查询待补单订单失败, yearMonth: 202506, error: xxx
查询发奖记录失败, orderId: order_123456, error: xxx
更新订单状态失败, orderId: order_123456, targetStatus: 1, error: xxx
订单补单失败: order_123456
```

## 监控建议

### 1. 日志监控
```bash
# 查看补单相关日志
tail -f /path/to/log/file | grep "订单补单"

# 查看补单成功数量
grep "订单补单完成" /path/to/log/file | tail -10

# 查看补单错误
grep "补单失败\|更新订单状态失败" /path/to/log/file
```

### 2. 数据库监控
```sql
-- 查看当前月份待补单订单数量
SELECT COUNT(*) FROM t_order_202506 
WHERE status = 0 
AND create_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE_SUB(NOW(), INTERVAL 5 MINUTE);

-- 查看最近补单的订单
SELECT order_id, status, create_time, update_time 
FROM t_order_202506 
WHERE update_time > DATE_SUB(NOW(), INTERVAL 10 MINUTE)
AND status IN (1, 2)
ORDER BY update_time DESC;
```

### 3. 性能监控
- 监控补单任务执行时间
- 监控数据库查询性能
- 监控补单成功率

## 注意事项

1. **时间范围设计**：
   - 不处理5分钟内的订单，避免干扰正在进行的业务流程
   - 不处理30分钟前的订单，避免处理过于陈旧的数据

2. **批量处理限制**：
   - 每次最多处理100条订单，避免长时间占用数据库资源
   - 如果有大量待补单订单，会在后续的定时任务中继续处理

3. **数据安全**：
   - 只更新status=0的订单，避免误操作已处理的订单
   - 使用事务确保数据一致性

4. **性能考虑**：
   - 定时任务在后台异步执行，不影响主业务流程
   - 查询使用索引优化，减少数据库压力

## 故障排查

### 常见问题

1. **补单任务未启动**
   - 检查服务启动日志是否有"订单补单定时任务启动成功"
   - 检查是否有错误导致任务启动失败

2. **订单状态未更新**
   - 检查订单是否在时间范围内（30分钟到5分钟前）
   - 检查数据库连接是否正常
   - 查看具体的错误日志

3. **补单逻辑错误**
   - 检查发奖记录和消费记录查询是否正常
   - 确认订单表和相关表的数据一致性

### 手动排查步骤

1. **检查待补单订单**：
```sql
SELECT order_id, status, create_time 
FROM t_order_202506 
WHERE status = 0 
AND create_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE_SUB(NOW(), INTERVAL 5 MINUTE)
LIMIT 10;
```

2. **检查发奖记录**：
```sql
SELECT * FROM t_present_202506 WHERE order_id = 'your_order_id';
```

3. **检查消费记录**：
```sql
SELECT * FROM t_consume_202506 WHERE order_id = 'your_order_id';
```

## 代码结构

- **主要文件**：`app/lottery_svr/internal/service/order_repair_job.go`
- **DAO扩展**：`app/lottery_svr/internal/dao/lottery_order.go`中的`SelectPendingOrdersInTimeRange`方法
- **集成位置**：`app/lottery_svr/internal/service/service.go`中启动定时任务
- **依赖库**：`github.com/robfig/cron/v3` - 标准的Go cron调度器

## 自定义配置

如果需要修改执行频率，可以在代码中使用自定义cron表达式：

```go
// 使用自定义cron表达式启动（例如每10分钟执行一次）
err := orderRepairJob.StartWithCustomCron("0 */10 * * * *")
if err != nil {
    log.Errorf("启动补单任务失败: %v", err)
}
```

常用的cron表达式示例：
- `0 */1 * * * *`：每分钟执行
- `0 */10 * * * *`：每10分钟执行
- `0 0 */1 * * *`：每小时执行
- `0 0 2 * * *`：每天凌晨2点执行

## 版本信息

- **版本**：v2.0.0
- **功能**：基于cron调度器的订单状态补单定时任务
- **特点**：标准化、灵活配置、自动化、无需人工干预
- **升级**：使用`github.com/robfig/cron/v3`替代原生ticker实现
