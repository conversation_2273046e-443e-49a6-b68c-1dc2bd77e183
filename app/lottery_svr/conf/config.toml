[Server.HTTP]
Addr = ":8080"

[Server.GRPC]
Addr = ":9090"

[Client.Grpc.Metadata]
app-name="kg"

[Log]
Level = "info"
Output = "console"
Filename = "../log/slog.log"
MaxSize = 128
MaxAge = 31
MaxBackups = 10
DevMode = true

[PanicLog]
Level = "debug"
Output = "file"
Filename = "../log/panic.log"
MaxSize = 128
MaxAge = 30
MaxBackups = 10
Compress = false
DevMode = false
DisableStacktrace = false

[AccessLog]
Level = "info"
Output = "file"
Filename = "../log/access.log"
MaxSize = 128
MaxAge = 30
MaxBackups = 10
Compress = false
DevMode = false
DisableStacktrace = false

[MysqlConfig.bmpLottery]
Mod = 0
Cmd = 0
Ip = "**********"
Port = 3306
Username = "fanxing"
Password = "kugou2014"
Database = "d_bmp_lottery"
MaxIdleConns = 32
ConnMaxIdleTime = "3s"
DialTimeout = "3s"
InterpolateParams = true
ServerName = "lottery_svr"
LocalTime = true

[MysqlConfig.bmpAdmin]
Mod = 0
Cmd = 0
Ip = "**********"
Port = 3306
Username = "fanxing"
Password = "kugou2014"
Database = "d_bmp_admin"
MaxIdleConns = 32
ConnMaxIdleTime = "3s"
DialTimeout = "3s"
InterpolateParams = true
ServerName = "lottery_svr"
LocalTime = true

[RedisConfig]
#Mod = 192000065
#Cmd = 67414
#Password = "202409005:activity_kg_test165755"
ServerName = "kg.game.lottery_svr"
Ip = "127.0.0.1"
Port = 6379

[TppRights]
AppKey =  "0710a95bc2ea31e39aa86d5203335cfc"
AppSecret = "94b3e874a402074601168577cdc18657"
ApplyRightsUrl = "https://rights.test.tmeoa.com/apply/systemRole?pkey=lottery-admin"
CheckOpRightsUri = "http://api.rights.test.tmeoa.com/checkOpRights"
HrcStaffDetailUri = "http://agw-test.tmeoa.com/hrcservice/api/staff-detail"
HrcStaffSearchUri = "http://agw-test.tmeoa.com/hrcservice/api/staff-search"
TimeOutMills = 3000
