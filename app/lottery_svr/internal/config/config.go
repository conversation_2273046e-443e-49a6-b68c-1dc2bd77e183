package config

import (
	kgmysql "cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/common/mysql"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/adapter_unified_assets/adapter_unified_assets"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/gopen"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/reward_sender"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/config"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/karaoke"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log/accesslog"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log/paniclog"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/redis"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/toml"
	"flag"
)

var (
	configPath string
)

func init() {
	flag.StringVar(&configPath, "config", "../conf/config.toml", "config file path")
}

// Config is the configuration for the service.
type Config struct {
	Server               *karaoke.ServerConfig
	Log                  *log.Config
	AccessLog            *accesslog.Config
	PanicLog             *paniclog.Config
	MysqlConfig          map[string]*kgmysql.Config
	RedisConfig          *redis.Config
	RewardSender         *reward_sender.ClientConfig
	Gopen                *gopen.ClientConfig
	TppRights            *TppRightsConfig // TME TPP 网关
	AdapterUnifiedAssets *adapter_unified_assets.ClientConfig
}

type TppRightsConfig struct {
	AppKey            string
	AppSecret         string
	ApplyRightsUrl    string // ApplyRightsUrl is the URL for applying rights.
	CheckOpRightsUri  string // TppServerAddr is the address of the TME TPP gateway server.
	HrcStaffDetailUri string // HrcStaffDetailUri is the URL for checking staff.
	HrcStaffSearchUri string // HrcStaffSearchUri is the URL for searching staff.
	TimeOutMills      int
}

type PulsarConfig struct {
	ConsumeService string
}

// Load loads the configuration.
func Load() (*Config, error) {
	c := &Config{}
	_, err := toml.DecodeFile(configPath, c)
	return c, err
}

func AppName() string {
	// 设置 app-name
	if config.GlobalConfig.Client.GRPC.Metadata == nil {
		config.GlobalConfig.Client.GRPC.Metadata = make(map[string]string)
	}
	return config.GlobalConfig.Client.GRPC.Metadata["app-name"]
}
