package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/config"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/errs"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/utils"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/adapter_common"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/adapter_unified_assets/adapter_unified_assets"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"encoding/json"
	"github.com/spf13/cast"
	"time"
)

//全民福利礼包发奖

type PlatWelfarePresentHandler struct {
	AdapterUnifiedAssetClient adapter_unified_assets.AdapterUnifiedAssetClient
}

func NewPlatWelfarePresentHandler(adapterUnifiedAssetClient adapter_unified_assets.AdapterUnifiedAssetClient) *PlatWelfarePresentHandler {
	return &PlatWelfarePresentHandler{
		AdapterUnifiedAssetClient: adapterUnifiedAssetClient,
	}
}

func (h *PlatWelfarePresentHandler) SupportType() string {
	return constant.PrizeTypeReward.Code()
}

func (h *PlatWelfarePresentHandler) Handle(ctx context.Context, lottery *model.LotteryActivity, present *model.Present) *dto.Result[bool] {
	// 解析 MapExt 字段
	var mapExt map[string]string
	if utils.IsNotBlank(present.MapExt) {
		if err := json.Unmarshal([]byte(present.MapExt), &mapExt); err != nil {
			log.Errorf("调用[SendGiftPackage]平台服务发放接口，MapExt 解析失败 | presentID:%s | data:%s | error:%v",
				present.PresentId, present.MapExt, err)
			return dto.Fail[bool](errs.ParamError.Code, "扩展字段格式错误")
		}
	}
	// 发放福利礼包
	sendGiftPackageReq := &adapter_unified_assets.SendGiftPackageReq{
		GameMiddleInfo: &adapter_common.GameMiddleInfo{
			GameAppId:  present.AppId,
			GameOpenId: present.OpenId,
			Uid:        cast.ToString(present.PlatUid),
		},
		GiftPackageId: present.PrizeId,
		Num:           present.PrizeNum,
		OrderId:       present.PresentId,
		Program:       "lottery_svr",
		Reason:        "lottery_svr user award",
		SendTs:        time.Now().Unix(),
		MapExt:        map[string]string{},
		ExtensionId:   adapter_unified_assets.SendExtensionId_SendExtensionIdBmpLotterySvr,
		Indentifiers:  config.AppName() + "_lottery_svr",
	}
	rsp, err := h.AdapterUnifiedAssetClient.SendGiftPackage(ctx, sendGiftPackageReq)
	log.Warnf("调用[SendGiftPackage]平台服务发放接口，发放结果: req:%+v rsp:%+v err:%+v", sendGiftPackageReq, rsp, err)
	if err != nil {
		log.Warnf("调用[SendGiftPackage]平台服务发放接口，发放失败。req:%+v rsp:%+v err:%+v", sendGiftPackageReq, rsp, err)
		return dto.Fail[bool](errs.RPCError.Code, errs.RPCError.Msg).WithError(err).
			WithMsg("调用[SendGiftPackage]平台服务发放接口，发放失败: %v", err)
	}
	log.Warnf("调用[SendGiftPackage]平台服务发放接口，发放成功。req:%+v rsp:%+v err:%+v", sendGiftPackageReq, rsp, err)
	// 更新订单状态
	result := UpdatePresentStatus(ctx, present)
	log.Warnf("调用[SendGiftPackage]平台服务发放接口，更新订单状态结果: %v", result)
	return result
}
