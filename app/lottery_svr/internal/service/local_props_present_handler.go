package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"context"
)

type LocalPropsPresentHandler struct {
}

func NewLocalPropsPresentHandler() *LocalPropsPresentHandler {
	return &LocalPropsPresentHandler{}
}

func (h *LocalPropsPresentHandler) SupportType() string {
	return constant.PrizeTypeLocalProps.Code()
}

func (h *LocalPropsPresentHandler) Handle(ctx context.Context, lottery *model.LotteryActivity, present *model.Present) *dto.Result[bool] {
	return dto.Success(true)
}
