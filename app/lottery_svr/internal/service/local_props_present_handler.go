package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"context"
)

type NothingPresentHandler struct {
}

func NewNothingPresentHandler() *NothingPresentHandler {
	return &NothingPresentHandler{}
}

func (h *NothingPresentHandler) SupportType() string {
	return constant.PrizeTypeNothing.Code()
}

func (h *NothingPresentHandler) Handle(ctx context.Context, lottery *model.LotteryActivity, present *model.Present) *dto.Result[bool] {
	return dto.Success(true)
}
