package service

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"strconv"

	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/errs"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/utils"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	plog "cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"

	"github.com/dromara/carbon/v2"
)

type PresentOrderService struct {
	dispatcher *PresentDispatcher // 添加调度器依赖
}

func NewPresentOrderService(dispatcher *PresentDispatcher) *PresentOrderService {
	return &PresentOrderService{
		dispatcher: dispatcher,
	}
}

// 实现业务逻辑

func (h *PresentOrderService) DoBiz(ctx context.Context, req *dto.LotteryOrderReqDTO) *dto.Result[*dto.LotteryOrderInfoDTO] {
	current := time.Now()
	orderType := h.GetOrderType()
	//构建主订单 & 发奖订单
	order := toOrder(req, orderType, current)

	presents := toPresentList(req, current)

	insertResult := h.insertWithPresent(order, presents)

	if !insertResult.IsSuccess() {
		code := insertResult.Code
		msg := insertResult.Msg
		plog.Errorf("订单插入失败 code=%d, msg=%s, error=%v", code, msg, insertResult.Err)
		return dto.Fail[*dto.LotteryOrderInfoDTO](code, msg).WithError(insertResult.Err)
	}
	var (
		allSuccess = true
		lastErr    error
	)
	for _, present := range presents {
		//根据prizeType调用不同的assetPresentHandler
		handler, ok := h.dispatcher.GetHandler(present.PrizeType)
		if !ok {
			// 处理未找到情况
			plog.Errorf("未找到资产处理器 type=%s", present.PrizeType)
			allSuccess = false
			continue
		}
		// 执行资产发放
		result := handler.Handle(ctx, req.LotteryActivity, present)
		if !result.IsSuccess() {
			allSuccess = false
			log.Errorf("资产发放失败 presentID=%s, error=%v",
				present.PresentId, result.Err)
			allSuccess = false

		}
	}
	if !allSuccess {
		plog.Errorf("部分奖品发放失败 error=%v", lastErr)
		return dto.Fail[*dto.LotteryOrderInfoDTO](
			errs.AssetPresentFailed.Code,
			"部分奖品发放失败",
		).WithError(lastErr)
	}
	// 更新订单状态
	//更新订单状态为成功
	lo, _ := time.LoadLocation(shanghaiLocName)
	localTime := time.Unix(req.BizTime, 0).In(lo)
	// 格式化为 yyyyMM
	monthStr := localTime.Format(monthFormat)
	effectRow, err := dao.LotteryOrderDao.UpdateWithSuccess(ctx, monthStr, req.OrderID)
	if err != nil {
		plog.Errorf("订单状态更新失败 error=%v", err)
		return dto.Fail[*dto.LotteryOrderInfoDTO](
			errs.OrderStatusError.Code,
			"订单状态更新失败",
		).WithError(err)
	}

	if effectRow != 1 {
		plog.Errorf("订单状态更新异常，影响行数: %d", effectRow)
		return dto.Fail[*dto.LotteryOrderInfoDTO](
			errs.OperateConflict.Code,
			fmt.Sprintf("订单状态更新异常，影响行数: %d", effectRow),
		)
	}

	plog.Infof("订单处理成功 orderID=%s", order.OrderId)
	// 返回成功结果
	return dto.Success(&dto.LotteryOrderInfoDTO{
		OrderID:    order.OrderId,
		Status:     constant.Success.Status,
		CreateTime: order.CreateTime,
	})

}

func toPresentList(req *dto.LotteryOrderReqDTO, current time.Time) []*model.Present {
	// 构建奖品列表
	var presents []*model.Present
	for _, p := range req.LotteryPresentReqDTO {
		presents = append(presents, &model.Present{
			OrderId:      req.OrderID,
			PresentId:    strconv.FormatInt(utils.GenerateID(), 10),
			AccessId:     req.AccessId,
			AppId:        req.AppID,
			OpenId:       req.OpenId,
			PlatUid:      cast.ToUint64(req.PlatUid),
			PrizeId:      p.PrizeID,
			PrizeType:    p.PrizeType,
			PrizeName:    p.PrizeName,
			PrizeNum:     p.PrizeNum,
			PrizePic:     p.PrizePic,
			BizTimestamp: req.BizTime,
			ExpireTime:   p.ExpireTime,
			CreateTime:   current,
			UpdateTime:   current,
			MapExt:       utils.ToJSONIgnoreError(req.MapExt),
		})
	}
	return presents
}

func toOrder(req *dto.LotteryOrderReqDTO, orderType int32, current time.Time) *model.Order {
	order := model.Order{
		OrderId:      req.OrderID,
		PlatUid:      cast.ToUint64(req.PlatUid),
		OpenId:       req.OpenId,
		OrderType:    orderType,
		AccessId:     req.AccessId,
		AppId:        req.AppID,
		BizTimestamp: req.BizTime,
		Status:       0,
		CreateTime:   current,
		UpdateTime:   current,
		MapExt:       utils.ToJSONIgnoreError(req.MapExt),
	}
	return &order
}

func (h *PresentOrderService) GetOrderType() int32 {
	return int32(constant.OrderTypeDeliveryTicket)
}
