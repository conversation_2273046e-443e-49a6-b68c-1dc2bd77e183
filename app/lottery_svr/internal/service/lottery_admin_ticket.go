package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/errs"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/utils"
	lottery_admin "cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/lottery/admin"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"github.com/dromara/carbon/v2"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"time"
)

// validateExpireTime 验证过期时间
// 确保时间戳是00:00:00的时间，且不能比今天早
func validateExpireTime(expireSecs int64) (int64, error) {
	if expireSecs <= 0 {
		return 0, nil // 0表示永不过期，允许
	}

	// 将时间戳转换为carbon时间对象
	expireTime := carbon.CreateFromTimestamp(expireSecs)
	if expireTime.IsZero() {
		return 0, errs.New("过期时间格式错误")
	}

	// 获取今天的00:00:00时间
	today := carbon.Now().StartOfDay()

	// 将过期时间调整为当天的00:00:00
	normalizedExpireTime := expireTime.StartOfDay()

	// 检查是否比今天早
	if normalizedExpireTime.Lt(today) {
		return 0, errs.New("过期时间不能早于今天")
	}

	// 返回标准化后的时间戳（00:00:00）
	return normalizedExpireTime.Timestamp(), nil
}

func (s *Service) CreateTicket(ctx context.Context, req *lottery_admin.CreateTicketReq) (*lottery_admin.CreateTicketRsp, error) {
	// 解析公共参数
	commonParam, err := s.GetCommonParam(ctx)
	if err != nil {
		log.Warnf("创建抽奖券配置，解析公共参数失败。error(%v)", err)
		return errs.Response[lottery_admin.CreateTicketRsp](errs.CommonParamError), nil
	}
	// 检查接口权限
	haveRight, err := s.CheckMethodRight(ctx)
	if !haveRight {
		log.Warnf("创建抽奖券配置，缺少创建权限。haveRight:%v, err:%v", haveRight, err)
		return errs.Response[lottery_admin.CreateTicketRsp](errs.LotteryRightError), nil
	}
	// 检查抽奖活动
	lotteryId := req.ActivityId
	activity, err := dao.LotteryActivityDao.GetLotteryActivityById(ctx, lotteryId)
	if err != nil || activity == nil {
		log.Warnf("创建抽奖券配置，检查活动不存在。lotteryId:%v, err:%v", lotteryId, err)
		return errs.Response[lottery_admin.CreateTicketRsp](errs.ActivityNotFound), nil
	}
	// 检查抽奖管理员
	hasPermission, err := s.checkStaffPermission(activity, commonParam.UserInfo.Ename)
	if err != nil || !hasPermission {
		log.Warnf("创建抽奖券配置，缺少抽奖管理员权限。req:%+v, err:%v, hasPermission:%v", req, err, hasPermission)
		return errs.Response[lottery_admin.CreateTicketRsp](errs.HasNoPermission), nil
	}
	// 验证过期时间
	validatedExpireSecs, err := validateExpireTime(req.ExpireSecs)
	if err != nil {
		log.Warnf("创建抽奖券配置，过期时间验证失败。expireSecs:%v, err:%v", req.ExpireSecs, err)
		return errs.Response[lottery_admin.CreateTicketRsp](errs.ParamError), nil
	}

	// 检查抽奖券ID
	ticketId := req.TicketId
	if ticketId <= 0 {
		ticketId = s.SnowflakeToInt32()
	}
	ticket := &model.LotteryTicket{
		TicketId:            ticketId,
		LotteryId:           lotteryId,
		PlatCode:            activity.PlatCode,
		TicketName:          req.TicketName,
		TotalNum:            req.TotalNum,
		UsedNum:             0,
		ExpireSecs:          validatedExpireSecs, // 使用验证后的过期时间
		SingleDrawTicketNum: req.SingleDrawTicketNum,
		CreateTime:          time.Now(),
		UpdateTime:          time.Now(),
	}
	rowsAffected, err := dao.LotteryTicketDao.InsertLotteryTicketIgnore(ctx, ticket)
	if err != nil {
		log.Warnf("创建抽奖券配置，保存异常。ticket:%+v, err:%v", ticket, err)
		return errs.Response[lottery_admin.CreateTicketRsp](errs.Failure), nil
	}
	if rowsAffected < 1 {
		log.Warnf("创建抽奖券配置，保存失败, ticket:%+v, rowsAffected:%v", ticket, rowsAffected)
		return errs.Response[lottery_admin.CreateTicketRsp](errs.LotteryIdDuplicate), nil
	}
	rsp := &lottery_admin.CreateTicketRsp{
		Code:     0,
		Msg:      "操作成功",
		TicketId: ticket.TicketId,
	}
	return rsp, nil
}

func (s *Service) QueryTicket(ctx context.Context, req *lottery_admin.QueryTicketReq) (*lottery_admin.QueryTicketRsp, error) {
	commonParam, err := s.GetCommonParam(ctx)
	if err != nil {
		log.Warnf("查询抽奖券列表，解析公共参数失败。error(%v)", err)
		return errs.Response[lottery_admin.QueryTicketRsp](errs.CommonParamError), nil
	}
	log.Warnf("查询抽奖券列表，公共参数。commonParam:%+v, req:%+v", utils.ToJSONIgnoreError(commonParam), req)
	pagination, err := model.NewPagination(req.Page, req.PageSize)
	if err != nil {
		log.Warnf("查询抽奖券列表，分页参数错误。page:%v, pageSize:%v, err:%v", req.Page, req.PageSize, err)
		return errs.Response[lottery_admin.QueryTicketRsp](errs.Success), nil
	}
	tickets, totalNum, err := dao.LotteryTicketDao.QueryLotteryTickets(ctx, req.PlatCode, req.ActivityId, req.TicketId, req.TicketName, pagination)
	if err != nil {
		log.Warnf("查询抽奖券列表，查询抽奖券失败。req:%+v, err:%v", req, err)
		return errs.Response[lottery_admin.QueryTicketRsp](errs.Failure), nil
	}
	ticketVos := lo.Map(tickets, func(item *model.LotteryTicket, index int) *lottery_admin.Ticket {
		return &lottery_admin.Ticket{
			TicketId:            item.TicketId,
			ActivityId:          item.LotteryId,
			TicketName:          item.TicketName,
			TicketDesc:          item.TicketDesc,
			TotalNum:            item.TotalNum,
			UsedNum:             item.UsedNum,
			ExpireSecs:          item.ExpireSecs,
			SingleDrawTicketNum: item.SingleDrawTicketNum,
			CreateTime:          cast.ToString(item.CreateTime.Unix()),
			UpdateTime:          cast.ToString(item.UpdateTime.Unix()),
		}
	})
	log.Infof("查询抽奖券列表，查询成功。req:%+v, tickets:%+v", req, tickets)
	rsp := &lottery_admin.QueryTicketRsp{
		Code:      0,
		Msg:       "操作成功",
		Page:      pagination.Page,
		PageSize:  pagination.PageSize,
		Total:     totalNum,
		TotalPage: (int32(totalNum) + pagination.PageSize - 1) / pagination.PageSize,
		Tickets:   ticketVos,
	}
	return rsp, nil
}

func (s *Service) ModifyTicket(ctx context.Context, req *lottery_admin.ModifyTicketReq) (*lottery_admin.ModifyTicketRsp, error) {
	// 解析公共参数
	commonParam, err := s.GetCommonParam(ctx)
	if err != nil {
		log.Warnf("修改抽奖券配置，解析公共参数失败。error(%v)", err)
		return errs.Response[lottery_admin.ModifyTicketRsp](errs.CommonParamError), nil
	}
	ticketId := cast.ToInt32(req.TicketId)
	// 获取抽奖券配置
	ticket, err := dao.LotteryTicketDao.GetLotteryTicketByTicketId(ctx, ticketId)
	if err != nil || ticket == nil {
		log.Warnf("修改抽奖券配置，抽奖券不存在。ticketId:%v, err:%v", ticketId, err)
		return errs.Response[lottery_admin.ModifyTicketRsp](errs.TicketNotFound), nil
	}
	// 检查抽奖活动
	activityId := ticket.LotteryId
	activity, err := dao.LotteryActivityDao.GetLotteryActivityById(ctx, activityId)
	if err != nil || activity == nil {
		log.Warnf("修改抽奖券配置，检查活动不存在。activityId:%v, err:%v", activityId, err)
		return errs.Response[lottery_admin.ModifyTicketRsp](errs.ActivityNotFound), nil
	}
	// 检查抽奖管理员
	hasPermission, err := s.checkStaffPermission(activity, commonParam.UserInfo.Ename)
	if err != nil || !hasPermission {
		log.Warnf("修改抽奖券配置，无权限修改抽奖配置。error(%v)", err)
		return errs.Response[lottery_admin.ModifyTicketRsp](errs.HasNoPermission), nil
	}

	// 验证过期时间
	validatedExpireSecs, err := validateExpireTime(req.ExpireSecs)
	if err != nil {
		log.Warnf("修改抽奖券配置，过期时间验证失败。expireSecs:%v, err:%v", req.ExpireSecs, err)
		return errs.Response[lottery_admin.ModifyTicketRsp](errs.ParamError), nil
	}
	update := &model.LotteryTicket{
		TicketId:            ticket.TicketId,
		TicketName:          req.TicketName,
		TicketDesc:          req.TicketDesc,
		TotalNum:            req.TotalNum,
		ExpireSecs:          validatedExpireSecs, // 使用验证后的过期时间
		SingleDrawTicketNum: req.SingleDrawTicketNum,
		CreateTime:          time.Now(),
		UpdateTime:          time.Now(),
	}
	rowsAffected, err := dao.LotteryTicketDao.UpdateLotteryTicket(ctx, update)
	if err != nil {
		log.Warnf("修改抽奖券配置，修改抽奖券失败。ticket:%+v, err:%v", update, err)
		return errs.Response[lottery_admin.ModifyTicketRsp](errs.Failure), nil
	}
	if rowsAffected < 1 {
		log.Warnf("修改抽奖券配置，修改抽奖券失败。ticket:%+v, rowsAffected:%v", update, rowsAffected)
		return errs.Response[lottery_admin.ModifyTicketRsp](errs.TicketIdDuplicate), nil
	}
	log.Infof("修改抽奖券配置，修改抽奖券成功。ticket:%+v", update)
	return errs.Response[lottery_admin.ModifyTicketRsp](errs.Success), nil
}
