package service

import (
	"context"

	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
)

type AssetConsumeHandler interface {
	Handle(ctx context.Context, lottery *model.LotteryActivity, consume *model.Consume) *dto.Result[bool]
	SupportType() string
	refund(ctx context.Context, consume *model.RefundOrder) *dto.Result[bool]
}

type ConsumeDispatcher struct {
	handlers map[string]AssetConsumeHandler
}

func NewConsumeDispatcher() *ConsumeDispatcher {
	return &ConsumeDispatcher{
		handlers: make(map[string]AssetConsumeHandler),
	}
}

// 注册处理器

func (d *ConsumeDispatcher) Register(h AssetConsumeHandler) {
	d.handlers[h.SupportType()] = h
}

func (d *ConsumeDispatcher) GetHandler(assetType string) (AssetConsumeHandler, bool) {
	handler, ok := d.handlers[assetType]
	return handler, ok
}
