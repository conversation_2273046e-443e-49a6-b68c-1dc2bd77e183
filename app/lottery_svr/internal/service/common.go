package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/utils"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/gopen"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/reward_sender"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"hash/crc32"
	"io"
	"net/http"
	"strings"
	"time"
)

func (s *Service) GenGlobalId() int64 {
	return utils.GenerateID()
}

func (s *Service) SnowflakeToInt32() int32 {
	snowflakeId := utils.GenerateID()
	bytes := []byte(fmt.Sprintf("%d", snowflakeId)) // 将ID转为字符串再哈希
	checksum := crc32.ChecksumIEEE(bytes)
	return int32(checksum & 0x7FFFFFFF) // 保留低31位，确保为正数
}

func (s *Service) Openid2PlatUid(ctx context.Context, appId string, openId string) (uint64, error) {
	log.Warnf("开始请求[Openid2PlatUid]，请求参数。appId: %v, openId: %v", appId, openId)
	rsp, err := s.gOpenServiceClient.Openid2PlatUid(ctx, &gopen.Openid2PlatUidReq{
		StrAppID:  appId,
		StrOpenid: openId,
	})
	if err != nil {
		log.Warnf("开始请求[Openid2PlatUid]，查询失败。appId: %v, openId: %v, err:%+v", appId, openId, err)
		return 0, err
	}
	log.Warnf("开始请求[Openid2PlatUid]，转换成功。appId: %v, openId: %v, platUid: %v", appId, openId, rsp.LUid)
	return rsp.LUid, nil
}

func (s *Service) GetRewardInfo(rewardIds []int64) (*reward_sender.GetRewardInfoRsp, error) {
	log.Warnf("调用游戏中台GetRewardInfo接口，请求参数。rewardIds: %+v", rewardIds)
	req := &reward_sender.GetRewardInfoReq{
		RewardIds: rewardIds,
	}
	rsp, err := s.rewardSenderClient.GetRewardInfo(context.Background(), req)
	if err != nil {
		log.Warnf("调用游戏中台GetRewardInfo接口，查询失败。req:%+v rsp:%+v err:%+v", req, rsp, err)
		return nil, err
	}
	log.Warnf("调用游戏中台GetRewardInfo接口，查询成功。req:%+v rsp:%+v err:%+v", req, rsp, err)
	return rsp, nil
}

func (s *Service) GetCommonParam(ctx context.Context) (*model.CommonParam, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		// 若没有元数据，可根据需求处理
		log.Errorf("no header%v", md)
		return nil, errors.New("invalid grpc header")
	}
	// 从元数据中获取 x-Gopen-Id 头部信息
	tokens := md.Get("x-token")
	token, exist := lo.First(tokens)
	if !exist || utils.IsBlank(token) {
		log.Errorf("x-token不存在。md:%+v", md)
		return nil, errors.New("x-token不存在")
	}
	timestamps := md.Get("x-timestamp")
	timestamp, exist := lo.First(timestamps)
	if !exist || utils.IsBlank(timestamp) {
		log.Errorf("x-timestamp不存在。md:%+v", md)
		return nil, errors.New("x-timestamp不存在")
	}
	userInfo, err := utils.DecodeToken(token, timestamp, s.tppRightsClient.AppSecret)
	if err != nil {
		log.Errorf("DecodeToken失败。md:%+v", md)
		return nil, errors.New("DecodeToken失败")
	}
	commonParam := model.CommonParam{
		Token:     token,
		Timestamp: timestamp,
		UserInfo:  userInfo,
	}
	return &commonParam, nil
}

//curl --location --request POST 'http://api.rights.test.tmeoa.com/checkOpRights' \
//--header 'x-appkey: 0710a95bc2ea31e39aa86d5203335cfc' \
//--header 'x-timestamp: 1747910983' \
//--header 'x-signature: 7a09bd2250fa49436972b1b8aded0f0fce826127' \
//--header 'Content-Type: application/json' \
//--data-raw '{"userId":"leonguo", "opCodes": ["lottery_activity_priviledge", "test_priviledge"]}'

func (s *Service) checkOpRights(commonParam model.CommonParam) (bool, error) {
	log.Warnf("开始请求[checkOpRights]，请求参数。userId: %s, opCodes: %v", commonParam.UserInfo.Ename, commonParam.MethodName)
	reqBodyBytes, _ := json.Marshal(map[string]interface{}{
		"userId":  commonParam.UserInfo.Ename,
		"opCodes": []string{commonParam.MethodName},
	})
	reqBody := strings.NewReader(string(reqBodyBytes))
	req, err := http.NewRequest("POST", s.tppRightsClient.CheckOpRightsUri, reqBody)
	if err != nil {
		log.Errorf("Failed to create tpp rights request: %v", err)
		return false, err
	}

	hash := sha1.New()
	hash.Write([]byte(commonParam.Timestamp + s.tppRightsClient.AppSecret + commonParam.Timestamp))
	signature := hex.EncodeToString(hash.Sum(nil))

	req.Header.Set("x-appkey", s.tppRightsClient.AppKey)
	req.Header.Set("x-timestamp", commonParam.Timestamp)
	req.Header.Set("x-signature", signature)
	req.Header.Set("Content-Type", "application/json")

	resp, err := s.tppRightsClient.Client.Do(req)
	if err != nil {
		log.Errorf("Failed to send tpp rights request: %v", err)
		return false, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("Failed to read tpp rights response body: %v", err)
		return false, err
	}

	log.Infof("Response: %s", string(body))

	// 定义结构体以匹配返回的 JSON 数据
	type CheckOpRightsResponse struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Result map[string]int `json:"result"`
		} `json:"data"`
	}

	var response CheckOpRightsResponse
	if err := json.Unmarshal(body, &response); err != nil || response.Code != 0 {
		log.Errorf("Failed to parse tpp rights response JSON: %v", err)
		return false, err
	}

	log.Infof("Parsed tpp rights Response: %+v", response)
	return response.Data.Result[commonParam.MethodName] == 1, nil
}

func (s *Service) checkStaff(activityAdmin string) (bool, error) {
	log.Warnf("开始请求[checkStaff]，请求参数。activityAdmin: %v", activityAdmin)
	req, err := http.NewRequest("GET", s.tppRightsClient.HrcStaffDetailUri+fmt.Sprintf("?ename=%s", activityAdmin), nil)
	if err != nil {
		log.Errorf("Failed to create tpp rights request: %v", err)
		return false, err
	}

	hash := sha1.New()
	ts := time.Now().Unix()
	hash.Write([]byte(cast.ToString(ts) + s.tppRightsClient.AppSecret + cast.ToString(ts)))
	signature := hex.EncodeToString(hash.Sum(nil))

	req.Header.Set("x-appkey", s.tppRightsClient.AppKey)
	req.Header.Set("x-timestamp", cast.ToString(ts))
	req.Header.Set("x-signature", signature)

	resp, err := s.tppRightsClient.Client.Do(req)
	if err != nil {
		log.Errorf("Failed to send tpp rights request: %v", err)
		return false, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("Failed to read tpp rights response body: %v", err)
		return false, err
	}

	log.Infof("Response: %s", string(body))

	// 定义结构体以匹配返回的 JSON 数据
	type StaffDetailResponse struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			EmplID        string `json:"emplid"`
			NameAC        string `json:"name_ac"`
			NameDisplay   string `json:"name_display"`
			NameFormal    string `json:"name_formal"`
			EmailBusiness string `json:"t_email_busn"`
			HRStatus      string `json:"hr_status"`
			BusinessUnit  string `json:"business_unit"`
			BusinessDescr string `json:"t_business_descr"`
			DeptPath      string `json:"c_dept_path"`
			OrgFullName   string `json:"c_org_fullname"`
			EmployeeClass string `json:"empl_class"`
			DeptID        string `json:"deptid"`
			DeptDescr     string `json:"dept_descr"`
			//LeaderEmplID      string `json:"leader_emplid"`
			//LeaderNameAC      string `json:"leader_name_ac"`
			//LeaderNameDisplay string `json:"leader_name_display"`
			//LeaderNameFormal  string `json:"leader_name_formal"`
		} `json:"data"`
	}

	var response StaffDetailResponse
	if err := json.Unmarshal(body, &response); err != nil || response.Code != 0 {
		log.Errorf("Failed to parse tpp StaffDetail response JSON, err: %v, code: %d", err, response.Code)
		return false, err
	}

	log.Infof("Parsed tpp StaffDetail Response: %+v", response)
	if response.Data.EmplID == "" || response.Data.NameAC != activityAdmin || response.Data.HRStatus != "A" {
		log.Errorf("员工状态异常。response.Data: %v", response.Data)
		return false, errors.New("员工状态异常")
	}
	return true, nil
}

func (s *Service) InvokeStaffSearch(kw string) (*model.StaffSearchResponse, error) {
	log.Warnf("开始请求[StaffSearch]，请求参数。kw: %v", kw)
	req, err := http.NewRequest("GET", s.tppRightsClient.HrcStaffSearchUri+fmt.Sprintf("?kw=%s", kw), nil)
	if err != nil {
		log.Errorf("Failed to create tpp rights request: %v", err)
		return nil, err
	}
	hash := sha1.New()
	ts := time.Now().Unix()
	hash.Write([]byte(cast.ToString(ts) + s.tppRightsClient.AppSecret + cast.ToString(ts)))
	signature := hex.EncodeToString(hash.Sum(nil))

	req.Header.Set("x-appkey", s.tppRightsClient.AppKey)
	req.Header.Set("x-timestamp", cast.ToString(ts))
	req.Header.Set("x-signature", signature)

	resp, err := s.tppRightsClient.Client.Do(req)
	if err != nil {
		log.Errorf("Failed to send tpp rights request: %v", err)
		return nil, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("Failed to read tpp rights response body: %v", err)
		return nil, err
	}

	log.Warnf("Response: %s", string(body))

	var response model.StaffSearchResponse
	if err := json.Unmarshal(body, &response); err != nil || response.Code != 0 {
		log.Errorf("Failed to parse tpp StaffDetail response JSON, err: %v, code: %d", err, response.Code)
		return nil, err
	}

	log.Warnf("Parsed tpp StaffDetail Response: %+v", response)

	return &response, nil
}

// 获取 gRPC 方法名
func getGRPCMethodName(ctx context.Context) string {
	method, ok := grpc.Method(ctx)
	if !ok {
		return ""
	}
	parts := strings.Split(method, "/")
	if len(parts) > 0 {
		return parts[len(parts)-1] // 获取路径的最后一部分
	}
	return ""
}

func (s *Service) CheckMethodRight(ctx context.Context) (bool, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		// 若没有元数据，可根据需求处理
		log.Errorf("no header%v", md)
		return false, errors.New("invalid grpc header")
	}
	// 从元数据中获取 x-Gopen-Id 头部信息
	tokens := md.Get("x-token")
	token, exist := lo.First(tokens)
	if !exist || utils.IsBlank(token) {
		log.Errorf("x-token不存在。md:%+v", md)
		return false, errors.New("x-token不存在")
	}
	timestamps := md.Get("x-timestamp")
	timestamp, exist := lo.First(timestamps)
	if !exist || utils.IsBlank(timestamp) {
		log.Errorf("x-timestamp不存在。md:%+v", md)
		return false, errors.New("x-timestamp不存在")
	}
	userInfo, err := utils.DecodeToken(token, timestamp, s.tppRightsClient.AppSecret)
	if err != nil {
		log.Errorf("DecodeToken失败。md:%+v", md)
		return false, errors.New("DecodeToken失败")
	}

	commonParam := model.CommonParam{
		Token:      token,
		Timestamp:  timestamp,
		MethodName: getGRPCMethodName(ctx),
		UserInfo:   userInfo,
	}
	res, err := s.checkOpRights(commonParam)
	if err != nil {
		log.Errorf("checkOpRights error。commonParam:%+v", commonParam)
		return false, errors.New("checkOpRights 失败")
	}
	return res, nil
}

// 检查职员权限（专属管理员+抽奖创建人有权限修改
func (s *Service) checkStaffPermission(lotteryActivity *model.LotteryActivity, ename string) (bool, error) {
	lotteryAdmin := lotteryActivity.LotteryAdmin
	if utils.IsBlank(ename) {
		return false, nil
	}
	var lotteryAdmins []string
	if utils.IsNotBlank(lotteryAdmin) {
		// 使用英文逗号分隔lotteryAdmin字符串
		lotteryAdmins = strings.Split(lotteryAdmin, ",")
	}
	return lo.Contains(lotteryAdmins, ename) || strings.EqualFold(lotteryActivity.CreateUser, ename), nil
}

// 检查职员账号
func (s *Service) checkStaffAccount(lotteryAdmin string) bool {
	var lotteryAdmins []string
	if utils.IsNotBlank(lotteryAdmin) {
		// 使用英文逗号分隔lotteryAdmin字符串
		lotteryAdmins = strings.Split(lotteryAdmin, ",")
	}
	// 检查抽奖管理员账号
	for _, admin := range lotteryAdmins {
		valid, err := s.checkStaff(admin)
		if err != nil || !valid {
			log.Warnf("检查职员账号，专属管理员非法。admin:%+v, valid:%v, err:%v", admin, valid, err)
			return false
		}
	}
	return true
}
