package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/errs"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"fmt"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"time"
)

type LotteryDeliveryOrderService struct {
	PresentDispatcher *PresentDispatcher
}

func NewLotteryDeliveryOrderService(presentDispatcher *PresentDispatcher) *LotteryDeliveryOrderService {
	return &LotteryDeliveryOrderService{
		PresentDispatcher: presentDispatcher,
	}
}

func (h *LotteryDeliveryOrderService) GetOrderType() int32 {
	return 0
}

func (h *LotteryDeliveryOrderService) DoBiz(ctx context.Context, req *dto.LotteryOrderReqDTO) *dto.Result[*dto.LotteryOrderInfoDTO] {
	// 构建消费发放参数
	current := time.Now()
	orderType := h.GetOrderType()
	// 构建主订单
	order := toOrder(req, orderType, current)
	insertResult := InsertOrder(order)

	if !insertResult.IsSuccess() {
		return dto.Fail[*dto.LotteryOrderInfoDTO](insertResult.Code, insertResult.Msg).WithError(insertResult.Err)
	}
	// 调用抽奖方法
	lotteryDto := req.LotteryInfoReqDTO
	LotteryPresents, drawErr := Draw(ctx, req.OpenId, cast.ToUint64(req.PlatUid), order.OrderId, order.BizTimestamp,
		lotteryDto.LotteryId, lotteryDto.LotteryPoolId, lotteryDto.LotteryNum)
	if drawErr != nil || len(LotteryPresents) == 0 {
		log.Errorf("抽奖失败 lotteryId=%d", req.LotteryInfoReqDTO.LotteryId)
		return h.handleLotteryFailure(order, drawErr)
	}
	presentList := ToPresents(LotteryPresents, order)
	// 抽奖成功则写入发奖记录
	if insertErr := BatchInsertPresents(presentList); insertErr != nil {
		return h.handlePresentInsertFailure(order, insertErr)
	}

	allSuccess := true
	var lastErr error
	for _, present := range presentList {
		// 根据prizeType调用不同的assetPresentHandler
		presentHandler, ok := h.PresentDispatcher.GetHandler(present.PrizeType)
		if !ok {
			// 处理未找到情况
			lastErr = fmt.Errorf("未找到资产处理器 type=%s", present.PrizeType)
			log.Errorf("未找到资产处理器 type=%s", present.PrizeType)
			allSuccess = false
			continue
		}
		// 执行资产发放
		result := presentHandler.Handle(ctx, req.LotteryActivity, present)
		if !result.IsSuccess() {
			allSuccess = false
			lastErr = result.Err
			log.Errorf("资产发放失败 presentID=%s, error=%v", present.PresentId, result.Err)
		}
	}

	if !allSuccess {
		//部分奖品发放失败，待补单
		return dto.Fail[*dto.LotteryOrderInfoDTO](errs.AssetPresentFailed.Code, "部分奖品发放失败，待补单").WithError(lastErr)
	}
	// 更新订单状态为成功
	xx, _ := time.LoadLocation(shanghaiLocName)
	localTime := time.Unix(req.BizTime, 0).In(xx)
	// 格式化为 yyyyMM
	monthStr := localTime.Format(monthFormat)
	rowsAffected, err := dao.LotteryOrderDao.UpdateWithSuccess(ctx, monthStr, req.OrderID)
	if err != nil {
		log.Warnf("更新订单状态失败 | orderID: %s, rowsAffected:%v, error: %v", req.OrderID, rowsAffected, err)
	}
	presents := lo.Map(presentList, func(p *model.Present, index int) dto.LotteryPresentRespDTO {
		return dto.LotteryPresentRespDTO{
			PresentId:  p.PresentId,
			PrizeId:    int32(p.PrizeId),
			PrizeName:  p.PrizeName,
			PrizeNum:   p.PrizeNum,
			PrizePic:   p.PrizePic,
			ExpireTime: p.ExpireTime,
			Status:     p.Status,
		}
	})

	return dto.Success(&dto.LotteryOrderInfoDTO{
		OrderID:        order.OrderId,
		Status:         constant.Success.Status,
		PresentRespDTO: presents,
		ConsumeRespDTO: nil,
		CreateTime:     order.CreateTime,
	})

}

func (h *LotteryDeliveryOrderService) handlePresentInsertFailure(order *model.Order, err error) *dto.Result[*dto.LotteryOrderInfoDTO] {
	log.Warnf("保存抽奖结果失败 | orderID: %s, error: %v", order.OrderId, err)
	return dto.Fail[*dto.LotteryOrderInfoDTO](errs.LotteryFailed.Code, "保存抽奖结果失败").WithError(err)
}

func (h *LotteryDeliveryOrderService) handleLotteryFailure(order *model.Order, err error) *dto.Result[*dto.LotteryOrderInfoDTO] {
	log.Warnf("抽奖失败 | orderID: %s, error: %v", order.OrderId, err)
	return dto.Fail[*dto.LotteryOrderInfoDTO](errs.LotteryFailed.Code, "抽奖失败").WithError(err)
}
