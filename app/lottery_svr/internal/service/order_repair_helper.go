package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
)

// OrderRepairHelper 订单补单辅助工具
type OrderRepairHelper struct{}

// NewOrderRepairHelper 创建补单辅助工具
func NewOrderRepairHelper() *OrderRepairHelper {
	return &OrderRepairHelper{}
}

// CheckPresentRecords 检查发奖记录
func (h *OrderRepairHelper) CheckPresentRecords(ctx context.Context, orderId, yearMonth string) ([]*model.Present, error) {
	presents, err := dao.LotteryPresentDao.SelectPresentListByOrderId(ctx, yearMonth, orderId)
	if err != nil {
		log.Warnf("查询发奖记录失败, orderId: %s, yearMonth: %s, error: %v", orderId, yearMonth, err)
		return nil, err
	}
	return presents, nil
}

// CheckConsumeRecords 检查消费记录
func (h *OrderRepairHelper) CheckConsumeRecords(ctx context.Context, orderId, yearMonth string) ([]*model.Consume, error) {
	consumes, err := dao.LotteryConsumeDao.SelectConsumeListByOrderId(ctx, yearMonth, orderId)
	if err != nil {
		log.Warnf("查询消费记录失败, orderId: %s, yearMonth: %s, error: %v", orderId, yearMonth, err)
		return nil, err
	}
	return consumes, nil
}

// CheckLotteryRecords 检查抽奖记录
// 注意：这里只会返回一条记录，是主键
func (h *OrderRepairHelper) CheckLotteryRecords(ctx context.Context, orderId, yearMonth string) (*model.LotteryRecord, error) {
	// 抽奖记录表中draw_id等于order_id，可以直接查询
	records, err := dao.LotteryRecordDao.SelectLotteryRecordsByOrderId(ctx, yearMonth, orderId)
	if err != nil {
		log.Warnf("查询抽奖记录失败, orderId: %s, yearMonth: %s, error: %v", orderId, yearMonth, err)
		return nil, err
	}
	if len(records) == 0 {
		return nil, nil
	}
	if len(records) > 1 {
		log.Warnf("查询抽奖记录异常，预期1条但实际返回%d条, orderId: %s", len(records), orderId)
	}
	return records[0], nil
}

// UpdateOrderStatus 更新订单状态
func (h *OrderRepairHelper) UpdateOrderStatus(ctx context.Context, orderId, yearMonth string, targetStatus int32) (bool, error) {
	var rowsAffected int64
	var err error

	switch targetStatus {
	case constant.Success.Status:
		// 更新为成功状态
		rowsAffected, err = dao.LotteryOrderDao.UpdateWithSuccess(ctx, yearMonth, orderId)
	case constant.Fail.Status:
		// 更新为失败状态
		db := dao.GetDb()
		rowsAffected, err = dao.LotteryOrderDao.UpdateWithFail(yearMonth, orderId, db)
	default:
		log.Errorf("不支持的目标状态: %d", targetStatus)
		return false, nil
	}

	if err != nil {
		log.Errorf("更新订单状态失败, orderId: %s, targetStatus: %d, error: %v", orderId, targetStatus, err)
		return false, err
	}

	if rowsAffected != 1 {
		log.Warnf("更新订单状态影响行数异常, orderId: %s, targetStatus: %d, rowsAffected: %d",
			orderId, targetStatus, rowsAffected)
		return false, nil
	}

	log.Infof("订单状态更新成功, orderId: %s, targetStatus: %d", orderId, targetStatus)
	return true, nil
}