package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"fmt"

	"github.com/dromara/carbon/v2"
)

// OrderRepairHelper 订单补单辅助工具
type OrderRepairHelper struct{}

// NewOrderRepairHelper 创建补单辅助工具
func NewOrderRepairHelper() *OrderRepairHelper {
	return &OrderRepairHelper{}
}

// RepairContext 补单上下文
type RepairContext struct {
	Order     *model.Order
	YearMonth string
	Ctx       context.Context
}

// RepairResult 补单结果
type RepairResult struct {
	Success      bool
	TargetStatus int32
	Reason       string
	Error        error
}

// CheckPresentRecords 检查发奖记录
func (h *OrderRepairHelper) CheckPresentRecords(ctx context.Context, orderId, yearMonth string) ([]*model.Present, error) {
	presents, err := dao.LotteryPresentDao.SelectPresentListByOrderId(ctx, yearMonth, orderId)
	if err != nil {
		log.Warnf("查询发奖记录失败, orderId: %s, yearMonth: %s, error: %v", orderId, yearMonth, err)
		return nil, err
	}
	return presents, nil
}

// CheckConsumeRecords 检查消费记录
func (h *OrderRepairHelper) CheckConsumeRecords(ctx context.Context, orderId, yearMonth string) ([]*model.Consume, error) {
	consumes, err := dao.LotteryConsumeDao.SelectConsumeListByOrderId(ctx, yearMonth, orderId)
	if err != nil {
		log.Warnf("查询消费记录失败, orderId: %s, yearMonth: %s, error: %v", orderId, yearMonth, err)
		return nil, err
	}
	return consumes, nil
}

// CheckLotteryRecords 检查抽奖记录
// 注意：这里只会返回一条记录，是主键
func (h *OrderRepairHelper) CheckLotteryRecords(ctx context.Context, orderId, yearMonth string) (*model.LotteryRecord, error) {
	// 抽奖记录表中draw_id等于order_id，可以直接查询
	records, err := dao.LotteryRecordDao.SelectLotteryRecordsByOrderId(ctx, yearMonth, orderId)
	if err != nil {
		log.Warnf("查询抽奖记录失败, orderId: %s, yearMonth: %s, error: %v", orderId, yearMonth, err)
		return nil, err
	}
	if len(records) == 0 {
		return nil, nil
	}
	if len(records) > 1 {
		log.Warnf("查询抽奖记录异常，预期1条但实际返回%d条, orderId: %s", len(records), orderId)
	}
	return records[0], nil
}

// UpdateOrderStatus 更新订单状态
func (h *OrderRepairHelper) UpdateOrderStatus(ctx context.Context, orderId, yearMonth string, targetStatus int32) (bool, error) {
	var rowsAffected int64
	var err error

	switch targetStatus {
	case constant.Success.Status:
		// 更新为成功状态
		rowsAffected, err = dao.LotteryOrderDao.UpdateWithSuccess(ctx, yearMonth, orderId)
	case constant.Fail.Status:
		// 更新为失败状态
		db := dao.GetDb()
		rowsAffected, err = dao.LotteryOrderDao.UpdateWithFail(yearMonth, orderId, db)
	default:
		log.Errorf("不支持的目标状态: %d", targetStatus)
		return false, nil
	}

	if err != nil {
		log.Errorf("更新订单状态失败, orderId: %s, targetStatus: %d, error: %v", orderId, targetStatus, err)
		return false, err
	}

	if rowsAffected != 1 {
		log.Warnf("更新订单状态影响行数异常, orderId: %s, targetStatus: %d, rowsAffected: %d",
			orderId, targetStatus, rowsAffected)
		return false, nil
	}

	log.Infof("订单状态更新成功, orderId: %s, targetStatus: %d", orderId, targetStatus)
	return true, nil
}

// determineLotteryDeliveryStatus 判断抽奖发奖订单状态
func (h *OrderRepairHelper) determineLotteryDeliveryStatus(orderId string, lotterys []*model.LotteryRecord, presents []*model.Present) RepairResult {
	// 首先检查抽奖记录
	if len(lotterys) == 0 {
		log.Infof("订单%s无抽奖记录，补成失败状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Fail.Status,
			Reason:       "无抽奖记录",
		}
	}

	// 有抽奖记录，检查发奖记录
	if len(presents) == 0 {
		log.Infof("订单%s有抽奖记录但无发奖记录，补成失败状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Fail.Status,
			Reason:       "有抽奖记录但无发奖记录",
		}
	}

	// 检查发奖是否都成功
	successCount := 0
	for _, present := range presents {
		if present.Status == constant.Success.Status {
			successCount++
		}
	}

	if successCount == len(presents) {
		log.Infof("订单%s抽奖和发奖都成功，补成成功状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Success.Status,
			Reason:       fmt.Sprintf("抽奖记录%d条，发奖记录%d条都成功", len(lotterys), len(presents)),
		}
	}

	log.Infof("订单%s抽奖成功但发奖部分失败，补成失败状态", orderId)
	return RepairResult{
		Success:      true,
		TargetStatus: constant.Fail.Status,
		Reason:       fmt.Sprintf("抽奖成功但发奖部分失败，成功%d条，总共%d条", successCount, len(presents)),
	}
}

// determineConsumeLotteryDeliveryStatus 判断扣抽奖券抽奖发奖订单状态
func (h *OrderRepairHelper) determineConsumeLotteryDeliveryStatus(orderId string, presents []*model.Present, consumes []*model.Consume, lotterys []*model.LotteryRecord) RepairResult {
	// 检查消费记录
	if len(consumes) == 0 {
		log.Infof("订单%s无消费记录，补成失败状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Fail.Status,
			Reason:       "无消费记录",
		}
	}

	// 检查消费是否成功
	consumeSuccess := false
	for _, consume := range consumes {
		if consume.Status == constant.Success.Status {
			consumeSuccess = true
			break
		}
	}

	if !consumeSuccess {
		log.Infof("订单%s消费失败，补成失败状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Fail.Status,
			Reason:       "消费失败",
		}
	}

	// 消费成功，检查抽奖记录
	if len(lotterys) == 0 {
		log.Infof("订单%s消费成功但无抽奖记录，补成失败状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Fail.Status,
			Reason:       "消费成功但无抽奖记录",
		}
	}

	// 消费和抽奖都成功，检查发奖
	if len(presents) == 0 {
		log.Infof("订单%s消费和抽奖成功但无发奖记录，补成失败状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Fail.Status,
			Reason:       "消费和抽奖成功但无发奖记录",
		}
	}

	// 检查发奖是否都成功
	successCount := 0
	for _, present := range presents {
		if present.Status == constant.Success.Status {
			successCount++
		}
	}

	if successCount == len(presents) {
		log.Infof("订单%s消费、抽奖和发奖都成功，补成成功状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Success.Status,
			Reason:       fmt.Sprintf("消费、抽奖记录%d条、发奖%d条都成功", len(lotterys), len(presents)),
		}
	}

	log.Infof("订单%s消费和抽奖成功但发奖部分失败，补成失败状态", orderId)
	return RepairResult{
		Success:      true,
		TargetStatus: constant.Fail.Status,
		Reason:       fmt.Sprintf("消费和抽奖成功但发奖部分失败，成功%d条，总共%d条", successCount, len(presents)),
	}
}

// determineDeliveryTicketStatus 判断发放抽奖券订单状态
func (h *OrderRepairHelper) determineDeliveryTicketStatus(orderId string, presents []*model.Present) RepairResult {
	// 发放抽奖券订单只写t_order和t_present表，不涉及抽奖记录
	if len(presents) == 0 {
		log.Infof("订单%s无发奖记录，补成失败状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Fail.Status,
			Reason:       "无发奖记录",
		}
	}

	// 检查发奖是否都成功
	successCount := 0
	for _, present := range presents {
		if present.Status == constant.Success.Status {
			successCount++
		}
	}

	if successCount == len(presents) {
		log.Infof("订单%s所有发奖记录都成功，补成成功状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Success.Status,
			Reason:       fmt.Sprintf("所有发奖记录(%d条)都成功", len(presents)),
		}
	}

	log.Infof("订单%s发奖记录部分失败，补成失败状态", orderId)
	return RepairResult{
		Success:      true,
		TargetStatus: constant.Fail.Status,
		Reason:       fmt.Sprintf("发奖记录部分失败，成功%d条，总共%d条", successCount, len(presents)),
	}
}

// GetYearMonthFromTimestamp 从时间戳获取年月字符串
func (h *OrderRepairHelper) GetYearMonthFromTimestamp(timestamp int64) string {
	c := carbon.CreateFromTimestamp(timestamp)
	return c.Format("Ymd")[:6] // 取前6位得到YYYYMM格式
}
