package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"fmt"

	"github.com/dromara/carbon/v2"
)

// OrderRepairHelper 订单补单辅助工具
type OrderRepairHelper struct{}

// NewOrderRepairHelper 创建补单辅助工具
func NewOrderRepairHelper() *OrderRepairHelper {
	return &OrderRepairHelper{}
}

// RepairContext 补单上下文
type RepairContext struct {
	Order     *model.Order
	YearMonth string
	Ctx       context.Context
}

// RepairResult 补单结果
type RepairResult struct {
	Success      bool
	TargetStatus int32
	Reason       string
	Error        error
}

// CheckPresentRecords 检查发奖记录
func (h *OrderRepairHelper) CheckPresentRecords(ctx context.Context, orderId, yearMonth string) ([]*model.Present, error) {
	presents, err := dao.LotteryPresentDao.SelectPresentListByOrderId(ctx, yearMonth, orderId)
	if err != nil {
		log.Warnf("查询发奖记录失败, orderId: %s, yearMonth: %s, error: %v", orderId, yearMonth, err)
		return nil, err
	}
	return presents, nil
}

// CheckConsumeRecords 检查消费记录
func (h *OrderRepairHelper) CheckConsumeRecords(ctx context.Context, orderId, yearMonth string) ([]*model.Consume, error) {
	consumes, err := dao.LotteryConsumeDao.SelectConsumeListByOrderId(ctx, yearMonth, orderId)
	if err != nil {
		log.Warnf("查询消费记录失败, orderId: %s, yearMonth: %s, error: %v", orderId, yearMonth, err)
		return nil, err
	}
	return consumes, nil
}

// CheckLotteryRecords 检查抽奖记录
func (h *OrderRepairHelper) CheckLotteryRecords(ctx context.Context, orderId, yearMonth string) ([]*model.LotteryRecord, error) {
	// 注意：抽奖记录表(t_lottery_*)中没有order_id字段，而是使用draw_id
	// 这里暂时返回空，如果需要根据订单ID查询抽奖记录，需要额外的关联逻辑
	log.Infof("查询抽奖记录, orderId: %s, yearMonth: %s (注意：抽奖记录表结构不支持直接按order_id查询)", orderId, yearMonth)
	return nil, nil
}

// UpdateOrderStatus 更新订单状态
func (h *OrderRepairHelper) UpdateOrderStatus(ctx context.Context, orderId, yearMonth string, targetStatus int32) (bool, error) {
	var rowsAffected int64
	var err error

	switch targetStatus {
	case constant.Success.Status:
		// 更新为成功状态
		rowsAffected, err = dao.LotteryOrderDao.UpdateWithSuccess(ctx, yearMonth, orderId)
	case constant.Fail.Status:
		// 更新为失败状态
		db := dao.GetDb()
		rowsAffected, err = dao.LotteryOrderDao.UpdateWithFail(yearMonth, orderId, db)
	default:
		log.Errorf("不支持的目标状态: %d", targetStatus)
		return false, nil
	}

	if err != nil {
		log.Errorf("更新订单状态失败, orderId: %s, targetStatus: %d, error: %v", orderId, targetStatus, err)
		return false, err
	}

	if rowsAffected != 1 {
		log.Warnf("更新订单状态影响行数异常, orderId: %s, targetStatus: %d, rowsAffected: %d",
			orderId, targetStatus, rowsAffected)
		return false, nil
	}

	log.Infof("订单状态更新成功, orderId: %s, targetStatus: %d", orderId, targetStatus)
	return true, nil
}

// DetermineOrderStatus 根据业务记录判断订单应该的最终状态
func (h *OrderRepairHelper) DetermineOrderStatus(ctx *RepairContext, presents []*model.Present, consumes []*model.Consume) RepairResult {
	orderId := ctx.Order.OrderId
	orderType := ctx.Order.OrderType

	switch constant.OrderType(orderType) {
	case constant.OrderTypeLottery:
		// 纯抽奖：有发奖记录就是成功
		return h.determineLotteryStatus(orderId, presents)

	case constant.OrderTypeLotteryDelivery:
		// 抽奖发奖：有发奖记录且发奖成功就是成功
		return h.determineLotteryDeliveryStatus(orderId, presents)

	case constant.OrderTypeConsumeLotteryDelivery:
		// 扣抽奖券抽奖发奖：需要检查消费和发奖
		return h.determineConsumeLotteryDeliveryStatus(orderId, presents, consumes)

	case constant.OrderTypeDeliveryTicket:
		// 发放抽奖券：有发奖记录且发奖成功就是成功
		return h.determineDeliveryTicketStatus(orderId, presents)

	default:
		return RepairResult{
			Success:      false,
			TargetStatus: constant.Fail.Status,
			Reason:       "未知的订单类型",
		}
	}
}

// determineLotteryStatus 判断纯抽奖订单状态
func (h *OrderRepairHelper) determineLotteryStatus(orderId string, presents []*model.Present) RepairResult {
	if len(presents) > 0 {
		log.Infof("订单%s有发奖记录%d条，补成成功状态", orderId, len(presents))
		
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Success.Status,
			Reason:       fmt.Sprintf("有发奖记录%d条", len(presents)),
		}
	}

	log.Infof("订单%s无发奖记录，补成失败状态", orderId)
	return RepairResult{
		Success:      true,
		TargetStatus: constant.Fail.Status,
		Reason:       "无发奖记录",
	}
}

// determineLotteryDeliveryStatus 判断抽奖发奖订单状态
func (h *OrderRepairHelper) determineLotteryDeliveryStatus(orderId string, presents []*model.Present) RepairResult {
	if len(presents) == 0 {
		log.Infof("订单%s无发奖记录，补成失败状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Fail.Status,
			Reason:       "无发奖记录",
		}
	}

	// 检查发奖是否都成功
	successCount := 0
	for _, present := range presents {
		if present.Status == constant.Success.Status {
			successCount++
		}
	}

	if successCount == len(presents) {
		log.Infof("订单%s所有发奖记录都成功，补成成功状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Success.Status,
			Reason:       fmt.Sprintf("所有发奖记录(%d条)都成功", len(presents)),
		}
	}

	log.Infof("订单%s发奖记录部分失败，补成失败状态", orderId)
	return RepairResult{
		Success:      true,
		TargetStatus: constant.Fail.Status,
		Reason:       fmt.Sprintf("发奖记录部分失败，成功%d条，总共%d条", successCount, len(presents)),
	}
}

// determineConsumeLotteryDeliveryStatus 判断扣抽奖券抽奖发奖订单状态
func (h *OrderRepairHelper) determineConsumeLotteryDeliveryStatus(orderId string, presents []*model.Present, consumes []*model.Consume) RepairResult {
	// 检查消费记录
	if len(consumes) == 0 {
		log.Infof("订单%s无消费记录，补成失败状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Fail.Status,
			Reason:       "无消费记录",
		}
	}

	// 检查消费是否成功
	consumeSuccess := false
	for _, consume := range consumes {
		if consume.Status == constant.Success.Status {
			consumeSuccess = true
			break
		}
	}

	if !consumeSuccess {
		log.Infof("订单%s消费失败，补成失败状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Fail.Status,
			Reason:       "消费失败",
		}
	}

	// 消费成功，检查发奖
	if len(presents) == 0 {
		log.Infof("订单%s消费成功但无发奖记录，补成失败状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Fail.Status,
			Reason:       "消费成功但无发奖记录",
		}
	}

	// 检查发奖是否都成功
	successCount := 0
	for _, present := range presents {
		if present.Status == constant.Success.Status {
			successCount++
		}
	}

	if successCount == len(presents) {
		log.Infof("订单%s消费和发奖都成功，补成成功状态", orderId)
		return RepairResult{
			Success:      true,
			TargetStatus: constant.Success.Status,
			Reason:       fmt.Sprintf("消费和发奖都成功，发奖%d条", len(presents)),
		}
	}

	log.Infof("订单%s消费成功但发奖部分失败，补成失败状态", orderId)
	return RepairResult{
		Success:      true,
		TargetStatus: constant.Fail.Status,
		Reason:       fmt.Sprintf("消费成功但发奖部分失败，成功%d条，总共%d条", successCount, len(presents)),
	}
}

// determineDeliveryTicketStatus 判断发放抽奖券订单状态
func (h *OrderRepairHelper) determineDeliveryTicketStatus(orderId string, presents []*model.Present) RepairResult {
	// 发放抽奖券订单的逻辑与抽奖发奖类似
	return h.determineLotteryDeliveryStatus(orderId, presents)
}

// GetYearMonthFromTimestamp 从时间戳获取年月字符串
func (h *OrderRepairHelper) GetYearMonthFromTimestamp(timestamp int64) string {
	c := carbon.CreateFromTimestamp(timestamp)
	return c.Format("Ymd")[:6] // 取前6位得到YYYYMM格式
}
