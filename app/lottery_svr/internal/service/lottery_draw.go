package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	stringutil "cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/utils"
	lottery_admin "cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/lottery/admin"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"encoding/json"
	"errors"
	"github.com/dromara/carbon/v2"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"strings"
	"time"
)

// GetTicketCost 根据抽奖配置ID和抽奖次数 ->返回扣抽奖券 ID 和数量 （如果是酷狗业务线则返回 nil）
func GetTicketCost(ctx context.Context, req model.TicketCostReq) (*model.TicketCostRsp, error) {
	log.Warnf("查询抽奖消耗抽奖券数量，开始查询,req:%+v", req)
	activityId := req.ActivityId
	drawNum := req.DrawNum
	lotteryActivity, err := dao.LotteryActivityDao.GetLotteryActivityById(ctx, activityId)
	if err != nil || lotteryActivity == nil {
		log.Warnf("查询抽奖消耗抽奖券数量，获取抽奖活动失败,err=%v", err)
		return nil, err
	}
	// 获取平台配置信息
	platInfo, exist := constant.GetPlatInfoByCode(lotteryActivity.PlatCode)
	if !exist {
		log.Warnf("查询抽奖消耗抽奖券数量，平台配置不存在,platCode=%s", lotteryActivity.PlatCode)
		return nil, errors.New("platCode not exist")
	}
	// 酷狗业务线不扣抽奖券
	if platInfo.IsKugouPlat() {
		log.Warnf("查询抽奖消耗抽奖券数量，酷狗业务线不扣抽奖券")
		return nil, errors.New("酷狗业务线不扣抽奖券")
	}
	// 根据抽奖活动配置查询抽奖券配置，根据抽奖次数获取对应的抽奖券数量
	lotteryTicket, err := dao.LotteryTicketDao.GetLotteryTicketByActivityId(ctx, activityId)
	if err != nil || lotteryTicket == nil {
		log.Warnf("查询抽奖消耗抽奖券数量，获取抽奖券配置失败,err=%v", err)
		return nil, err
	}
	var costTicketRsp = model.TicketCostRsp{
		AssetType: constant.PrizeTypeBossAsset.Code() + "_consume",
		TicketId:  lotteryTicket.TicketId,
		TicketNum: int64(lotteryTicket.SingleDrawTicketNum) * drawNum,
	}
	log.Warnf("查询抽奖消耗抽奖券数量，查询完成,costTicketRsp=%+v", costTicketRsp)
	return &costTicketRsp, nil
}

func Draw(ctx context.Context, openId string, platUid uint64, drawId string, drawTime int64,
	lotteryId int32, poolId string, drawNum int64) ([]*model.LotteryPresent, error) {
	log.Warnf("执行抽奖，开始抽奖,drawId=%s,drawTime=%d,lotteryId=%d,poolId=%s,drawNum=%d", drawId, drawTime, lotteryId, poolId, drawNum)
	// 检查重入订单
	month := GetYearMonth(drawTime)
	lotteryRecord, err := dao.LotteryRecordDao.GetLotteryRecordByDrawId(ctx, month, drawId)
	if err != nil {
		log.Warnf("执行抽奖，获取抽奖记录失败,err=%v", err)
		return nil, err
	}
	if lotteryRecord != nil {
		log.Warnf("执行抽奖，存在重入订单,drawId=%s", drawId)
		var presents []*model.LotteryPresent
		drawResult := lotteryRecord.DrawResult
		err := json.Unmarshal([]byte(drawResult), &presents)
		if err != nil {
			log.Warnf("执行抽奖，解析抽奖结果失败,err=%v", err)
			return nil, err
		}
		return presents, nil
	}
	lotteryActivity, err := dao.LotteryActivityDao.GetLotteryActivityById(ctx, lotteryId)
	if err != nil {
		log.Warnf("执行抽奖，获取抽奖活动失败,err=%v", err)
		return nil, err
	}
	var poolInfos []*lottery_admin.PoolInfo
	err = json.Unmarshal([]byte(lotteryActivity.PoolInfo), &poolInfos)
	if err != nil || len(poolInfos) == 0 {
		log.Warnf("执行抽奖，解析奖池信息失败,err=%v", err)
		return nil, err
	}
	poolInfo, exist := lo.Find(poolInfos, func(item *lottery_admin.PoolInfo) bool {
		return strings.EqualFold(item.PoolId, poolId)
	})
	if !exist {
		log.Warnf("执行抽奖，奖池不存在,poolId=%s", poolId)
		return nil, errors.New("奖池不存在")
	}
	log.Warnf("执行抽奖，获取到奖池信息: %+v", poolInfo)
	// 获取奖池模型
	modelTypeInfo, exist := GetPoolModelInfoByCode(poolInfo.PoolModel)
	if !exist {
		log.Warnf("执行抽奖，奖池模型不存在,poolModel=%s", poolInfo.PoolModel)
		return nil, errors.New("奖池模型不存在")
	}
	// 执行奖池抽奖
	rewardPrizes, err := modelTypeInfo.Draw(poolInfo)
	if err != nil {
		log.Warnf("执行抽奖，执行抽奖失败,err=%v", err)
		return nil, err
	}
	// 生成奖品列表
	presents := lo.Map(rewardPrizes, func(item *lottery_admin.PrizeItem, index int) *model.LotteryPresent {
		return &model.LotteryPresent{
			PresentId:   s.GenGlobalId(),
			PrizeItemId: cast.ToInt64(item.PrizeItemId),
			PrizeType:   item.PrizeType,
			PrizeId:     item.PrizeId,
			PrizeName:   item.PrizeName,
			PrizeNum:    item.PrizeNum,
			PrizePic:    item.PrizePic,
			ExpireTime:  item.ExpireTime,
		}
	})
	log.Warnf("执行抽奖，生成发奖列表: %+v", stringutil.ToJSONIgnoreError(presents))
	// 保存抽奖结果
	marshal, err := json.Marshal(presents)
	if err != nil {
		log.Warnf("执行抽奖，序列化抽奖结果失败,err=%v", err)
		return nil, err
	}
	lotteryRecord = &model.LotteryRecord{
		DrawId:        drawId,
		DrawTime:      uint64(drawTime),
		OpenId:        openId,
		PlatUid:       platUid,
		LotteryId:     lotteryId,
		LotteryPoolId: poolId,
		DrawNum:       drawNum,
		DrawResult:    string(marshal),
		CreateTime:    time.Now(),
		UpdateTime:    time.Now(),
	}
	rowsAffected, err := dao.LotteryRecordDao.InsertLotteryRecordIgnore(ctx, month, lotteryRecord)
	if err != nil {
		log.Warnf("执行抽奖，保存抽奖记录失败,err=%v", err)
		return nil, err
	}
	if rowsAffected < 1 {
		log.Warnf("执行抽奖，存在并发订单,drawId=%s", drawId)
		// 检查并发订单
		lotteryRecord, err := dao.LotteryRecordDao.GetLotteryRecordByDrawId(ctx, month, drawId)
		if err != nil {
			log.Warnf("执行抽奖，获取抽奖记录失败,err=%v", err)
			return nil, err
		}
		if lotteryRecord != nil {
			var presents []*model.LotteryPresent
			drawResult := lotteryRecord.DrawResult
			err := json.Unmarshal([]byte(drawResult), &presents)
			if err != nil {
				log.Warnf("执行抽奖，解析抽奖结果失败,err=%v", err)
				return nil, err
			}
			return presents, nil
		}
		return nil, err
	}
	return presents, nil
}

func GetYearMonth(secs int64) string {
	c := carbon.CreateFromTimestamp(secs)
	yearMonth := c.Format("Ymd")[:6]
	return yearMonth
}
