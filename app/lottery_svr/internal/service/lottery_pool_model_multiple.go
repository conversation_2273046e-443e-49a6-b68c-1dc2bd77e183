package service

import (
	lottery_admin "cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/lottery/admin"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"math/rand"
)

func PoolModelMultipleCheck(poolInfo *lottery_admin.PoolInfo) bool {
	log.Warnf("PoolModelSimpleCheck, poolInfo:%+v", poolInfo)
	if poolInfo == nil {
		log.Warnf("PoolModelSimpleCheck, poolInfo is nil")
		return false
	}
	// 检查每个奖项的属性是否合法
	invalid := lo.SomeBy(poolInfo.PrizeList, func(prize *lottery_admin.PrizeItem) bool {
		return CheckPrizeItem(prize)
	})
	if invalid {
		log.Warnf("PoolModelSimpleCheck，奖池奖项检查不通过。poolInfo:%+v", poolInfo)
		return false
	}
	return true
}

func PoolModelMultipleDraw(poolInfo *lottery_admin.PoolInfo, drawNum int64) ([]*lottery_admin.PrizeItem, error) {
	log.Warnf("PoolModelMultipleDraw, poolInfo:%+v", poolInfo)
	if poolInfo == nil || len(poolInfo.PrizeList) == 0 {
		return nil, nil
	}
	var result []*lottery_admin.PrizeItem
	for _, prize := range poolInfo.PrizeList {
		probability, err := decimal.NewFromString(prize.Probability)
		if err != nil {
			log.Warnf("PoolModelMultipleDraw, 概率转换失败, prize:%+v, err:%v", prize, err)
			continue
		}
		// 生成0-1之间的随机数
		randNum := decimal.NewFromFloat(rand.Float64())
		// 如果随机数小于等于概率值，则命中该奖项
		if randNum.LessThanOrEqual(probability) {
			result = append(result, prize)
		}
		log.Warnf("PoolModelMultipleDraw, 概率:%v, 随机数:%v", probability, randNum)
	}
	return result, nil
}
