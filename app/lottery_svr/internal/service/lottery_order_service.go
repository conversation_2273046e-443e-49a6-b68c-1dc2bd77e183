package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/errs"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"time"

	"github.com/dromara/carbon/v2"
)

type PureLotteryOrderService struct {
}

func (h *PureLotteryOrderService) GetOrderType() int32 {
	return int32(constant.OrderTypeLottery)
}

func NewPureLotteryOrderService() *PureLotteryOrderService {
	return &PureLotteryOrderService{}
}

func (h *PureLotteryOrderService) DoBiz(ctx context.Context, req *dto.LotteryOrderReqDTO) *dto.Result[*dto.LotteryOrderInfoDTO] {
	// 构建消费发放参数
	current := time.Now()
	orderType := h.GetOrderType()
	// 构建主订单
	order := toOrder(req, orderType, current)
	insertResult := InsertOrder(order)
	if !insertResult.IsSuccess() {
		return dto.Fail[*dto.LotteryOrderInfoDTO](insertResult.Code, insertResult.Msg).WithError(insertResult.Err)
	}
	// 调用抽奖方法
	lotteryDto := req.LotteryInfoReqDTO
	LotteryPresents, err := Draw(ctx, req.OpenId, cast.ToUint64(req.PlatUid), order.OrderId, order.BizTimestamp,
		lotteryDto.LotteryId, lotteryDto.LotteryPoolId, lotteryDto.LotteryNum)
	if err != nil || len(LotteryPresents) == 0 {
		log.Errorf("抽奖失败 lotteryId=%d", req.LotteryInfoReqDTO.LotteryId)
		return dto.Fail[*dto.LotteryOrderInfoDTO](errs.LotteryFailedRefund.Code, "抽奖失败,订单退款").WithError(err)
	}
	presentList := ToPresents(LotteryPresents, order)

	// 更新订单状态为成功
	c := carbon.CreateFromTimestamp(req.BizTime)
	// 格式化为 yyyyMM
	monthStr := c.Format("Ymd")[:6]
	rowsAffected, err := dao.LotteryOrderDao.UpdateWithSuccess(ctx, monthStr, req.OrderID)
	if err != nil {
		log.Warnf("更新订单状态为成功失败 orderID=%s, rowsAffected:%v", req.OrderID, rowsAffected)
	}

	presents := lo.Map(presentList, func(p *model.Present, index int) dto.LotteryPresentRespDTO {
		return dto.LotteryPresentRespDTO{
			PresentId:  p.PresentId,
			PrizeId:    int32(p.PrizeId),
			PrizeName:  p.PrizeName,
			PrizeNum:   p.PrizeNum,
			PrizePic:   p.PrizePic,
			ExpireTime: p.ExpireTime,
		}
	})

	return dto.Success(&dto.LotteryOrderInfoDTO{
		OrderID:        order.OrderId,
		Status:         constant.Success.Status,
		PresentRespDTO: presents,
		ConsumeRespDTO: []dto.LotteryConsumeRespDTO{},
		CreateTime:     order.CreateTime,
	})

}
