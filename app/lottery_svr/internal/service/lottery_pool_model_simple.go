package service

import (
	lottery_admin "cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/lottery/admin"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"math/rand"
)

func PoolModelSimpleCheck(poolInfo *lottery_admin.PoolInfo) bool {
	log.Warnf("PoolModelSimpleCheck, poolInfo:%+v", poolInfo)
	if poolInfo == nil {
		log.Warnf("PoolModelSimpleCheck, poolInfo is nil")
		return false
	}
	// 检查每个奖项的属性是否合法
	invalid := lo.SomeBy(poolInfo.PrizeList, func(prize *lottery_admin.PrizeItem) bool {
		return CheckPrizeItem(prize)
	})
	if invalid {
		log.Warnf("PoolModelSimpleCheck，奖池奖项检查不通过。poolInfo:%+v", poolInfo)
		return false
	}
	// 检查奖项概率之和是否等于1
	sumProbability := lo.Reduce(poolInfo.PrizeList, func(acc decimal.Decimal, item *lottery_admin.PrizeItem, index int) decimal.Decimal {
		probability, err := decimal.NewFromString(item.Probability)
		if err != nil {
			return decimal.NewFromInt(0)
		}
		return acc.Add(probability)
	}, decimal.NewFromInt(0))
	if !sumProbability.Equal(decimal.NewFromInt(1)) {
		log.Warnf("PoolModelSimpleCheck，奖池奖项概率之和不等于1。sumProbability:%s, poolInfo:%+v", sumProbability, poolInfo)
		return false
	}
	return true
}

func PoolModelSimpleDraw(poolInfo *lottery_admin.PoolInfo) ([]*lottery_admin.PrizeItem, error) {
	log.Warnf("PoolModelSimpleDraw，奖池信息。poolInfo:%+v", poolInfo)
	if poolInfo == nil || len(poolInfo.PrizeList) == 0 {
		log.Warnf("PoolModelSimpleDraw, 奖池为空, poolInfo:%+v", poolInfo)
		return nil, nil
	}
	// 使用全局随机数生成器,避免并发问题
	randomNum := decimal.NewFromFloat(rand.Float64())
	// 根据概率区间判断命中哪个奖项
	var sumProbability decimal.Decimal
	for _, prize := range poolInfo.PrizeList {
		probability, err := decimal.NewFromString(prize.Probability)
		if err != nil {
			log.Warnf("PoolModelSimpleDraw, 概率转换失败, prize:%+v, err:%v", prize, err)
			continue
		}
		sumProbability = sumProbability.Add(probability)
		// 如果随机数小于等于累计概率,则命中该奖项
		if randomNum.LessThanOrEqual(sumProbability) {
			return []*lottery_admin.PrizeItem{prize}, nil
		}
	}
	log.Warnf("PoolModelSimpleDraw, 未命中任何奖项, randomNum:%v, sumProbability:%v", randomNum, sumProbability)
	return nil, nil
}
