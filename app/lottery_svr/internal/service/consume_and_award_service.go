package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/errs"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/utils"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"fmt"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"strconv"
	"time"
)

type ConsumeAndAwardOrderService struct {
	consumeDispatcher *ConsumeDispatcher
	presentDispatcher *PresentDispatcher
}

func (h *ConsumeAndAwardOrderService) GetOrderType() int32 {
	return 0
}

func NewConsumeAndAwardOrderService(consumeDispatcher *ConsumeDispatcher, presentDispatcher *PresentDispatcher) *ConsumeAndAwardOrderService {
	return &ConsumeAndAwardOrderService{
		consumeDispatcher: consumeDispatcher,
		presentDispatcher: presentDispatcher,
	}
}

func (h *ConsumeAndAwardOrderService) DoBiz(ctx context.Context, req *dto.LotteryOrderReqDTO) *dto.Result[*dto.LotteryOrderInfoDTO] {
	//1.构建消费发放参数
	current := time.Now()
	orderType := h.GetOrderType()
	//构建主订单 & 扣费订单
	order := toOrder(req, orderType, current)
	consumeOrder := toConsumeOrder(req, current)
	insertResult := h.insertWithConsume(order, consumeOrder)

	if !insertResult.IsSuccess() {
		return dto.Fail[*dto.LotteryOrderInfoDTO](insertResult.Code, insertResult.Msg).WithError(insertResult.Err)
	}

	//2.调用消费
	handler, ok := h.consumeDispatcher.GetHandler(consumeOrder.ConsumeType)
	if !ok {
		// 处理未找到情况
		return dto.Fail[*dto.LotteryOrderInfoDTO](
			errs.HandlerNotFound.Code,
			fmt.Sprintf("未找到消费处理器 type=%s", consumeOrder.ConsumeType),
		).WithError(fmt.Errorf("handler not found"))
	}
	consumeResult := handler.Handle(ctx, req.LotteryActivity, consumeOrder)
	//2.1消费明确失败的话，返回异常
	if !consumeResult.IsSuccess() {
		log.Errorf("消费失败 consumeID=%s", consumeOrder.ConsumeId)
		//组装refundOrder
		refundOrder := ToRefund(order, consumeOrder)
		h.updateOrderFailAndInsert(refundOrder)
		return dto.Fail[*dto.LotteryOrderInfoDTO](errs.Failure.Code, "扣费失败,订单异常").WithError(consumeResult.Err)
	}
	//3.调用抽奖方法
	lotteryDto := req.LotteryInfoReqDTO
	LotteryPresents, drawErr := Draw(ctx, req.OpenId, cast.ToUint64(req.PlatUid), order.OrderId, order.BizTimestamp,
		lotteryDto.LotteryId, lotteryDto.LotteryPoolId, lotteryDto.LotteryNum)
	if drawErr != nil || len(LotteryPresents) == 0 {
		log.Errorf("抽奖失败 lotteryId=%d", req.LotteryInfoReqDTO.LotteryId)
		return h.handleLotteryFailure(order, consumeOrder, drawErr)
	}
	presentList := ToPresents(LotteryPresents, order)
	//3.2抽奖成功则写入发奖记录
	if insertErr := BatchInsertPresents(presentList); insertErr != nil {
		return h.handlePresentInsertFailure(order, consumeOrder, insertErr)
	}

	allSuccess := true
	var lastErr error
	for _, present := range presentList {
		//根据prizeType调用不同的assetPresentHandler
		presentHandler, ok := h.presentDispatcher.GetHandler(present.PrizeType)
		if !ok {
			// 处理未找到情况
			lastErr = fmt.Errorf("未找到资产处理器 type=%s", present.PrizeType)
			log.Errorf("未找到资产处理器 type=%s", present.PrizeType)
			allSuccess = false
			continue
		}
		// 执行资产发放
		result := presentHandler.Handle(ctx, req.LotteryActivity, present)
		if !result.IsSuccess() {
			allSuccess = false
			lastErr = result.Err
			log.Errorf("资产发放失败 presentID=%s, error=%v", present.PresentId, result.Err)
		}
	}

	if !allSuccess {
		//部分奖品发放失败，待补单
		return dto.Fail[*dto.LotteryOrderInfoDTO](
			errs.AssetPresentFailed.Code,
			"部分奖品发放失败，待补单",
		).WithError(lastErr)

	}
	//更新订单状态为成功
	xx, _ := time.LoadLocation(shanghaiLocName)
	localTime := time.Unix(req.BizTime, 0).In(xx)
	// 格式化为 yyyyMM
	monthStr := localTime.Format(monthFormat)
	dao.LotteryOrderDao.UpdateWithSuccess(ctx, monthStr, req.OrderID)

	presents := lo.Map(presentList, func(p *model.Present, index int) dto.LotteryPresentRespDTO {
		return dto.LotteryPresentRespDTO{
			PresentId:  p.PresentId,
			PrizeId:    int32(p.PrizeId),
			PrizeName:  p.PrizeName,
			PrizeNum:   p.PrizeNum,
			PrizePic:   p.PrizePic,
			ExpireTime: p.ExpireTime,
		}
	})

	return dto.Success(&dto.LotteryOrderInfoDTO{
		OrderID:        order.OrderId,
		Status:         constant.Success.Status,
		PresentRespDTO: presents,
		ConsumeRespDTO: []dto.LotteryConsumeRespDTO{},
		CreateTime:     order.CreateTime,
	})

}

func BatchInsertPresents(presents []*model.Present) error {
	loc, _ := time.LoadLocation(shanghaiLocName)
	localTime := time.Unix(presents[0].BizTimestamp, 0).In(loc)
	monthStr := localTime.Format(monthFormat)
	return dao.LotteryPresentDao.BatchInsert(monthStr, presents, dao.GetDb())
}

func (h *ConsumeAndAwardOrderService) handlePresentInsertFailure(order *model.Order, consumeOrder *model.Consume, err error) *dto.Result[*dto.LotteryOrderInfoDTO] {
	refundOrder := ToRefund(order, consumeOrder)
	if refundErr := h.updateOrderFailAndInsert(refundOrder); refundErr != nil {
		return dto.Fail[*dto.LotteryOrderInfoDTO](
			errs.DBAccessError.Code,
			"奖品记录异常且退款失败",
		).WithError(fmt.Errorf("insert error: %v, refund error: %v", err, refundErr))
	}
	return dto.Fail[*dto.LotteryOrderInfoDTO](
		errs.LotteryFailedRefund.Code,
		"奖品记录异常，,订单退款",
	).WithError(err)
}

func (h *ConsumeAndAwardOrderService) handleLotteryFailure(order *model.Order, consumeOrder *model.Consume, err error) *dto.Result[*dto.LotteryOrderInfoDTO] {
	refundOrder := ToRefund(order, consumeOrder)
	if refundErr := h.updateOrderFailAndInsert(refundOrder); refundErr != nil {
		return dto.Fail[*dto.LotteryOrderInfoDTO](
			errs.DBAccessError.Code,
			"抽奖失败且退款处理异常",
		).WithError(fmt.Errorf("lottery error: %v, refund error: %v", err, refundErr))
	}
	return dto.Fail[*dto.LotteryOrderInfoDTO](
		errs.LotteryFailedRefund.Code,
		"抽奖失败,订单退款",
	).WithError(err)
}

func toConsumeOrder(req *dto.LotteryOrderReqDTO, current time.Time) *model.Consume {
	mapExt := req.MapExt
	if mapExt == nil {
		mapExt = make(map[string]string)
	}
	lotteryId := req.LotteryInfoReqDTO.LotteryId
	if _, exists := mapExt["lotteryId"]; !exists && lotteryId > 0 {
		mapExt["lotteryId"] = cast.ToString(lotteryId)
	}
	return &model.Consume{
		ConsumeId:   req.ConsumeReqDTO.ConsumeID,
		OrderId:     req.OrderID,
		OrderTime:   req.BizTime,
		AccessId:    req.AccessId,
		AppId:       req.AppID,
		PlatUid:     cast.ToUint64(req.PlatUid),
		ConsumeType: req.ConsumeReqDTO.ConsumeType,
		AssetId:     req.ConsumeReqDTO.AssetID,
		AssetNum:    req.ConsumeReqDTO.AssetNum,
		MapExt:      utils.ToJSONIgnoreError(req.MapExt),
		CreateTime:  current, // 系统记录时间
		UpdateTime:  current,
	}
}

func ToPresents(
	prizes []*model.LotteryPresent,
	order *model.Order,
) []*model.Present {
	var presents []*model.Present
	now := time.Now()

	for _, prize := range prizes {
		present := model.Present{
			PresentId:    strconv.FormatInt(prize.PresentId, 10),
			OrderId:      order.OrderId,
			AccessId:     order.AccessId,
			AppId:        order.AppId,
			PlatUid:      order.PlatUid,
			OpenId:       order.OpenId,
			PrizeId:      prize.PrizeId,
			PrizeName:    prize.PrizeName,
			PrizeNum:     prize.PrizeNum,
			PrizePic:     prize.PrizePic,
			PrizeType:    prize.PrizeType,
			ExpireTime:   prize.ExpireTime,
			BizTimestamp: order.BizTimestamp,
			CreateTime:   now,
			UpdateTime:   now,
			MapExt:       order.MapExt,
		}

		presents = append(presents, &present)
	}

	return presents
}

func ToRefund(order *model.Order, consume *model.Consume) *model.RefundOrder {
	now := time.Now()
	return &model.RefundOrder{
		RefundId:     strconv.FormatInt(utils.GenerateID(), 10),
		OrderId:      order.OrderId,
		AppId:        order.AppId,
		AccessId:     order.AccessId,
		PlatUid:      order.PlatUid,
		OpenId:       order.OpenId,
		ConsumeId:    consume.ConsumeId,
		ConsumeType:  consume.ConsumeType,
		AssetId:      consume.AssetId,
		AssetNum:     consume.AssetNum,
		BizTimestamp: order.BizTimestamp,
		CreateTime:   now,
		UpdateTime:   now,
		MapExt:       order.MapExt,
	}
}
