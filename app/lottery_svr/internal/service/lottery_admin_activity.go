package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/errs"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/utils"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/lottery/admin"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"encoding/json"
	"github.com/dromara/carbon/v2"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"strings"
	"time"
)

func (s *Service) StaffSearch(ctx context.Context, req *lottery_admin.StaffSearchReq) (*lottery_admin.StaffSearchRsp, error) {
	staffSearchResponse, err := s.InvokeStaffSearch(req.Kw)
	if err != nil || staffSearchResponse == nil {
		return errs.Response[lottery_admin.StaffSearchRsp](errs.Failure), nil
	}
	data := lo.Map(staffSearchResponse.Data, func(item model.StaffSearchData, index int) *lottery_admin.StaffInfoData {
		return &lottery_admin.StaffInfoData{
			Emplid:      item.EmplID,
			NameAc:      item.NameAC,
			NameFormal:  item.NameFormal,
			NameDisplay: item.NameDisplay,
			TEmailBusn:  item.EmailBusiness,
		}
	})
	rsp := &lottery_admin.StaffSearchRsp{
		Code: staffSearchResponse.Code,
		Msg:  staffSearchResponse.Msg,
		Data: data,
	}
	return rsp, nil
}

func (s *Service) QueryRewardInfo(ctx context.Context, req *lottery_admin.QueryRewardInfoReq) (*lottery_admin.QueryRewardInfoRsp, error) {
	rewardId := cast.ToInt64(req.RewardId)
	rsp, err := s.GetRewardInfo([]int64{rewardId})
	if err != nil {
		return nil, err
	}
	rewardConfig, ok := rsp.RewardInfo[rewardId]
	if !ok {
		return errs.Response[lottery_admin.QueryRewardInfoRsp](errs.RewardIdNotFound), nil
	}
	rewards := rewardConfig.Rewards
	marshal, err := json.Marshal(rewards)
	if err != nil {
		return errs.Response[lottery_admin.QueryRewardInfoRsp](errs.RewardIdNotFound), nil
	}
	return &lottery_admin.QueryRewardInfoRsp{
		Code: 0,
		Msg:  "操作成功",
		Data: string(marshal),
	}, nil
}

func (c *Service) GetDropList(ctx context.Context, req *lottery_admin.GetDropListReq) (*lottery_admin.GetDropListRsp, error) {
	platCode := req.PlatCode
	dropName := req.DropName
	var items []*lottery_admin.DropItem
	if strings.EqualFold("PLAT_TYPE", dropName) {
		items = lo.Map(lo.Values(constant.PlatIdInfos), func(item constant.PlatInfo, index int) *lottery_admin.DropItem {
			return &lottery_admin.DropItem{
				Id:   int32(item.Id),
				Code: item.Code,
				Name: item.Name,
			}
		})
	}
	if strings.EqualFold("PRIZE_TYPE", dropName) {
		types := lo.Filter(lo.Values(constant.PrizeTypeInfos), func(item constant.PrizeTypeInfo, index int) bool {
			return lo.Contains(item.PlatCode, platCode)
		})
		items = lo.Map(types, func(item constant.PrizeTypeInfo, index int) *lottery_admin.DropItem {
			return &lottery_admin.DropItem{
				Id:   int32(item.Id),
				Code: item.Code,
				Name: item.Name,
			}
		})
	}
	if strings.EqualFold("MODEL_TYPE", dropName) {
		items = lo.Map(lo.Values(PoolModelInfos), func(item PoolModelInfo, index int) *lottery_admin.DropItem {
			return &lottery_admin.DropItem{
				Id:   int32(item.Id),
				Code: item.Code,
				Name: item.Name,
			}
		})
	}
	rsp := &lottery_admin.GetDropListRsp{
		Code:  0,
		Msg:   "操作成功",
		Items: items,
	}
	return rsp, nil
}

func (s *Service) CreateActivity(ctx context.Context, req *lottery_admin.CreateActivityReq) (*lottery_admin.CreateActivityRsp, error) {
	commonParam, err := s.GetCommonParam(ctx)
	if err != nil {
		log.Warnf("创建抽奖活动配置，解析公共参数失败。error(%v)", err)
		return errs.Response[lottery_admin.CreateActivityRsp](errs.CommonParamError), nil
	}
	log.Warnf("创建抽奖活动配置，公共参数。commonParam:%+v", utils.ToJSONIgnoreError(commonParam))
	haveRight, err := s.CheckMethodRight(ctx)
	if !haveRight {
		log.Warnf("创建抽奖活动配置，缺少创建接口权限。haveRight:%v, err:%v", haveRight, err)
		return errs.Response[lottery_admin.CreateActivityRsp](errs.ActivityRightError), nil
	}
	// 检查活动时间
	startTime := carbon.Parse(req.StartTime)
	endTime := carbon.Parse(req.EndTime)
	if startTime.Gt(endTime) {
		log.Warnf("创建抽奖活动配置，开始时间不能晚于结束时间。startTime:%v, endTime:%v", startTime, endTime)
		return errs.Response[lottery_admin.CreateActivityRsp](errs.InvalidTimeRange), nil
	}
	// 检查平台代号
	platIdInfo, exist := constant.GetPlatInfoByCode(req.PlatCode)
	if !exist {
		log.Warnf("创建抽奖活动配置，平台代号错误。req:%+v", req)
		return errs.Response[lottery_admin.CreateActivityRsp](errs.PlatCodeNotFound), nil
	}
	// 检查抽奖管理员
	lotteryAdmin := req.LotteryAdmin
	validAdmin := s.checkStaffAccount(lotteryAdmin)
	if !validAdmin {
		log.Warnf("创建抽奖活动配置，抽奖管理员错误。req:%+v", req)
		return errs.Response[lottery_admin.CreateActivityRsp](errs.AdminNotFound), nil
	}
	// 检查抽奖奖池
	poolInfos := req.PoolInfos
	log.Warnf("修改抽奖活动配置，奖池信息。poolInfos:%+v", poolInfos)
	invalidPoolInfos := s.checkLotteryPool(poolInfos)
	if len(invalidPoolInfos) > 0 {
		log.Warnf("修改抽奖活动配置，奖池模型错误。invalidPoolInfos:%+v", invalidPoolInfos)
		poolInfo, _ := lo.First(invalidPoolInfos)
		return errs.Response[lottery_admin.CreateActivityRsp](errs.InvalidRequest, "奖池模型配置错误，奖池:"+poolInfo.PoolId), nil
	}
	poolIdMap := make(map[string]bool)
	hasDuplicate := false
	lo.ForEach(poolInfos, func(item *lottery_admin.PoolInfo, index int) {
		if utils.IsBlank(item.PoolId) {
			item.PoolId = cast.ToString(s.GenGlobalId())
		}
		if _, exists := poolIdMap[item.PoolId]; exists {
			hasDuplicate = true
		}
		poolIdMap[item.PoolId] = true
	})
	if hasDuplicate {
		log.Warnf("创建抽奖活动配置，存在重复的PoolId。req:%+v", req)
		return errs.Response[lottery_admin.CreateActivityRsp](errs.DuplicatePoolId), nil
	}
	marshal, err := json.Marshal(poolInfos)
	if err != nil {
		return errs.Response[lottery_admin.CreateActivityRsp](errs.PlatCodeNotFound), nil
	}
	lotteryId := int32(0)
	if utils.IsNotBlank(req.ActivityId) {
		newLotteryId, err := cast.ToInt32E(req.ActivityId)
		if err != nil || !platIdInfo.IsValidLotteryId(newLotteryId) {
			log.Warnf("创建抽奖活动配置，输入的抽奖ID错误。req:%+v", req)
			return errs.Response[lottery_admin.CreateActivityRsp](errs.ActivityIdInvalid, platIdInfo.GetLotteryIdRangeMsg()), nil
		}
		lotteryId = newLotteryId
	} else {
		maxLotteryId, err := dao.LotteryActivityDao.GetMaxLotteryId(ctx, platIdInfo.Code)
		if err != nil {
			log.Warnf("创建抽奖活动配置，查询最大的LotteryId失败。req:%+v", req)
			return errs.Response[lottery_admin.CreateActivityRsp](errs.DuplicatePoolId), nil
		}
		newLotteryId := platIdInfo.GetNextLotteryId(maxLotteryId)
		lotteryId = newLotteryId
	}
	if lotteryId <= 0 {
		log.Warnf("创建抽奖活动配置，生成的LotteryId无效。lotteryId:%d", lotteryId)
		return errs.Response[lottery_admin.CreateActivityRsp](errs.ActivityIdInvalid), nil
	}
	activity := &model.LotteryActivity{
		LotteryId:     lotteryId,
		PlatCode:      platIdInfo.Code,
		ActivityName:  req.ActivityName,
		ActivityDesc:  req.ActivityDesc,
		StartTime:     startTime.StdTime(),
		EndTime:       endTime.StdTime(),
		Status:        1,
		PoolInfo:      string(marshal),
		BossAccountId: req.BossAccountId,
		BossAppId:     req.BossAppId,
		BossAppSecret: req.BossAppSecret,
		BossBid:       req.BossBid,
		CreateUser:    commonParam.UserInfo.Ename,
		UpdateUser:    commonParam.UserInfo.Ename,
		LotteryAdmin:  lotteryAdmin,
		CreateTime:    time.Now(),
		UpdateTime:    time.Now(),
	}
	rowsAffected, err := dao.LotteryActivityDao.InsertLotteryActivityIgnore(ctx, activity)
	if err != nil || rowsAffected < 1 {
		log.Errorf("InsertLotteryActivityIgnore rowsAffected:%v, err:%v", rowsAffected, err)
		return errs.Response[lottery_admin.CreateActivityRsp](errs.LotteryIdDuplicate), nil
	}
	return &lottery_admin.CreateActivityRsp{
		Code:       0,
		Msg:        "操作成功",
		ActivityId: cast.ToString(activity.LotteryId),
	}, nil
}

func (s *Service) QueryActivity(ctx context.Context, req *lottery_admin.QueryActivityReq) (*lottery_admin.QueryActivityRsp, error) {
	commonParam, err := s.GetCommonParam(ctx)
	if err != nil {
		log.Warnf("查询抽奖活动配置，解析公共参数失败。error(%v)", err)
		return errs.Response[lottery_admin.QueryActivityRsp](errs.CommonParamError), nil
	}
	log.Warnf("查询抽奖活动配置，公共参数。commonParam:%+v", utils.ToJSONIgnoreError(commonParam))
	// 检查平台代号
	platInfo, exist := constant.GetPlatInfoByCode(req.PlatCode)
	if !exist {
		log.Warnf("查询抽奖活动配置，平台代号错误。req:%+v", req)
		return errs.Response[lottery_admin.QueryActivityRsp](errs.PlatCodeNotFound), nil
	}
	// 分页参数检查
	pagination, err := model.NewPagination(req.Page, req.PageSize)
	if err != nil {
		log.Warnf("查询抽奖活动配置，分页参数错误。req:%+v", req)
		return errs.Response[lottery_admin.QueryActivityRsp](errs.InvalidRequest), nil
	}
	// 查询奖项信息
	activities, totalNum, err := dao.LotteryActivityDao.GetLotteryActivities(ctx, platInfo.Code,
		req.ActivityId, req.ActivityName, req.ActivityStatus, pagination)
	if err != nil {
		log.Error("查询抽奖活动配置，查询数据失败。error(%v)", err)
		return errs.Response[lottery_admin.QueryActivityRsp](errs.InvalidRequest), nil
	}
	activityList := lo.Map(activities, func(item *model.LotteryActivity, index int) *lottery_admin.Activity {
		var poolInfos []*lottery_admin.PoolInfo
		err := json.Unmarshal([]byte(item.PoolInfo), &poolInfos)
		if err != nil {
			return nil
		}
		return &lottery_admin.Activity{
			PlatCode:      platInfo.Code,
			PlatName:      platInfo.Name,
			ActivityId:    cast.ToString(item.LotteryId),
			ActivityName:  item.ActivityName,
			ActivityDesc:  item.ActivityDesc,
			Status:        int32(item.Status),
			StartTime:     cast.ToString(item.StartTime.Unix()),
			EndTime:       cast.ToString(item.EndTime.Unix()),
			PoolInfos:     poolInfos,
			BossAccountId: item.BossAccountId,
			BossBid:       item.BossBid,
			BossAppId:     item.BossAppId,
			BossAppSecret: item.BossAppSecret,
			LotteryAdmin:  item.LotteryAdmin,
			CreateUser:    item.CreateUser,
			UpdateUser:    item.UpdateUser,
			CreateTime:    cast.ToString(item.CreateTime.Unix()),
			UpdateTime:    cast.ToString(item.UpdateTime.Unix()),
		}
	})
	rsp := &lottery_admin.QueryActivityRsp{
		Total:      totalNum,
		Activities: activityList,
	}
	return rsp, nil
}

func (s *Service) ModifyActivity(ctx context.Context, req *lottery_admin.ModifyActivityReq) (*lottery_admin.ModifyActivityRsp, error) {
	// 解析公共参数
	commonParam, err := s.GetCommonParam(ctx)
	if err != nil {
		log.Warnf("修改抽奖活动配置，解析公共参数失败。error(%v)", err)
		return errs.Response[lottery_admin.ModifyActivityRsp](errs.CommonParamError), nil
	}
	// 检查活动配置
	activity, err := dao.LotteryActivityDao.GetLotteryActivityById(ctx, req.ActivityId)
	if err != nil || activity == nil {
		log.Warnf("修改抽奖活动配置，修改活动不存在。req:%+v, err:%v", req, err)
		return errs.Response[lottery_admin.ModifyActivityRsp](errs.ActivityNotFound), nil
	}
	// 检查职员权限
	hasPermission, err := s.checkStaffPermission(activity, commonParam.UserInfo.Ename)
	if err != nil || !hasPermission {
		log.Warnf("修改抽奖活动配置，无权限修改抽奖配置。error(%v)", err)
		return errs.Response[lottery_admin.ModifyActivityRsp](errs.HasNoPermission), nil
	}
	// 检查平台代号
	platIdInfo, exist := constant.GetPlatInfoByCode(req.PlatCode)
	if !exist {
		log.Warnf("修改抽奖活动配置，平台代号错误。req:%+v", req)
		return errs.Response[lottery_admin.ModifyActivityRsp](errs.PlatCodeNotFound), nil
	}
	// 检查奖池标识（生成PoolId并检查重复）
	poolInfos := req.PoolInfos
	poolIdMap := make(map[string]bool)
	hasDuplicate := false
	lo.ForEach(poolInfos, func(item *lottery_admin.PoolInfo, index int) {
		if utils.IsBlank(item.PoolId) {
			item.PoolId = cast.ToString(s.GenGlobalId())
		}
		if _, exists := poolIdMap[item.PoolId]; exists {
			hasDuplicate = true
		}
		poolIdMap[item.PoolId] = true
	})
	if hasDuplicate {
		log.Warnf("修改抽奖活动配置，存在重复的PoolId。req:%+v", req)
		return errs.Response[lottery_admin.ModifyActivityRsp](errs.DuplicatePoolId), nil
	}
	// 检查抽奖奖池
	log.Warnf("修改抽奖活动配置，奖池信息。poolInfos:%+v", poolInfos)
	invalidPoolInfos := s.checkLotteryPool(poolInfos)
	if len(invalidPoolInfos) > 0 {
		log.Warnf("修改抽奖活动配置，奖池模型错误。invalidPoolInfos:%+v", invalidPoolInfos)
		return errs.Response[lottery_admin.ModifyActivityRsp](errs.InvalidRequest), nil
	}
	// 检查专属管理员
	//validAdmin := s.checkStaffAccount(req.LotteryAdmin)
	//if !validAdmin {
	//	log.Warnf("修改抽奖活动配置，抽奖管理员错误。req:%+v", req)
	//	return errs.Response[lottery_admin.ModifyActivityRsp](errs.AdminNotFound), nil
	//}
	// 保存活动信息
	marshal, err := json.Marshal(req.PoolInfos)
	if err != nil {
		log.Warnf("修改抽奖活动配置，平台代号错误。req:%+v", req)
		return errs.Response[lottery_admin.ModifyActivityRsp](errs.Failure), nil
	}
	update := &model.LotteryActivity{
		LotteryId:     req.ActivityId,
		PlatCode:      platIdInfo.Code,
		ActivityName:  req.ActivityName,
		ActivityDesc:  req.ActivityDesc,
		StartTime:     carbon.Parse(req.StartTime).StdTime(),
		EndTime:       carbon.Parse(req.EndTime).StdTime(),
		Status:        int8(req.Status),
		UpdateTime:    time.Now(),
		UpdateUser:    commonParam.UserInfo.Ename,
		PoolInfo:      string(marshal),
		BossBid:       req.BossBid,
		BossAppId:     req.BossAppId,
		BossAppSecret: req.BossAppSecret,
		BossAccountId: req.BossAccountId,
		LotteryAdmin:  req.LotteryAdmin,
	}
	rowsAffected, err := dao.LotteryActivityDao.UpdateLotteryActivityById(ctx, update)
	if err != nil || rowsAffected < 1 {
		log.Error("UpdateLotteryActivityById error(%v), rowsAffected:%v", rowsAffected, err)
		return errs.Response[lottery_admin.ModifyActivityRsp](errs.Failure), nil
	}
	return errs.Response[lottery_admin.ModifyActivityRsp](errs.Success), nil
}

func (s *Service) checkLotteryPool(poolInfos []*lottery_admin.PoolInfo) []*lottery_admin.PoolInfo {
	// 检查奖池模型
	invalidPoolInfos := lo.Filter(poolInfos, func(item *lottery_admin.PoolInfo, index int) bool {
		// 初始化奖项ID
		lo.ForEach(item.PrizeList, func(item *lottery_admin.PrizeItem, index int) {
			prizeItemId := cast.ToInt64(item.PrizeItemId)
			if prizeItemId <= 0 {
				item.PrizeItemId = cast.ToString(s.GenGlobalId())
			}
		})
		// 获取奖池模型
		poolModelInfo, exist := GetPoolModelInfoByCode(item.PoolModel)
		if !exist {
			log.Warnf("修改抽奖活动配置，奖池模型错误。item:%+v", item)
			return true
		}
		// 检查奖池配置
		valid := poolModelInfo.Check(item)
		if !valid {
			return true
		}
		return false
	})
	return invalidPoolInfos
}
