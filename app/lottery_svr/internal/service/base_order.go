package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/errs"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"fmt"
)

type BaseOrderService struct {
	orderHandler LotteryOrderService
}

func NewBaseOrderService(orderHandler LotteryOrderService) *BaseOrderService {
	return &BaseOrderService{
		orderHandler: orderHandler,
	}
}

type LotteryOrderService interface {
	GetOrderType() int32
	DoBiz(ctx context.Context, req *dto.LotteryOrderReqDTO) *dto.Result[*dto.LotteryOrderInfoDTO]
}

func (s *BaseOrderService) Execute(ctx context.Context, req *dto.LotteryOrderReqDTO) *dto.Result[*dto.LotteryOrderInfoDTO] {
	// 幂等检查
	//支持跨月查询订单信息
	preOrder, err := s.getByOrderIdSupportCrossMonth(ctx, req.BizTime, req.OrderID)
	if err != nil {
		return dto.Fail[*dto.LotteryOrderInfoDTO](
			errs.ParamError.Code,
			errs.ParamError.Msg,
		).WithError(err)
	}
	expectOrderType := s.orderHandler.GetOrderType()
	log.Warnf("req: %+v, expectOrderType: %d, preOrder: %+v", req, expectOrderType, preOrder)
	if preOrder != nil {
		if err := checkConsistency(preOrder, expectOrderType); err != nil {
			return dto.Fail[*dto.LotteryOrderInfoDTO](
				errs.IdempotentOrderTypeErr.Code,
				err.Error(),
			)
		}
		switch preOrder.Status {
		case constant.Success.Status:
			{
				//查询扣费订单信息
				//t := time.Unix(preOrder.BizTimestamp, 0).UTC()
				//currentMonth := t.Format(dateLayout)
				//consumeList, _ := s.dao.SelectConsumeListByOrderId(ctx, currentMonth, preOrder.OrderID)
				//presentList, _ := s.dao.SelectPresentListByOrderId(ctx, currentMonth, preOrder.OrderID)
				//查询发奖订单信息
				return buildExistResult(preOrder)
			}
		case constant.Init.Status:
			return dto.Fail[*dto.LotteryOrderInfoDTO](
				errs.Processing.Code,
				"订单处理中，请稍后查询",
			)
		case constant.Fail.Status:
			return dto.Fail[*dto.LotteryOrderInfoDTO](
				errs.Failure.Code,
				"订单处理失败，可重新提交",
			)
		default:
			return dto.Fail[*dto.LotteryOrderInfoDTO](
				errs.NetworkError.Code,
				fmt.Sprintf("未知订单状态: %d", preOrder.Status),
			)
		}
	}
	return s.orderHandler.DoBiz(ctx, req)

}

// 构建存在订单的响应
func buildExistResult(order *dto.OrderDTO) *dto.Result[*dto.LotteryOrderInfoDTO] {

	return dto.Success(&dto.LotteryOrderInfoDTO{
		OrderID: order.OrderID,
		Status:  order.Status,
		//Presents: presents,
		//	Consumes:   consumes,
		CreateTime: order.CreateTime,
	})
}

// 参数一致性检查
func checkConsistency(preOrder *dto.OrderDTO, expectOrderType int32) error {
	if preOrder.OrderType != expectOrderType {
		return fmt.Errorf("订单类型不一致 (已有:%d 当前:%d)", preOrder.OrderType, expectOrderType)
	}
	return nil
}
