package service

import (
	corev1 "cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/grpc-party/gen-go/asset/api/v1"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/errs"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/utils"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/adapter_unified_assets/callback"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/lottery"
	lottery_admin "cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/lottery/admin"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"encoding/json"
	"github.com/dromara/carbon/v2"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"time"
)

// QueryLottery 查询抽奖券
func (s *Service) QueryLottery(ctx context.Context, req *lottery.QueryLotteryReq) (*lottery.QueryLotteryRsp, error) {
	lotteryId, err := cast.ToInt32E(req.LotteryId)
	if err != nil {
		log.Errorf("查询抽奖活动配置，参数解析失败。req:%+v, err:%v", req, err)
		return errs.Response[lottery.QueryLotteryRsp](errs.ParamError), nil
	}
	lotteryActivity, err := dao.LotteryActivityDao.GetLotteryActivityById(ctx, lotteryId)
	if err != nil || lotteryActivity == nil {
		log.Errorf("查询抽奖活动配置，查询抽奖失败。req:%+v, err:%v", req, err)
		return errs.Response[lottery.QueryLotteryRsp](errs.ActivityNotFound), nil
	}
	// 检查平台代号
	platInfo, exist := constant.GetPlatInfoByCode(lotteryActivity.PlatCode)
	if !exist {
		log.Warnf("查询抽奖活动配置，平台代号错误。req:%+v", req)
		return errs.Response[lottery.QueryLotteryRsp](errs.PlatCodeNotFound), nil
	}
	// 解析奖池信息
	var poolInfos []*lottery_admin.PoolInfo
	err = json.Unmarshal([]byte(lotteryActivity.PoolInfo), &poolInfos)
	if err != nil {
		log.Warnf("查询抽奖活动配置，解析奖池错误。req:%+v, err:%v", req, err)
		return errs.Response[lottery.QueryLotteryRsp](errs.ParsePoolFailure), nil
	}
	// 设置PrizeTypeId
	lo.ForEach(poolInfos, func(item *lottery_admin.PoolInfo, index int) {
		lo.ForEach(item.PrizeList, func(prize *lottery_admin.PrizeItem, index int) {
			prizeType := prize.PrizeType
			prizeTypeInfo, exist := constant.GetPrizeTypeInfoByCode(prizeType)
			if exist || prizeTypeInfo != nil {
				prize.PrizeTypeId = int32(prizeTypeInfo.Id)
			}
		})
	})
	activity := &lottery_admin.Activity{
		PlatCode:      platInfo.Code,
		PlatName:      platInfo.Name,
		ActivityId:    cast.ToString(lotteryActivity.LotteryId),
		ActivityName:  lotteryActivity.ActivityName,
		ActivityDesc:  lotteryActivity.ActivityDesc,
		Status:        int32(lotteryActivity.Status),
		StartTime:     cast.ToString(lotteryActivity.StartTime.Unix()),
		EndTime:       cast.ToString(lotteryActivity.EndTime.Unix()),
		PoolInfos:     poolInfos,
		BossAccountId: lotteryActivity.BossAccountId,
		BossBid:       lotteryActivity.BossBid,
		BossAppId:     lotteryActivity.BossAppId,
		BossAppSecret: lotteryActivity.BossAppSecret,
		LotteryAdmin:  lotteryActivity.LotteryAdmin,
		CreateUser:    lotteryActivity.CreateUser,
		UpdateUser:    lotteryActivity.UpdateUser,
		CreateTime:    cast.ToString(lotteryActivity.CreateTime.Unix()),
		UpdateTime:    cast.ToString(lotteryActivity.UpdateTime.Unix()),
	}
	rsp := &lottery.QueryLotteryRsp{
		Code: errs.Success.Code,
		Msg:  errs.Success.Msg,
		Data: activity,
	}
	return rsp, nil
}

func (s *Service) CallbackCheckSend(ctx context.Context, req *callback.GiftPackageBusinessCheckSendReq) (*callback.GiftPackageBusinessCheckSendRsp, error) {
	rsp := &callback.GiftPackageBusinessCheckSendRsp{
		Pass: false,
	}
	defer log.Infof("CallbackCheckSend invoked, req=%+v, rsp=%+v", req, rsp)
	orderId := req.CheckInfo.OrderId
	month := carbon.Now().Format("Ym")
	order, err := dao.LotteryPresentDao.FindPresentByOrderId(ctx, month, orderId)
	if err != nil {
		log.Errorf("CallbackCheckSend failed,req:%+v, err:%v", req, err)
		return rsp, status.Error(codes.Code(errs.OrderRecheckFail.Code), "CallbackCheckSend fail")
	}
	if order == nil {
		month := carbon.Now().SubMonth().Format("Ym")
		order, err = dao.LotteryPresentDao.FindPresentByOrderId(ctx, month, orderId)
		if err != nil {
			log.Errorf("CallbackCheckSend failed,req:%+v, err:%v", req, err)
			return rsp, status.Error(codes.Code(errs.OrderRecheckFail.Code), "CallbackCheckSend fail")
		}
	}
	rsp.Pass = order == nil
	return rsp, nil
}

// Lottery 抽奖
func (s *Service) Lottery(ctx context.Context, req *lottery.LotteryReq) (*lottery.LotteryRsp, error) {
	log.Warnf("纯抽奖，查询抽奖消耗抽奖券数量，请求参数。req：%+v", utils.ToJSONIgnoreError(req))
	platUid, err := s.Openid2PlatUid(ctx, req.AppId, req.OpenId)
	if err != nil || platUid < 1 {
		log.Warnf("纯抽奖，获取用户openid失败。req:%+v, err:%v", req, err)
		return errs.Response[lottery.LotteryRsp](errs.OpenIdNotFound), nil
	}
	// 检查抽奖配置
	lotteryId, err := cast.ToInt32E(req.LotteryInfo.LotteryId)
	if err != nil {
		log.Warnf("纯抽奖，请求参数错误。req:%+v, err:%v", req, err)
		return errs.Response[lottery.LotteryRsp](errs.InvalidRequest), nil
	}
	lotteryActivity, err := dao.LotteryActivityDao.GetLotteryActivityById(ctx, lotteryId)
	if err != nil || lotteryActivity == nil {
		log.Errorf("纯抽奖，抽奖配置不存在。req:%+v, err:%v", req, err)
		return errs.Response[lottery.LotteryRsp](errs.ActivityNotFound), nil
	}
	accessId := lo.ValueOr(req.MapExt, "access_id", "")
	lotteryInfoReq := &dto.LotteryInfoReqDTO{
		AccessId:      accessId,
		AppID:         req.AppId,
		PlatID:        req.PlatId,
		OrderID:       req.OrderId,
		BizTime:       req.BizTimestamp,
		UserID:        cast.ToString(platUid),
		LotteryId:     cast.ToInt32(req.LotteryInfo.LotteryId),
		LotteryPoolId: req.LotteryInfo.LotteryPoolId,
		LotteryNum:    req.LotteryInfo.LotteryNum,
	}
	log.Warnf("纯抽奖，抽奖信息请求参数: %+v", lotteryInfoReq)
	// 组装扣减参数
	orderReq := &dto.LotteryOrderReqDTO{
		AppID:             req.AppId,
		AccessId:          accessId,
		OrderID:           req.OrderId,
		OrderType:         string(int32(constant.OrderTypeLottery)),
		PlatUid:           cast.ToString(platUid),
		OpenId:            req.OpenId,
		BizTime:           req.BizTimestamp,
		LotteryInfoReqDTO: lotteryInfoReq,
		LotteryActivity:   lotteryActivity,
		MapExt:            req.MapExt,
	}
	log.Warnf("纯抽奖，订单请求参数: %+v", orderReq)
	result := NewBaseOrderService(s.lotteryOrderService).Execute(ctx, orderReq)
	if !result.IsSuccess() {
		return errs.Response[lottery.LotteryRsp](errs.Failure), nil
	}
	orderInfo := result.Data
	presentDtoList := orderInfo.PresentRespDTO
	rewardDataList := lo.Map(presentDtoList, func(present dto.LotteryPresentRespDTO, index int) *lottery.RewardOrderInfo {
		return &lottery.RewardOrderInfo{
			RewardOrderId: present.PresentId,
			PrizeId:       present.PrizeId,
			PrizeName:     present.PrizeName,
			PrizeNum:      present.PrizeNum,
			PrizePic:      present.PrizePic,
			Status:        present.Status,
		}
	})
	lotteryRsp := &lottery.LotteryRsp{}
	lotteryRsp.Code = errs.Success.Code
	lotteryRsp.Msg = errs.Success.Msg
	lotteryRsp.Data = &lottery.LotteryData{
		OrderId:        orderInfo.OrderID,
		OrderStatus:    orderInfo.Status,
		RewardDataList: rewardDataList,
	}
	return lotteryRsp, nil
}

// LotteryDelivery 抽奖+发奖
func (s *Service) LotteryDelivery(ctx context.Context, req *lottery.LotteryDeliveryReq) (*lottery.LotteryDeliveryRsp, error) {
	log.Warnf("抽奖发奖，查询抽奖消耗抽奖券数量，请求参数。req：%+v", utils.ToJSONIgnoreError(req))
	platUid, err := s.Openid2PlatUid(ctx, req.AppId, req.OpenId)
	if err != nil || platUid < 1 {
		log.Warnf("抽奖发奖，获取用户openid失败。req:%+v, err:%v", req, err)
		return errs.Response[lottery.LotteryDeliveryRsp](errs.OpenIdNotFound), nil
	}
	// 检查抽奖配置
	lotteryId, err := cast.ToInt32E(req.LotteryInfo.LotteryId)
	if err != nil {
		log.Warnf("抽奖发奖，请求参数错误。req:%+v, err:%v", req, err)
		return errs.Response[lottery.LotteryDeliveryRsp](errs.InvalidRequest), nil
	}
	lotteryActivity, err := dao.LotteryActivityDao.GetLotteryActivityById(ctx, lotteryId)
	if err != nil || lotteryActivity == nil {
		log.Errorf("抽奖发奖，抽奖配置不存在。req:%+v, err:%v", req, err)
		return errs.Response[lottery.LotteryDeliveryRsp](errs.ActivityNotFound), nil
	}
	lotteryInfoReq := &dto.LotteryInfoReqDTO{
		AppID:         req.AppId,
		AccessId:      req.AccessId,
		PlatID:        req.PlatId,
		OrderID:       req.OrderId,
		BizTime:       req.BizTimestamp,
		UserID:        cast.ToString(platUid),
		LotteryId:     cast.ToInt32(req.LotteryInfo.LotteryId),
		LotteryPoolId: req.LotteryInfo.LotteryPoolId,
		LotteryNum:    req.LotteryInfo.LotteryNum,
	}
	log.Warnf("抽奖发奖，抽奖信息请求参数: %+v", lotteryInfoReq)
	// 组装扣减参数
	orderReq := &dto.LotteryOrderReqDTO{
		AppID:             req.AppId,
		AccessId:          req.AccessId,
		OrderID:           req.OrderId,
		OrderType:         string(int32(constant.OrderTypeLotteryDelivery)),
		PlatUid:           cast.ToString(platUid),
		OpenId:            req.OpenId,
		BizTime:           req.BizTimestamp,
		LotteryInfoReqDTO: lotteryInfoReq,
		LotteryActivity:   lotteryActivity,
		MapExt:            req.MapExt,
	}
	log.Warnf("抽奖发奖，订单请求参数: %+v", orderReq)
	result := NewBaseOrderService(s.lotteryDeliveryOrderService).Execute(ctx, orderReq)
	if !result.IsSuccess() {
		return errs.Response[lottery.LotteryDeliveryRsp](errs.Failure), nil
	}
	orderInfo := result.Data
	presentDtoList := orderInfo.PresentRespDTO
	rewardDataList := lo.Map(presentDtoList, func(present dto.LotteryPresentRespDTO, index int) *lottery.RewardOrderInfo {
		return &lottery.RewardOrderInfo{
			RewardOrderId: present.PresentId,
			PrizeId:       present.PrizeId,
			PrizeName:     present.PrizeName,
			PrizeNum:      present.PrizeNum,
			PrizePic:      present.PrizePic,
			Status:        present.Status,
		}
	})
	lotteryDeliveryRsp := &lottery.LotteryDeliveryRsp{}
	lotteryDeliveryRsp.Code = errs.Success.Code
	lotteryDeliveryRsp.Msg = errs.Success.Msg
	lotteryDeliveryRsp.Data = &lottery.LotteryDeliveryData{
		OrderId:        orderInfo.OrderID,
		OrderStatus:    orderInfo.Status,
		RewardDataList: rewardDataList,
	}
	return lotteryDeliveryRsp, nil
}

// DrawLottery 扣抽奖券+抽奖+发奖
func (s *Service) DrawLottery(ctx context.Context, req *lottery.DrawLotteryReq) (*lottery.DrawLotteryRsp, error) {
	log.Warnf("扣抽奖券抽奖发奖，查询抽奖消耗抽奖券数量，请求参数。req：%+v", utils.ToJSONIgnoreError(req))
	platUid, err := s.Openid2PlatUid(ctx, req.AppId, req.OpenId)
	if err != nil || platUid < 1 {
		log.Warnf("扣抽奖券抽奖发奖，获取用户openid失败。err:%v", err)
		return errs.Response[lottery.DrawLotteryRsp](errs.OpenIdNotFound), nil
	}
	baseRsp := &lottery.DrawLotteryRsp{
		Time: time.Now().Unix(),
		Data: &lottery.DrawLotteryData{
			OrderId: req.GetOrderId(),
			PlatUid: platUid,
			OpenId:  req.OpenId,
		},
	}
	// 检查抽奖配置
	lotteryId := cast.ToInt32(req.LotteryInfo.LotteryId)
	lotteryActivity, err := dao.LotteryActivityDao.GetLotteryActivityById(ctx, lotteryId)
	if err != nil || lotteryActivity == nil {
		log.Errorf("扣抽奖券抽奖发奖，抽奖配置不存在。err:%v", err)
		return errs.Response[lottery.DrawLotteryRsp](errs.ActivityNotFound), nil
	}
	// 获取扣减信息
	costResult, err := GetTicketCost(ctx, model.TicketCostReq{
		ActivityId: lotteryId,
		DrawNum:    req.LotteryInfo.LotteryNum,
	})
	if err != nil || costResult == nil {
		log.Errorf("扣抽奖券抽奖发奖，获取扣减信息失败。err:%v", err)
		return errs.Response[lottery.DrawLotteryRsp](errs.TicketNotFound), nil
	}
	log.Warnf("扣减信息获取成功: %+v", costResult)
	lotteryInfoReq := &dto.LotteryInfoReqDTO{
		AppID:         req.AppId,
		AccessId:      req.AccessId,
		PlatID:        req.PlatId,
		OrderID:       req.OrderId,
		UserID:        cast.ToString(platUid),
		LotteryId:     cast.ToInt32(req.LotteryInfo.LotteryId),
		LotteryPoolId: req.LotteryInfo.LotteryPoolId,
		LotteryNum:    req.LotteryInfo.LotteryNum,
		BizTime:       req.BizTimestamp,
	}
	log.Warnf("扣抽奖券抽奖发奖，抽奖信息请求参数: %+v", lotteryInfoReq)
	// 组装扣减参数
	consumeReq := &dto.LotteryConsumeReqDTO{
		ConsumeID:   req.OrderId,
		UserID:      cast.ToString(platUid),
		AssetID:     int64(costResult.TicketId),
		AssetNum:    costResult.TicketNum,
		ConsumeType: costResult.AssetType,
	}
	log.Warnf("扣抽奖券抽奖发奖，扣减请求参数: %+v", consumeReq)

	orderReq := &dto.LotteryOrderReqDTO{
		AppID:             req.AppId,
		AccessId:          req.AccessId,
		PlatUid:           cast.ToString(platUid),
		OpenId:            req.OpenId,
		OrderID:           req.OrderId,
		BizTime:           req.BizTimestamp,
		OrderType:         string(int32(constant.OrderTypeLottery)),
		LotteryInfoReqDTO: lotteryInfoReq,
		ConsumeReqDTO:     consumeReq,
		LotteryActivity:   lotteryActivity,
		MapExt:            req.MapExt,
	}
	log.Warnf("扣抽奖券抽奖发奖，订单请求参数: %+v", orderReq)
	result := NewBaseOrderService(s.consumeAndAwardOrderService).Execute(ctx, orderReq)
	if !result.IsSuccess() {
		log.Warnf("扣抽奖券抽奖发奖，订单处理失败错误码: %d, 错误信息: %s", result.Code, result.Msg)
		baseRsp.Code = result.Code
		baseRsp.Msg = result.Msg
		return baseRsp, nil
	}

	orderInfo := result.Data
	log.Warnf("扣抽奖券抽奖发奖，订单处理成功订单状态: %d", orderInfo.Status)
	baseRsp.Code = errs.Success.Code
	baseRsp.Msg = errs.Success.Msg
	baseRsp.Data.OrderId = orderInfo.OrderID
	baseRsp.Data.OrderStatus = orderInfo.Status
	presentDtoList := orderInfo.PresentRespDTO
	rewardDataList := lo.Map(presentDtoList, func(present dto.LotteryPresentRespDTO, index int) *lottery.RewardOrderInfo {
		return &lottery.RewardOrderInfo{
			RewardOrderId: present.PresentId,
			PrizeId:       present.PrizeId,
			PrizeName:     present.PrizeName,
			PrizeNum:      present.PrizeNum,
			PrizePic:      present.PrizePic,
			Status:        present.Status,
		}
	})
	baseRsp.Data.RewardDataList = rewardDataList
	return baseRsp, nil
}

// SendLotteryTicket 发放抽奖券
func (s *Service) SendLotteryTicket(ctx context.Context, req *lottery.SendLotteryTicketReq) (*lottery.SendLotteryTicketRsp, error) {
	// 检查发放用户
	platUid, err := s.Openid2PlatUid(ctx, req.AppId, req.OpenId)
	if err != nil || platUid <= 0 {
		log.Warnf("发放抽奖券，获取用户openid失败。err:%v", err)
		return errs.Response[lottery.SendLotteryTicketRsp](errs.OpenIdNotFound), nil
	}
	// 检查发放数量
	ticketNum := req.TicketNum
	if ticketNum <= 0 {
		log.Warnf("发放抽奖券，获取用户openid失败。err:%v", err)
		return errs.Response[lottery.SendLotteryTicketRsp](errs.InvalidRequest), nil
	}
	// 检查抽奖配置
	lotteryId := cast.ToInt32(req.LotteryId)
	lotteryActivity, err := dao.LotteryActivityDao.GetLotteryActivityById(ctx, lotteryId)
	if err != nil || lotteryActivity == nil {
		log.Errorf("发放抽奖券，抽奖配置不存在。err:%v", err)
		return errs.Response[lottery.SendLotteryTicketRsp](errs.ActivityNotFound), nil
	}
	// 检查抽奖券配置
	lotteryTicket, err := dao.LotteryTicketDao.GetLotteryTicketByActivityId(ctx, lotteryActivity.LotteryId)
	if err != nil || lotteryTicket == nil {
		log.Errorf("发放抽奖券，抽奖券配置不存在。err:%v", err)
		return errs.Response[lottery.SendLotteryTicketRsp](errs.TicketNotFound), nil
	}
	baseRsp := &lottery.SendLotteryTicketRsp{
		Time: time.Now().Unix(),
		Data: &lottery.SendLotteryTicketData{
			OrderId: req.GetOrderId(),
			PlatUid: platUid,
			OpenId:  req.OpenId,
		},
	}
	// 构建发放请求
	var presentReqs []*dto.LotteryPresentReqDTO
	presentReqs = append(presentReqs, &dto.LotteryPresentReqDTO{
		UserID:     cast.ToString(platUid),
		PrizeID:    int64(lotteryTicket.TicketId),
		PrizeNum:   int64(req.TicketNum),
		PrizeType:  constant.PrizeTypeBossAsset.Code() + "_present",
		BizTime:    req.BizTimestamp,
		ExpireTime: lotteryTicket.ExpireSecs,
	})
	orderReq := &dto.LotteryOrderReqDTO{
		AppID:                req.AppId,
		AccessId:             "",
		OrderID:              req.OrderId,
		OrderType:            string(int32(constant.OrderTypeDeliveryTicket)),
		PlatUid:              cast.ToString(platUid),
		OpenId:               req.OpenId,
		BizTime:              req.BizTimestamp,
		LotteryPresentReqDTO: presentReqs,
		LotteryActivity:      lotteryActivity,
		MapExt:               req.MapExt,
	}
	executeResult := NewBaseOrderService(s.presentOrderService).Execute(ctx, orderReq)
	if !executeResult.IsSuccess() {
		baseRsp.Code = executeResult.Code
		baseRsp.Msg = executeResult.Msg
		return baseRsp, nil
	}
	orderInfo := executeResult.Data
	baseRsp.Code = errs.Success.Code
	baseRsp.Msg = errs.Success.Msg
	baseRsp.Data.OrderStatus = orderInfo.Status
	return baseRsp, nil
}

// QueryLotteryTicket 查询抽奖券
func (s *Service) QueryLotteryTicket(ctx context.Context, req *lottery.QueryLotteryTicketReq) (*lottery.QueryLotteryTicketRsp, error) {
	log.Warnf("查询抽奖券，请求参数。req: %v", req)
	platUid, err := s.Openid2PlatUid(ctx, req.AppId, req.OpenId)
	if err != nil || platUid < 1 {
		log.Warnf("查询抽奖券，获取用户openid失败。err:%v", err)
		return errs.Response[lottery.QueryLotteryTicketRsp](errs.OpenIdNotFound), nil
	}
	// 检查抽奖配置
	lotteryId := cast.ToInt32(req.LotteryId)
	activity, err := dao.LotteryActivityDao.GetLotteryActivityById(ctx, lotteryId)
	if err != nil || activity == nil {
		log.Errorf("查询抽奖券，抽奖配置不存在。err:%v", err)
		return errs.Response[lottery.QueryLotteryTicketRsp](errs.ActivityNotFound), nil
	}
	// 查询抽奖券表
	ticket, err := dao.LotteryTicketDao.GetLotteryTicketByActivityId(ctx, lotteryId)
	if err != nil || ticket == nil {
		log.Errorf("查询抽奖券，抽奖配置不存在。err:%v", err)
		return &lottery.QueryLotteryTicketRsp{
			Assets: []*lottery.UserAsset{},
		}, err
	}
	// 查询Boss资产系统
	queryAssetResponse, err := QueryBossAsset(activity.BossBid, activity.BossAppId, activity.BossAppSecret, activity.BossAccountId, cast.ToString(platUid))
	if err != nil {
		log.Warnf("查询抽奖券，查询Boss资产系统失败。activity:%+v, platUid:%v, err: %v", activity, platUid, err)
		return errs.Response[lottery.QueryLotteryTicketRsp](errs.Failure), nil
	}
	userAssets := queryAssetResponse.Assets
	assets := lo.FilterMap(userAssets, func(userAsset *corev1.UserAsset, index int) (*lottery.UserAsset, bool) {
		details := lo.Map(userAsset.Details, func(detail *corev1.UserAsset_Detail, index int) *lottery.UserAsset_Detail {
			return &lottery.UserAsset_Detail{
				AssetNum: detail.AssetNum,
				ExpireTs: detail.ExpireTs,
			}
		})
		return &lottery.UserAsset{
			AssetId:  userAsset.AssetId,
			AssetNum: userAsset.AssetNum,
			Details:  details,
		}, userAsset.AssetId == ticket.TicketId
	})
	rsp := &lottery.QueryLotteryTicketRsp{
		Assets: assets,
	}
	return rsp, nil
}
