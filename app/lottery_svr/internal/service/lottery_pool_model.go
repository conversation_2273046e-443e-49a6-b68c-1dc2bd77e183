package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	lottery_admin "cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/lottery/admin"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"github.com/shopspring/decimal"
)

type PoolModel int

const (
	PoolModelSimple PoolModel = iota + 1
	PoolModelMultiple
)

type PoolModelInfo struct {
	Id    PoolModel                                                                                 `json:"id,omitempty"`
	Code  string                                                                                    `json:"code,omitempty"`
	Name  string                                                                                    `json:"name,omitempty"`
	Check func(*lottery_admin.PoolInfo) bool                                                        `json:"check,omitempty"`
	Draw  func(poolInfo *lottery_admin.PoolInfo, drawNum int64) ([]*lottery_admin.PrizeItem, error) `json:"draw,omitempty"`
}

var PoolModelInfos = map[PoolModel]PoolModelInfo{
	PoolModelSimple: {
		Id:    PoolModelSimple,
		Code:  "simple",
		Name:  "单次抽中一个模型",
		Check: PoolModelSimpleCheck,
		Draw:  PoolModelSimpleDraw,
	},
	PoolModelMultiple: {
		Id:    PoolModelMultiple,
		Code:  "multiple",
		Name:  "单次抽中多个模型",
		Check: PoolModelMultipleCheck,
		Draw:  PoolModelMultipleDraw,
	},
}

var PoolModelCodeMap = func() map[string]PoolModelInfo {
	m := make(map[string]PoolModelInfo)
	for _, info := range PoolModelInfos {
		m[info.Code] = info
	}
	return m
}()

func (p PoolModel) Code() string {
	if info, ok := PoolModelInfos[p]; ok {
		return info.Code
	}
	return "unknown"
}

func (p PoolModel) Name() string {
	if info, ok := PoolModelInfos[p]; ok {
		return info.Name
	}
	return "Unknown platform"
}

func GetPoolModelInfoByCode(code string) (*PoolModelInfo, bool) {
	info, ok := PoolModelCodeMap[code]
	return &info, ok
}

func CheckPrizeItem(prize *lottery_admin.PrizeItem) bool {
	// 检查奖项概率
	if prize.Probability == "" {
		log.Warnf("PoolModelSimpleCheck，奖项概率为空, prize:%+v", prize)
		return true
	}
	// 检查奖项概率
	probability, err := decimal.NewFromString(prize.Probability)
	if err != nil {
		log.Warnf("PoolModelSimpleCheck，奖项概率格式错误, prize:%+v, err:%v", prize, err)
		return true
	}
	// 检查奖项概率
	if probability.LessThan(decimal.NewFromInt(0)) || probability.GreaterThan(decimal.NewFromInt(1)) {
		log.Warnf("PoolModelSimpleCheck，奖项概率不在0-1之间, prize:%+v", prize)
		return true
	}
	// 检查奖项类型
	if prize.PrizeId < 0 {
		log.Warnf("PoolModelSimpleCheck，奖项ID错误, prize:%+v", prize)
		return true
	}
	// 检查奖项数量
	if prize.PrizeNum < 0 {
		log.Warnf("PoolModelSimpleCheck，奖项数量错误, prize:%+v", prize)
		return true
	}
	// 检查奖项类型
	_, exist := constant.GetPrizeTypeInfoByCode(prize.PrizeType)
	if !exist {
		log.Warnf("PoolModelSimpleCheck，奖项类型错误, prize:%+v", prize)
		return true
	}
	return false
}
