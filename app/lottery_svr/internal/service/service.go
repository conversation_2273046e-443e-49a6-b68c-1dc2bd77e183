package service

import (
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/adapter_unified_assets/adapter_unified_assets"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"fmt"
	"net/http"
	"sync"
	"time"

	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/config"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/errs"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/utils"
	config_cache "cnb.tmeoa.com/tme_bmp/component/common/config"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/lottery"
	lottery_admin "cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/lottery/admin"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/rank_reconcile"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/gopen"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/reward_sender"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/redis"

	"github.com/dromara/carbon/v2"
)

type TppRightsClient struct {
	AppKey            string
	AppSecret         string
	ApplyRightsUrl    string // SystemKey is the system key for TME TPP.
	CheckOpRightsUri  string // TppServerAddr is the address of the TME TPP gateway server.
	HrcStaffDetailUri string // HrcStaffDetailUri is the URI for checking staff details.
	HrcStaffSearchUri string
	Client            *http.Client
}

// Service struct.
type Service struct {
	lottery.UnimplementedLotteryApiServer
	lottery_admin.UnimplementedLotteryAdminApiServer
	config                      *config.Config
	configCache                 *config_cache.ConfigCache[rank_reconcile.ReconciliationConfig]
	redisCli                    *redis.Client
	daoInitLock                 sync.Mutex
	presentOrderService         *PresentOrderService
	consumeAndAwardOrderService *ConsumeAndAwardOrderService
	rewardSenderClient          reward_sender.RewardSenderClient
	gOpenServiceClient          gopen.GOpenServiceClient
	tppRightsClient             *TppRightsClient
	adapterUnifiedAssetClient   adapter_unified_assets.AdapterUnifiedAssetClient
	lotteryOrderService         *PureLotteryOrderService
	lotteryDeliveryOrderService *LotteryDeliveryOrderService
	orderRepairJob              *OrderRepairJob
}

var s *Service

// New creates a new Service.
func New(c *config.Config) (*Service, error) {
	carbon.SetTimezone("Asia/Shanghai")

	// Redis初始化
	redisCli, err := redis.NewClient(c.RedisConfig)
	if err != nil {
		log.Warnf("redis.NewClient err:%v", err)
		panic(err)
	}

	// Mysql初始化
	_, err = dao.Init(c)
	if err != nil {
		panic(err)
	}

	rewardSenderClient := reward_sender.NewClient(c.RewardSender)
	adapterUnifiedAssetClient := adapter_unified_assets.NewClient(c.AdapterUnifiedAssets)

	presentDispatcher := NewPresentDispatcher()
	presentDispatcher.Register(NewBossAssetPresentHandler())
	presentDispatcher.Register(NewGameWelfarePresentHandler(rewardSenderClient))
	presentDispatcher.Register(NewPlatWelfarePresentHandler(adapterUnifiedAssetClient))

	consumeDispatcher := NewConsumeDispatcher()
	consumeDispatcher.Register(&BossAssetConsumeHandler{})

	presentOrderService := NewPresentOrderService(presentDispatcher)
	consumeAndAwardOrderService := NewConsumeAndAwardOrderService(consumeDispatcher, presentDispatcher)
	lotteryOrderService := NewPureLotteryOrderService()
	lotteryDeliveryOrderService := NewLotteryDeliveryOrderService(presentDispatcher)

	// 初始化订单补单定时任务
	orderRepairJob := NewOrderRepairJob()

	if err != nil {
		panic("dao.New err:" + err.Error())
	}
	if c.TppRights == nil {
		panic("c.Tpp nil")
	}
	s = &Service{
		config:                      c,
		redisCli:                    redisCli,
		presentOrderService:         presentOrderService,
		consumeAndAwardOrderService: consumeAndAwardOrderService,
		rewardSenderClient:          rewardSenderClient,
		gOpenServiceClient:          gopen.NewClient(c.Gopen),
		tppRightsClient:             NewTppClient(c.TppRights),
		adapterUnifiedAssetClient:   adapterUnifiedAssetClient,
		lotteryOrderService:         lotteryOrderService,
		lotteryDeliveryOrderService: lotteryDeliveryOrderService,
		orderRepairJob:              orderRepairJob,
	}

	// set error for TPP rights client
	errs.ActivityRightError.WithMsg(fmt.Sprintf("无新增活动权限，请前往 %s 申请运营者权限", s.tppRightsClient.ApplyRightsUrl))
	errs.LotteryRightError.WithMsg(fmt.Sprintf("无新增抽奖券权限，请前往 %s 申请运营者权限", s.tppRightsClient.ApplyRightsUrl))

	// 启动订单补单定时任务
	orderRepairJob.Start()
	log.Info("订单补单定时任务已启动")

	return s, nil
}

func NewTppClient(tpp *config.TppRightsConfig) *TppRightsClient {
	return &TppRightsClient{
		AppKey:            tpp.AppKey,
		AppSecret:         tpp.AppSecret,
		ApplyRightsUrl:    tpp.ApplyRightsUrl,
		CheckOpRightsUri:  tpp.CheckOpRightsUri,
		HrcStaffDetailUri: tpp.HrcStaffDetailUri,
		HrcStaffSearchUri: tpp.HrcStaffSearchUri,
		Client:            utils.NewHTTPClient(time.Duration(tpp.TimeOutMills) * time.Millisecond),
	}
}
