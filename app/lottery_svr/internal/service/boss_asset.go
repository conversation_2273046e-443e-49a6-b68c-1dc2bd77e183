package service

import (
	"bytes"
	corev1 "cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/grpc-party/gen-go/asset/api/v1"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/utils"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"github.com/spf13/cast"
	"io"
	"net/http"
	"strconv"
	"time"
)

const (
	bossAssetBaseURL = "http://127.0.0.1:65001"
)

// PresentBossAsset 发放资产到Boss
func PresentBossAsset(bossAppId string, bossAppSecret string, bossBid int32, bossAccountId string, userId string,
	orderId string, orderTime int64, presentAssets []*corev1.PresentAsset) (*corev1.PresentAssetResponse, error) {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := "testnonce"
	presentAssetRequest := &corev1.PresentAssetRequest{
		AppId:         bossAppId,
		Bid:           bossBid,
		AccountId:     bossAccountId,
		UserId:        userId,
		OrderId:       orderId,
		OrderTime:     orderTime,
		PresentAssets: presentAssets,
	}
	body, err := utils.ProtoToJSON(presentAssetRequest)
	if err != nil {
		return nil, err
	}
	log.Warnf("PresentBossAsset request body: %s", body)

	uri := "/boss/asset/v1/present"
	req, _ := http.NewRequest("POST", bossAssetBaseURL+uri, bytes.NewBuffer([]byte(body)))
	req.Host = "kg.boss.asset_access"
	req.Header.Add("x-boss-bid", cast.ToString(bossBid))
	req.Header.Add("x-boss-app-id", bossAppId)
	req.Header.Add("x-boss-timestamp", timestamp)
	req.Header.Add("x-boss-nonce", nonce)
	signature := generateSign(uri, timestamp, nonce, []byte(body), []byte(bossAppSecret))
	req.Header.Add("x-boss-signature", signature)

	rsp, err := http.DefaultClient.Do(req)
	if err != nil {
		log.Warnf("PresentBossAsset request error: %v", err)
		return nil, err
	}
	if rsp == nil || rsp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("PresentBossAsset failed with rsp: %v", utils.ToJSONIgnoreError(rsp))
	}
	rspBody, err := io.ReadAll(rsp.Body)
	log.Warnf("PresentBossAsset response, req:%+v, reqBody:%s, rsp.StatusCode:%d, rspBody:%s", req, body, rsp.StatusCode, string(rspBody))
	presentAssetResponse := &corev1.PresentAssetResponse{}
	err = utils.JsonToProto(string(rspBody), presentAssetResponse)
	if err != nil {
		return nil, err
	}
	return presentAssetResponse, nil
}

func ConsumeBossAsset(bossAppId string, bossAppSecret string, bossBid int32, bossAccountId string, userId string,
	orderId string, orderTime int64, consumeAssets []*corev1.ConsumeAsset) (*corev1.ConsumeAssetResponse, error) {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := "testnonce"
	consumeAssetRequest := &corev1.ConsumeAssetRequest{
		AppId:          bossAppId,
		Bid:            bossBid,
		AccountId:      bossAccountId,
		UserId:         userId,
		OrderId:        orderId,
		OrderTime:      orderTime,
		ReduceStrategy: corev1.AssetReduceStrategy_ASSET_REDUCE_STRATEGY_NORMAL,
		ConsumeAssets:  consumeAssets,
		Remark:         "",
		Ext:            "",
	}
	jsonBody, _ := utils.ProtoToJSON(consumeAssetRequest)

	uri := "/boss/asset/v1/consume"
	req, _ := http.NewRequest("POST", bossAssetBaseURL+uri, bytes.NewBuffer([]byte(jsonBody)))
	req.Host = "kg.boss.asset_access"
	req.Header.Add("x-boss-bid", cast.ToString(bossBid))
	req.Header.Add("x-boss-app-id", bossAppId)
	req.Header.Add("x-boss-timestamp", timestamp)
	req.Header.Add("x-boss-nonce", nonce)
	signature := generateSign(uri, timestamp, nonce, []byte(jsonBody), []byte(bossAppSecret))
	req.Header.Add("x-boss-signature", signature)

	rsp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	if rsp == nil || rsp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("ConsumeBossAsset failed with rsp: %v", utils.ToJSONIgnoreError(rsp))
	}
	rspBody, err := io.ReadAll(rsp.Body)
	if err != nil {
		return nil, err
	}
	log.Warnf("ConsumeBossAsset req:%+v, rsp.StatusCode:%d, rspBody:%s", req, rsp.StatusCode, string(rspBody))
	consumeAssetResponse := &corev1.ConsumeAssetResponse{}
	err = utils.JsonToProto(string(rspBody), consumeAssetResponse)
	if err != nil {
		return nil, err
	}
	return consumeAssetResponse, nil
}

func RefundConsumeBossAsset(bossAppId string, bossAppSecret string, bossBid int32, bossAccountId string,
	platUid uint64, orderId string, orderTime int64) (*corev1.RefundConsumeAssetResponse, error) {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := "testnonce"
	refundConsumeAssetRequest := &corev1.RefundConsumeAssetRequest{
		Bid:            bossBid,
		AppId:          bossAppId,
		AccountId:      bossAccountId,
		UserId:         cast.ToString(platUid),
		ConsumeOrderId: orderId,
		OrderTime:      orderTime,
		Remark:         "",
		Ext:            "",
	}
	jsonBody, _ := utils.ProtoToJSON(refundConsumeAssetRequest)

	uri := "/boss/asset/v1/refund_consume"
	req, _ := http.NewRequest("POST", bossAssetBaseURL+uri, bytes.NewBuffer([]byte(jsonBody)))
	req.Host = "kg.boss.asset_access"
	req.Header.Add("x-boss-bid", cast.ToString(bossBid))
	req.Header.Add("x-boss-app-id", bossAppId)
	req.Header.Add("x-boss-timestamp", timestamp)
	req.Header.Add("x-boss-nonce", nonce)
	signature := generateSign(uri, timestamp, nonce, []byte(jsonBody), []byte(bossAppSecret))
	req.Header.Add("x-boss-signature", signature)

	rsp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	if rsp == nil || rsp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("RefundConsumeBossAsset failed with rsp: %v", utils.ToJSONIgnoreError(rsp))
	}
	rspBody, err := io.ReadAll(rsp.Body)
	if err != nil {
		return nil, err
	}
	log.Warnf("RefundConsumeBossAsset req:%+v, rsp.StatusCode:%d, rspBody:%s", req, rsp.StatusCode, string(rspBody))
	refundConsumeAssetResponse := &corev1.RefundConsumeAssetResponse{}
	err = utils.JsonToProto(string(rspBody), refundConsumeAssetResponse)
	if err != nil {
		return nil, err
	}
	return refundConsumeAssetResponse, nil
}

func QueryBossAsset(bossBid int32, bossAppId string, bossAppSecret string, bossAccountId string, platUid string) (*corev1.QueryAssetResponse, error) {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := "testnonce"
	params := fmt.Sprintf("bid=%d&appId=%s&accountId=%s&userId=%s", bossBid, bossAppId, bossAccountId, platUid)
	secret := []byte(bossAppSecret)

	uri := "/boss/asset/v1/query"
	req, _ := http.NewRequest("GET", bossAssetBaseURL+uri+"?"+params, nil)
	req.Host = "kg.boss.asset_access"
	req.Header.Add("x-boss-bid", cast.ToString(bossBid))
	req.Header.Add("x-boss-app-id", bossAppId)
	req.Header.Add("x-boss-timestamp", timestamp)
	req.Header.Add("x-boss-nonce", nonce)
	req.Header.Add("x-boss-signature", generateSign(uri, timestamp, nonce, []byte(params), secret))

	rsp, err := http.DefaultClient.Do(req)
	log.Warnf("QueryBossAsset请求结果, err:%v, req:%+v, rsp:%+v", err, req, rsp)
	if err != nil {
		log.Warnf("QueryBossAsset err: %v", err)
		return nil, err
	}
	if rsp == nil || rsp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("QueryBossAsset failed with rsp: %v", utils.ToJSONIgnoreError(rsp))
	}
	body, _ := io.ReadAll(rsp.Body)
	log.Warnf("QueryBossAsset statusCode:%v, body:%v", rsp.StatusCode, string(body))
	queryAssetResponse := &corev1.QueryAssetResponse{}
	err = utils.JsonToProto(string(body), queryAssetResponse)
	return queryAssetResponse, nil
}

func generateSign(path, timestamp, nonce string, body, secretKey []byte) string {
	// timestamp + '\n' + nonce + '\n' + path + '\n' + body
	hm := hmac.New(sha256.New, secretKey)
	hm.Write([]byte(timestamp))
	hm.Write([]byte{'\n'})
	hm.Write([]byte(nonce))
	hm.Write([]byte{'\n'})
	hm.Write([]byte(path))
	hm.Write([]byte{'\n'})
	hm.Write(body)
	return base64.StdEncoding.EncodeToString(hm.Sum(nil))
}
