package service

import (
	corev1 "cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/grpc-party/gen-go/asset/api/v1"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/errs"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"encoding/json"
	"fmt"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type BossAssetConsumeHandler struct {
}

func (h *BossAssetConsumeHandler) SupportType() string {
	return constant.PrizeTypeBossAsset.Code() + "_consume"
}

func (h *BossAssetConsumeHandler) Handle(ctx context.Context, lottery *model.LotteryActivity, consume *model.Consume) *dto.Result[bool] {
	request := h.buildConsumeRequest(lottery, consume)
	resp, err := ConsumeBossAsset(lottery.BossAppId, lottery.BossAppSecret, lottery.BossBid,
		lottery.BossAccountId, request.UserId, request.OrderId, request.OrderTime, request.ConsumeAssets)
	log.Warnf("Asset consume successful resp=%v", resp)
	// 处理调用结果
	if err != nil {
		return h.handleGrpcError(consume.ConsumeId, err)
	}
	// 成功响应
	if resp != nil {
		log.Warn("Asset consume successful",
			"consumeId", consume.ConsumeId,
			"assetId", consume.AssetId)
		return dto.Success(true)
	}
	return dto.Fail[bool](
		errs.ThirdPartyError.Code,
		errs.ThirdPartyError.Msg,
	).WithError(fmt.Errorf("扣费异常 resp is null "))
}

func (h *BossAssetConsumeHandler) handleGrpcError(consumeId string, err error) *dto.Result[bool] {
	st, _ := status.FromError(err)
	if st != nil {
		switch st.Code() {
		case 10000: // 幂等错误码
			log.Warn("幂等请求已处理",
				"consumeID", consumeId,
				"code", st.Code(),
				"message", st.Message())
			return dto.Success(true)
		case 10002: // 余额不足错误码
			log.Warn("余额不足",
				"consumeID", consumeId,
				"code", st.Code(),
				"message", st.Message())
			return dto.Fail[bool](
				errs.NotEnough.Code,
				errs.NotEnough.Msg,
			).WithError(st.Err())
		case codes.DeadlineExceeded:
			log.Error("请求超时",
				"consumeID", consumeId,
				"error", st.Err())
			return dto.Fail[bool](
				errs.ThirdPartyTimeout.Code,
				"请求处理超时",
			).WithError(st.Err())
		default:
			log.Error("gRPC服务返回错误",
				"consumeID", consumeId,
				"code", st.Code(),
				"message", st.Message())
			return dto.Fail[bool](
				errs.ThirdPartyError.Code,
				"服务处理失败",
			).WithError(st.Err())
		}
	}

	log.Error("gRPC通信错误",
		"consumeID", consumeId,
		"error", err)
	return dto.Fail[bool](
		errs.GrpcConnectionError.Code,
		"服务通信失败",
	).WithError(err)
}

func (h *BossAssetConsumeHandler) buildConsumeRequest(lottery *model.LotteryActivity, c *model.Consume) *corev1.ConsumeAssetRequest {
	consumeAsset := &corev1.ConsumeAsset{
		AssetId:  int32(c.AssetId),
		AssetNum: c.AssetNum,
	}

	return &corev1.ConsumeAssetRequest{
		Bid:            lottery.BossBid,
		AppId:          c.AppId,
		UserId:         cast.ToString(c.PlatUid),
		OrderId:        c.OrderId,
		OrderTime:      c.OrderTime,
		ReduceStrategy: corev1.AssetReduceStrategy_ASSET_REDUCE_STRATEGY_NORMAL, // 使用严格扣减策略
		ConsumeAssets:  []*corev1.ConsumeAsset{consumeAsset},
		Remark:         "",
		AccountId:      lottery.BossAccountId,
	}
}

func (h *BossAssetConsumeHandler) refund(ctx context.Context, order *model.RefundOrder) *dto.Result[bool] {
	mapExt := map[string]string{}
	err := json.Unmarshal([]byte(order.MapExt), &mapExt)
	if err != nil || mapExt == nil {
		log.Warn("Asset refund，解析mapExt失败。err:%v", err)
		return dto.Fail[bool](errs.ThirdPartyError.Code, errs.ThirdPartyError.Msg).WithError(fmt.Errorf("退费失败，解析mapExt失败"))
	}
	lotteryId, err := cast.ToInt32E(lo.ValueOr(mapExt, "lotteryId", ""))
	if err != nil || lotteryId <= 0 {
		log.Warn("Asset refund，解析lotteryId失败。err:%v", err)
		return dto.Fail[bool](errs.ThirdPartyError.Code, errs.ThirdPartyError.Msg).WithError(fmt.Errorf("退费失败，解析lotteryId失败"))
	}
	lotteryActivity, err := dao.LotteryActivityDao.GetLotteryActivityById(ctx, lotteryId)
	if err != nil || lotteryActivity == nil {
		log.Errorf("Asset refund，查询抽奖活动配置不存在。lotteryId:%+v, err:%v", lotteryId, err)
		return dto.Fail[bool](errs.ThirdPartyError.Code, errs.ThirdPartyError.Msg).WithError(fmt.Errorf("查询抽奖活动配置不存在"))
	}
	// 调用Boss资产执行退款
	resp, err := RefundConsumeBossAsset(lotteryActivity.BossAppId, lotteryActivity.BossAppSecret, lotteryActivity.BossBid,
		lotteryActivity.BossAccountId, order.PlatUid, order.ConsumeId, order.BizTimestamp)
	log.Warnf("Asset refund，调用完成。resp=%v", resp)
	// 处理调用结果
	if err != nil {
		return h.handleGrpcError(order.ConsumeId, err)
	}
	// 成功响应
	if resp != nil {
		log.Warn("Asset refund，调用成功。", "consumeId", order.ConsumeId, "assetId", order.AssetId)
		return dto.Success(true)
	}
	return dto.Fail[bool](
		errs.ThirdPartyError.Code,
		errs.ThirdPartyError.Msg,
	).WithError(fmt.Errorf("Asset refund，扣费异常 resp is null "))
}
