package service

import (
	corev1 "cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/grpc-party/gen-go/asset/api/v1"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/errs"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"github.com/spf13/cast"
	"google.golang.org/grpc/status"

	"github.com/dromara/carbon/v2"
)

type BossAssetPresentHandler struct {
}

func NewBossAssetPresentHandler() *BossAssetPresentHandler {
	return &BossAssetPresentHandler{}
}

func (h *BossAssetPresentHandler) SupportType() string {
	return constant.PrizeTypeBossAsset.Code() + "_present"
}

func (h *BossAssetPresentHandler) Handle(ctx context.Context, lottery *model.LotteryActivity, present *model.Present) *dto.Result[bool] {

	// 构建请求
	request := h.buildPresentRequest(lottery, present)

	// 尝试发放资产
	presentResult := h.tryPresent(ctx, lottery, request)
	if !presentResult.IsSuccess() {
		log.Warnf("发放资产失败: %v", presentResult)
		return presentResult
	}
	// 更新订单状态
	result := UpdatePresentStatus(ctx, present)
	log.Infof("更新订单状态结果: %v", result)
	return result
}

func (h *BossAssetPresentHandler) buildPresentRequest(lottery *model.LotteryActivity, present *model.Present) *corev1.PresentAssetRequest {
	return &corev1.PresentAssetRequest{
		Bid:       lottery.BossBid,
		AppId:     lottery.BossAppId,
		AccountId: lottery.BossAccountId,
		UserId:    cast.ToString(present.PlatUid),
		OrderId:   present.PresentId,
		OrderTime: present.BizTimestamp,
		PresentAssets: []*corev1.PresentAsset{
			{
				AssetId:  int32(present.PrizeId),
				AssetNum: present.PrizeNum,
				ExpireTs: present.ExpireTime,
			},
		},
	}
}

func (h *BossAssetPresentHandler) tryPresent(ctx context.Context, lottery *model.LotteryActivity, req *corev1.PresentAssetRequest) *dto.Result[bool] {
	rsp, err := PresentBossAsset(lottery.BossAppId, lottery.BossAppSecret, lottery.BossBid, lottery.BossAccountId,
		req.UserId, req.OrderId, req.OrderTime, req.PresentAssets)
	log.Warnf("PresentBossAsset rsp: %v", rsp)
	// 处理错误
	if err != nil {
		log.Warnf("gRPC调用失败。err:%v", err)
		result := h.handleGrpcError(err)
		log.Warnf("gRPC调用失败: %v", result)
		return result
	}
	result := dto.Success(true)
	log.Warnf("gRPC调用成功: %v", result)
	return result
}

func (h *BossAssetPresentHandler) handleGrpcError(err error) *dto.Result[bool] {
	st, _ := status.FromError(err)
	if st == nil {
		result := dto.Fail[bool](errs.NetworkError.Code, "网络通信异常").
			WithError(err).
			WithData(false).
			WithMsg("网络连接失败: %v", err)
		log.Warnf("网络通信异常: %v", result)
		return result
	}

	switch st.Code() {
	case 10000: // 假设的幂等错误码
		result := dto.Success(true).
			WithMsg("幂等操作已确认: %s", st.Message())
		log.Infof("幂等操作: %v", result)
		return result
	default:
		result := dto.Fail[bool](errs.ThirdPartyError.Code, "第三方服务异常").
			WithData(false).
			WithMsg("服务错误[%d]: %s", st.Code(), st.Message())
		log.Warnf("第三方服务异常: %v", result)
		return result
	}
}

func UpdatePresentStatus(ctx context.Context, p *model.Present) *dto.Result[bool] {
	monthStr := GetYearMonth(p.BizTimestamp)
	// 更新数据库
	effectRow, err := dao.LotteryPresentDao.UpdatePresentWithSuccess(ctx, monthStr, p.PresentId)
	if err != nil {
		result := dto.Fail[bool](errs.DBAccessError.Code, "数据库操作失败").
			WithError(err).
			WithMsg("更新订单状态失败: %v", err)
		log.Warnf("数据库操作失败: %v", result)
		return result
	}

	if effectRow != 1 {
		result := dto.Fail[bool](errs.Processing.Code, errs.Processing.Msg).
			WithMsg("更新影响行数异常，期望1行，实际%d行", effectRow)
		log.Warnf("更新影响行数异常: %v", result)
		return result
	}

	result := dto.Success(true)
	p.Status = 1
	log.Infof("更新present订单状态成功: %v", result)
	return result
}
