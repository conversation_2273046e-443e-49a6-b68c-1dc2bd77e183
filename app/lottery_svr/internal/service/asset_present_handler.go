package service

import (
	"context"

	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
)

type AssetPresentHandler interface {
	Handle(ctx context.Context, lottery *model.LotteryActivity, present *model.Present) *dto.Result[bool]
	SupportType() string
}

type PresentDispatcher struct {
	handlers map[string]AssetPresentHandler
}

func NewPresentDispatcher() *PresentDispatcher {
	return &PresentDispatcher{
		handlers: make(map[string]AssetPresentHandler),
	}
}

// 注册处理器

func (d *PresentDispatcher) Register(h AssetPresentHandler) {
	d.handlers[h.SupportType()] = h
}

func (d *PresentDispatcher) GetHandler(assetType string) (AssetPresentHandler, bool) {
	handler, ok := d.handlers[assetType]
	return handler, ok
}
