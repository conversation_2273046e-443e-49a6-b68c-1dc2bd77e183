package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/utils"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"encoding/json"
	"github.com/spf13/cast"
	"sync"
	"time"

	"github.com/dromara/carbon/v2"
	"github.com/robfig/cron/v3"
)

// OrderRepairJob 订单补单定时任务
type OrderRepairJob struct {
	cron    *cron.Cron
	helper  *OrderRepairHelper
	running bool
	mu      sync.RWMutex
}

// NewOrderRepairJob 创建订单补单定时任务
func NewOrderRepairJob() *OrderRepairJob {
	// 创建cron调度器，支持秒级精度
	c := cron.New(cron.WithSeconds())

	return &OrderRepairJob{
		cron:    c,
		helper:  NewOrderRepairHelper(),
		running: false,
	}
}

// Start 启动定时任务
func (j *OrderRepairJob) Start() {
	j.mu.Lock()
	defer j.mu.Unlock()

	if j.running {
		log.Warn("订单补单定时任务已经在运行中")
		return
	}

	// 添加定时任务：每5分钟执行一次
	// cron表达式: "0 */5 * * * *" 表示每5分钟的第0秒执行
	_, err := j.cron.AddFunc("0 */5 * * * *", func() {
		j.executeRepairTask()
	})

	if err != nil {
		log.Errorf("添加订单补单定时任务失败: %v", err)
		return
	}

	// 启动cron调度器
	j.cron.Start()
	j.running = true

	log.Info("订单补单定时任务启动成功 (每5分钟执行一次)")
}

// Stop 停止定时任务
func (j *OrderRepairJob) Stop() {
	j.mu.Lock()
	defer j.mu.Unlock()

	if !j.running {
		log.Warn("订单补单定时任务未在运行")
		return
	}

	// 停止cron调度器
	ctx := j.cron.Stop()

	// 等待正在执行的任务完成
	<-ctx.Done()

	j.running = false

	log.Info("订单补单定时任务停止成功")
}

// IsRunning 检查任务是否在运行
func (j *OrderRepairJob) IsRunning() bool {
	j.mu.RLock()
	defer j.mu.RUnlock()
	return j.running
}

// executeRepairTask 执行补单任务
func (j *OrderRepairJob) executeRepairTask() {
	ctx := context.Background()

	log.Info("开始执行订单补单任务")

	// 获取当前月份和上个月份的分月表
	monthTables := j.getMonthTables()

	for _, monthTable := range monthTables {
		j.processMonthTable(ctx, monthTable)
	}

	log.Info("订单补单任务执行完成")
}

// getMonthTables 获取需要处理的分月表
func (j *OrderRepairJob) getMonthTables() []string {
	now := carbon.Now()

	// 当前月份
	currentMonth := now.Format("Ymd")[:6] // 使用carbon的格式化方式，取前6位得到YYYYMM

	// 上个月份
	lastMonth := now.SubMonth().Format("Ymd")[:6]

	return []string{currentMonth, lastMonth}
}

// processMonthTable 处理指定月份的订单表
func (j *OrderRepairJob) processMonthTable(ctx context.Context, yearMonth string) {
	log.Infof("开始处理年月%s的订单补单", yearMonth)

	// 查询30分钟到5分钟前status=0的订单，每次最多处理100条
	orders, err := dao.LotteryOrderDao.SelectPendingOrdersInTimeRange(ctx, yearMonth, 30, 5, 100)
	if err != nil {
		log.Errorf("查询待补单订单失败, yearMonth: %s, error: %v", yearMonth, err)
		return
	}

	if len(orders) == 0 {
		log.Infof("年月%s没有需要补单的订单", yearMonth)
		return
	}

	log.Infof("年月%s找到%d个需要补单的订单", yearMonth, len(orders))

	// 按订单类型分组处理
	ordersByType := j.groupOrdersByType(orders)

	successCount := 0
	failCount := 0

	for orderType, typeOrders := range ordersByType {
		typeSuccessCount, typeFailCount := j.processOrdersByType(ctx, orderType, typeOrders, yearMonth)
		successCount += typeSuccessCount
		failCount += typeFailCount
	}

	log.Infof("年月%s订单补单完成, 成功: %d, 失败: %d", yearMonth, successCount, failCount)
}

// groupOrdersByType 按订单类型分组
func (j *OrderRepairJob) groupOrdersByType(orders []*model.Order) map[int32][]*model.Order {
	ordersByType := make(map[int32][]*model.Order)

	for _, order := range orders {
		orderType := order.OrderType
		ordersByType[orderType] = append(ordersByType[orderType], order)
	}

	return ordersByType
}

// processOrdersByType 按订单类型处理订单
func (j *OrderRepairJob) processOrdersByType(ctx context.Context, orderType int32, orders []*model.Order, yearMonth string) (int, int) {
	orderTypeInfo, exists := constant.GetOrderTypeInfoById(constant.OrderType(orderType))
	if !exists {
		log.Errorf("未知的订单类型: %d", orderType)
		return 0, len(orders)
	}

	log.Infof("开始处理订单类型: %s (%s), 订单数量: %d", orderTypeInfo.Name, orderTypeInfo.Code, len(orders))

	successCount := 0
	failCount := 0

	for _, order := range orders {
		if j.processOrderByType(ctx, order, orderType, yearMonth) {
			successCount++
		} else {
			failCount++
		}
	}

	log.Infof("订单类型%s处理完成, 成功: %d, 失败: %d", orderTypeInfo.Name, successCount, failCount)
	return successCount, failCount
}

// processOrderByType 根据订单类型处理单个订单
func (j *OrderRepairJob) processOrderByType(ctx context.Context, order *model.Order, orderType int32, yearMonth string) bool {
	log.Infof("开始补单订单: %s, 类型: %d", order.OrderId, orderType)

	var success bool

	switch constant.OrderType(orderType) {
	case constant.OrderTypeLottery:
		// 纯抽奖订单补单逻辑
		success = j.repairLotteryOrder(ctx, order, yearMonth)
	case constant.OrderTypeLotteryDelivery:
		// 抽奖发奖订单补单逻辑
		success = j.repairLotteryDeliveryOrder(ctx, order, yearMonth)
	case constant.OrderTypeConsumeLotteryDelivery:
		// 扣抽奖券抽奖发奖订单补单逻辑
		success = j.repairConsumeLotteryDeliveryOrder(ctx, order, yearMonth)
	case constant.OrderTypeDeliveryTicket:
		// 发放抽奖券订单补单逻辑
		success = j.repairDeliveryTicketOrder(ctx, order, yearMonth)
	default:
		log.Errorf("未支持的订单类型: %d", orderType)
		success = false
	}

	if success {
		log.Infof("订单补单成功: %s, 类型: %d", order.OrderId, orderType)
	} else {
		log.Errorf("订单补单失败: %s, 类型: %d", order.OrderId, orderType)
	}

	return success
}

// repairLotteryOrder 纯抽奖订单补单逻辑
func (j *OrderRepairJob) repairLotteryOrder(ctx context.Context, order *model.Order, yearMonth string) bool {
	log.Infof("处理纯抽奖订单补单逻辑: %s", order.OrderId)
	// 检查抽奖记录
	record, err := j.helper.CheckLotteryRecords(ctx, order.OrderId, yearMonth)
	if err != nil {
		log.Errorf("处理纯抽奖订单补单逻辑，检查抽奖记录失败: %v", err)
		return false
	}
	// 如果有抽奖记录，说明订单已经处理过，直接返回成功
	targetStatus := constant.Fail.Status
	if record != nil {
		targetStatus = constant.Success.Status
	}
	// 更新订单状态
	success, err := j.helper.UpdateOrderStatus(ctx, order.OrderId, yearMonth, targetStatus)
	if err != nil {
		log.Errorf("处理纯抽奖订单补单逻辑，更新订单状态失败: %v", err)
		return false
	}
	if success {
		log.Warnf("处理纯抽奖订单补单逻辑，纯抽奖订单补单成功: %s, 状态: %d", order.OrderId, targetStatus)
	}
	return success
}

// repairLotteryDeliveryOrder 抽奖发奖订单补单逻辑
func (j *OrderRepairJob) repairLotteryDeliveryOrder(ctx context.Context, order *model.Order, yearMonth string) bool {
	log.Warnf("抽奖发奖订单补单逻辑，处理抽奖发奖订单补单逻辑: %s", order.OrderId)
	record, err := j.helper.CheckLotteryRecords(ctx, order.OrderId, yearMonth)
	if err != nil {
		log.Errorf("处理抽奖发奖订单补单逻辑，检查抽奖记录失败: %v", err)
		return false
	}
	targetStatus := constant.Fail.Status
	if record != nil {
		// 检查发奖记录
		presents, err := j.helper.CheckPresentRecords(ctx, order.OrderId, yearMonth)
		if err != nil {
			log.Errorf("抽奖发奖订单补单逻辑，检查发奖记录失败: %v", err)
			return false
		}
		// 如果有抽奖记录
		if len(presents) > 0 {
			mapExt := make(map[string]string)
			if utils.IsNotBlank(order.MapExt) {
				err := json.Unmarshal([]byte(order.MapExt), &mapExt)
				if err != nil {
					log.Errorf("抽奖发奖订单补单逻辑，解析MapExt失败: %v", err)
					return false
				}
			}
			lotteryActivity, err := dao.LotteryActivityDao.GetLotteryActivityById(ctx, record.LotteryId)
			if err != nil || lotteryActivity == nil {
				log.Errorf("抽奖发奖订单补单逻辑，抽奖配置不存在。record:%+v, err:%v", record, err)
				return false
			}
			lotteryInfoReq := &dto.LotteryInfoReqDTO{
				AppID:         order.AppId,
				AccessId:      order.AccessId,
				OrderID:       order.OrderId,
				BizTime:       order.BizTimestamp,
				UserID:        cast.ToString(order.PlatUid),
				LotteryId:     cast.ToInt32(record.LotteryId),
				LotteryPoolId: record.LotteryPoolId,
				LotteryNum:    record.DrawNum,
			}
			orderReq := &dto.LotteryOrderReqDTO{
				AppID:                order.AppId,
				AccessId:             order.AccessId,
				OrderID:              order.OrderId,
				OrderType:            string(order.OrderType),
				PlatUid:              cast.ToString(order.PlatUid),
				OpenId:               order.OpenId,
				BizTime:              order.BizTimestamp,
				MapExt:               mapExt,
				LotteryInfoReqDTO:    lotteryInfoReq,
				ConsumeReqDTO:        nil,
				LotteryPresentReqDTO: nil,
				LotteryActivity:      lotteryActivity,
			}
			result := NewBaseOrderService(s.lotteryDeliveryOrderService).Execute(ctx, orderReq)
			if result.IsSuccess() {
				log.Warnf("抽奖发奖订单补单逻辑，抽奖发奖订单补单成功: %s, 状态: %d", order.OrderId, targetStatus)
				targetStatus = constant.Success.Status
			}
		}
	}
	// 更新订单状态
	success, err := j.helper.UpdateOrderStatus(ctx, order.OrderId, yearMonth, targetStatus)
	if err != nil {
		log.Errorf("抽奖发奖订单补单逻辑，更新订单状态失败: %v", err)
		return false
	}
	return success
}

// repairConsumeLotteryDeliveryOrder 扣抽奖券抽奖发奖订单补单逻辑
func (j *OrderRepairJob) repairConsumeLotteryDeliveryOrder(ctx context.Context, order *model.Order, yearMonth string) bool {
	log.Infof("处理扣抽奖券抽奖发奖订单补单逻辑: %s", order.OrderId)
	// 检查消费记录
	consumes, err := j.helper.CheckConsumeRecords(ctx, order.OrderId, yearMonth)
	if err != nil {
		log.Errorf("检查消费记录失败: %v", err)
		return false
	}
	// 检查抽奖记录
	lottery, err := j.helper.CheckLotteryRecords(ctx, order.OrderId, yearMonth)
	if err != nil {
		log.Errorf("检查抽奖记录失败: %v", err)
		return false
	}
	// 检查发奖记录
	presents, err := j.helper.CheckPresentRecords(ctx, order.OrderId, yearMonth)
	if err != nil {
		log.Errorf("检查发奖记录失败: %v", err)
		return false
	}
	if len(consumes) == 0 || len(presents) == 0 || lottery == nil {
		log.Warnf("扣抽奖券抽奖发奖订单补单逻辑，订单状态异常，不处理: %s", order.OrderId)
		// 更新订单状态
		success, err := j.helper.UpdateOrderStatus(ctx, order.OrderId, yearMonth, constant.Fail.Status)
		if err != nil {
			log.Errorf("更新订单状态失败: %v", err)
			return false
		}
		return success
	}
	return false
}

// repairDeliveryTicketOrder 发放抽奖券订单补单逻辑
func (j *OrderRepairJob) repairDeliveryTicketOrder(ctx context.Context, order *model.Order, yearMonth string) bool {
	log.Infof("处理发放抽奖券订单补单逻辑: %s", order.OrderId)

	// 检查发奖记录
	presents, err := j.helper.CheckPresentRecords(ctx, order.OrderId, yearMonth)
	if err != nil {
		log.Errorf("检查发奖记录失败: %v", err)
		return false
	}
	targetStatus := constant.Fail.Status
	if len(presents) > 0 {
		// 构建发放请求
		var presentReqs []*dto.LotteryPresentReqDTO
		presentReqs = append(presentReqs, &dto.LotteryPresentReqDTO{
			UserID:     cast.ToString(order.PlatUid),
			PrizeID:    int64(lotteryTicket.TicketId),
			PrizeNum:   int64(req.TicketNum),
			PrizeType:  constant.PrizeTypeBossAsset.Code() + "_present",
			BizTime:    req.BizTimestamp,
			ExpireTime: lotteryTicket.ExpireSecs,
		})
		orderReq := &dto.LotteryOrderReqDTO{
			AppID:                req.AppId,
			AccessId:             "",
			OrderID:              req.OrderId,
			OrderType:            string(int32(constant.OrderTypeDeliveryTicket)),
			PlatUid:              cast.ToString(platUid),
			OpenId:               req.OpenId,
			BizTime:              time.Now().Unix(),
			LotteryPresentReqDTO: presentReqs,
			LotteryActivity:      lotteryActivity,
			MapExt:               req.MapExt,
		}
		NewBaseOrderService(s.presentOrderService).Execute(ctx, orderReq)
	}

	// 更新订单状态
	success, err := j.helper.UpdateOrderStatus(ctx, order.OrderId, yearMonth, targetStatus)
	if err != nil {
		log.Errorf("更新订单状态失败: %v", err)
		return false
	}

	return success
}

// GetStatus 获取任务状态信息
func (j *OrderRepairJob) GetStatus() map[string]interface{} {
	j.mu.RLock()
	defer j.mu.RUnlock()

	var entries []string
	if j.cron != nil {
		for _, entry := range j.cron.Entries() {
			entries = append(entries, entry.Next.Format("2006-01-02 15:04:05"))
		}
	}

	return map[string]interface{}{
		"running":     j.running,
		"description": "订单补单定时任务，使用cron调度器，每5分钟执行一次，处理30分钟到5分钟前status=0的订单",
		"cron_expr":   "0 */5 * * * *",
		"interval":    "5分钟",
		"time_range":  "30分钟到5分钟前",
		"batch_size":  100,
		"next_runs":   entries,
		"scheduler":   "github.com/robfig/cron/v3",
		"order_types": []string{"lottery", "lottery_delivery", "consume_lottery_delivery", "delivery_ticket"},
	}
}
