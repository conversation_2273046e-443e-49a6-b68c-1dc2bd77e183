package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
)

// OrderRepairJob 订单补单定时任务
type OrderRepairJob struct {
	cron    *cron.Cron
	running bool
	mu      sync.RWMutex
}

// NewOrderRepairJob 创建订单补单定时任务
func NewOrderRepairJob() *OrderRepairJob {
	// 创建cron调度器，支持秒级精度
	c := cron.New(cron.WithSeconds())

	return &OrderRepairJob{
		cron:    c,
		running: false,
	}
}

// Start 启动定时任务
func (j *OrderRepairJob) Start() {
	j.mu.Lock()
	defer j.mu.Unlock()

	if j.running {
		log.Warn("订单补单定时任务已经在运行中")
		return
	}

	// 添加定时任务：每5分钟执行一次
	// cron表达式: "0 */5 * * * *" 表示每5分钟的第0秒执行
	_, err := j.cron.AddFunc("0 */5 * * * *", func() {
		j.executeRepairTask()
	})

	if err != nil {
		log.Errorf("添加订单补单定时任务失败: %v", err)
		return
	}

	// 启动cron调度器
	j.cron.Start()
	j.running = true

	log.Info("订单补单定时任务启动成功 (每5分钟执行一次)")
}

// StartWithCustomCron 使用自定义cron表达式启动定时任务
func (j *OrderRepairJob) StartWithCustomCron(cronExpr string) error {
	j.mu.Lock()
	defer j.mu.Unlock()

	if j.running {
		return fmt.Errorf("订单补单定时任务已经在运行中")
	}

	// 添加自定义定时任务
	_, err := j.cron.AddFunc(cronExpr, func() {
		j.executeRepairTask()
	})

	if err != nil {
		return fmt.Errorf("添加订单补单定时任务失败: %w", err)
	}

	// 启动cron调度器
	j.cron.Start()
	j.running = true

	log.Infof("订单补单定时任务启动成功 (cron表达式: %s)", cronExpr)
	return nil
}

// Stop 停止定时任务
func (j *OrderRepairJob) Stop() {
	j.mu.Lock()
	defer j.mu.Unlock()

	if !j.running {
		log.Warn("订单补单定时任务未在运行")
		return
	}

	// 停止cron调度器
	ctx := j.cron.Stop()

	// 等待正在执行的任务完成
	<-ctx.Done()

	j.running = false

	log.Info("订单补单定时任务停止成功")
}

// IsRunning 检查任务是否在运行
func (j *OrderRepairJob) IsRunning() bool {
	j.mu.RLock()
	defer j.mu.RUnlock()
	return j.running
}



// executeRepairTask 执行补单任务
func (j *OrderRepairJob) executeRepairTask() {
	ctx := context.Background()
	
	log.Info("开始执行订单补单任务")

	// 获取当前月份和上个月份
	now := time.Now()
	currentMonth := now.Format("200601")
	lastMonth := now.AddDate(0, -1, 0).Format("200601")

	// 处理当前月份的订单
	j.processMonthOrders(ctx, currentMonth)
	
	// 处理上个月份的订单
	j.processMonthOrders(ctx, lastMonth)

	log.Info("订单补单任务执行完成")
}

// processMonthOrders 处理指定月份的订单
func (j *OrderRepairJob) processMonthOrders(ctx context.Context, yearMonth string) {
	log.Infof("开始处理年月%s的订单补单", yearMonth)

	// 查询30分钟到5分钟前status=0的订单，每次最多处理100条
	orders, err := dao.LotteryOrderDao.SelectPendingOrdersInTimeRange(ctx, yearMonth, 30, 5, 100)
	if err != nil {
		log.Errorf("查询待补单订单失败, yearMonth: %s, error: %v", yearMonth, err)
		return
	}

	if len(orders) == 0 {
		log.Infof("年月%s没有需要补单的订单", yearMonth)
		return
	}

	log.Infof("年月%s找到%d个需要补单的订单", yearMonth, len(orders))

	// 处理每个订单
	successCount := 0
	failCount := 0
	
	for _, order := range orders {
		if j.repairSingleOrder(ctx, order, yearMonth) {
			successCount++
		} else {
			failCount++
		}
	}

	log.Infof("年月%s订单补单完成, 成功: %d, 失败: %d", yearMonth, successCount, failCount)
}

// repairSingleOrder 补单单个订单
func (j *OrderRepairJob) repairSingleOrder(ctx context.Context, order *model.Order, yearMonth string) bool {
	log.Infof("开始补单订单: %s", order.OrderId)

	// 判断订单应该补成什么状态
	targetStatus := j.determineTargetStatus(ctx, order, yearMonth)
	
	// 执行状态更新
	success := j.updateOrderStatus(ctx, order, targetStatus, yearMonth)
	
	if success {
		log.Infof("订单补单成功: %s, 原状态: %d, 新状态: %d", order.OrderId, order.Status, targetStatus)
	} else {
		log.Errorf("订单补单失败: %s", order.OrderId)
	}
	
	return success
}

// determineTargetStatus 判断订单应该补成什么状态
func (j *OrderRepairJob) determineTargetStatus(ctx context.Context, order *model.Order, yearMonth string) int32 {
	// 检查是否有发奖记录
	presents, err := dao.LotteryPresentDao.SelectPresentListByOrderId(ctx, yearMonth, order.OrderId)
	if err != nil {
		log.Warnf("查询发奖记录失败, orderId: %s, error: %v", order.OrderId, err)
		// 查询失败时默认补成失败状态
		return constant.Fail.Status
	}

	// 如果有发奖记录，说明业务逻辑执行成功，应该补成成功状态
	if len(presents) > 0 {
		log.Infof("订单%s有发奖记录%d条，补成成功状态", order.OrderId, len(presents))
		return constant.Success.Status
	}

	// 检查是否有消费记录
	consumes, err := dao.LotteryConsumeDao.SelectConsumeListByOrderId(ctx, yearMonth, order.OrderId)
	if err != nil {
		log.Warnf("查询消费记录失败, orderId: %s, error: %v", order.OrderId, err)
	}

	// 如果有消费记录但没有发奖记录，可能是抽奖失败，补成失败状态
	if len(consumes) > 0 {
		log.Infof("订单%s有消费记录但无发奖记录，补成失败状态", order.OrderId)
		return constant.Fail.Status
	}

	// 既没有发奖记录也没有消费记录，可能是业务逻辑未执行完成，补成失败状态
	log.Infof("订单%s无发奖记录和消费记录，补成失败状态", order.OrderId)
	return constant.Fail.Status
}

// updateOrderStatus 更新订单状态
func (j *OrderRepairJob) updateOrderStatus(ctx context.Context, order *model.Order, targetStatus int32, yearMonth string) bool {
	var rowsAffected int64
	var err error

	if targetStatus == constant.Success.Status {
		// 更新为成功状态
		rowsAffected, err = dao.LotteryOrderDao.UpdateWithSuccess(ctx, yearMonth, order.OrderId)
	} else {
		// 更新为失败状态
		db := dao.GetDb()
		rowsAffected, err = dao.LotteryOrderDao.UpdateWithFail(yearMonth, order.OrderId, db)
	}

	if err != nil {
		log.Errorf("更新订单状态失败, orderId: %s, targetStatus: %d, error: %v", order.OrderId, targetStatus, err)
		return false
	}

	if rowsAffected != 1 {
		log.Warnf("更新订单状态影响行数异常, orderId: %s, targetStatus: %d, rowsAffected: %d", 
			order.OrderId, targetStatus, rowsAffected)
		return false
	}

	return true
}

// GetStatus 获取任务状态信息
func (j *OrderRepairJob) GetStatus() map[string]interface{} {
	j.mu.RLock()
	defer j.mu.RUnlock()

	var entries []string
	if j.cron != nil {
		for _, entry := range j.cron.Entries() {
			entries = append(entries, entry.Next.Format("2006-01-02 15:04:05"))
		}
	}

	return map[string]interface{}{
		"running":      j.running,
		"description":  "订单补单定时任务，使用cron调度器，每5分钟执行一次，处理30分钟到5分钟前status=0的订单",
		"cron_expr":    "0 */5 * * * *",
		"interval":     "5分钟",
		"time_range":   "30分钟到5分钟前",
		"batch_size":   100,
		"next_runs":    entries,
		"scheduler":    "github.com/robfig/cron/v3",
	}
}
