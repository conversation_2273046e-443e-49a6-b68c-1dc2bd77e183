package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/constant"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/errs"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/utils"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/reward_sender"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"encoding/json"
)

//全民福利礼包发奖

type GameWelfarePresentHandler struct {
	RewardSenderClient reward_sender.RewardSenderClient
}

func NewGameWelfarePresentHandler(rewardSenderClient reward_sender.RewardSenderClient) *GameWelfarePresentHandler {
	return &GameWelfarePresentHandler{
		RewardSenderClient: rewardSenderClient,
	}
}

func (h *GameWelfarePresentHandler) SupportType() string {
	return constant.PrizeTypeGameReward.Code()
}

func (h *GameWelfarePresentHandler) Handle(ctx context.Context, lottery *model.LotteryActivity, present *model.Present) *dto.Result[bool] {
	// 解析 MapExt 字段
	var mapExt map[string]string
	if utils.IsNotBlank(present.MapExt) {
		if err := json.Unmarshal([]byte(present.MapExt), &mapExt); err != nil {
			log.Errorf("调用[SendReward]游戏福利发放接口，MapExt 解析失败 | presentID:%s | data:%s | error:%v",
				present.PresentId, present.MapExt, err)
			return dto.Fail[bool](errs.ParamError.Code, "扩展字段格式错误")
		}
	}
	// 构建奖励发送请求
	req := &reward_sender.SendRewardReq{
		AppId:  present.AppId,
		OpenId: present.OpenId,
		Rewards: []*reward_sender.Reward{{
			RewardId: present.PrizeId,
			Num:      present.PrizeNum,
		}},
		BillNo:      present.PresentId,
		SendTs:      present.BizTimestamp,
		ProgramName: "lottery_svr",
		MapExt:      map[string]string{},
	}
	rsp, err := h.RewardSenderClient.SendReward(ctx, req)
	log.Warnf("调用[SendReward]游戏福利发放接口，发放结果: req:%+v rsp:%+v err:%+v", req, rsp, err)
	if err != nil {
		log.Warnf("调用[SendReward]游戏福利发放接口，发放失败。req:%+v rsp:%+v err:%+v", req, rsp, err)
		return dto.Fail[bool](errs.RPCError.Code, errs.RPCError.Msg).WithError(err).
			WithMsg("调用[SendReward]游戏福利发放接口失败: %v", err)
	}
	// 更新订单状态
	result := UpdatePresentStatus(ctx, present)
	log.Infof("调用[SendReward]游戏福利发放接口，更新订单状态结果: %v", result)
	return result
}
