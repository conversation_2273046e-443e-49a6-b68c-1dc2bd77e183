package service

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/dto"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/errs"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"strings"

	"github.com/dromara/carbon/v2"
)

func InsertOrder(order *model.Order) *dto.Result[bool] {
	//根据req.getBizTimestamp() 获取yyyyMM
	c := carbon.CreateFromTimestamp(order.BizTimestamp)
	// 格式化为 yyyyMM
	monthStr := c.Format("Ymd")[:6]
	db := dao.GetDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 插入主订单
		if err := dao.LotteryOrderDao.Insert(monthStr, order, tx); err != nil {
			// 处理主键冲突
			if isDuplicateKeyError(err) {
				log.Warn("主订单已存在", "orderID", order.OrderId, "table", monthStr)
				return fmt.Errorf("%w: order already exists", errs.Processing)
			}
			log.Error("主订单插入失败", "orderID", order.OrderId, "error", err.Error())
			return fmt.Errorf("order insert failed: %w", err)
		}
		return nil
	})
	if err != nil {
		// 处理中间态错误
		var pe *errs.ProcessingError
		if errors.As(err, &pe) {
			return dto.Fail[bool](errs.Processing.Code, errs.Processing.Msg).WithError(err)
		}
		return dto.Fail[bool](errs.DBAccessError.Code, errs.DBAccessError.Msg).
			WithError(err).WithMsg("数据库操作异常: %v", err)
	}
	return dto.Success(true)
}

func (h *PresentOrderService) insertWithPresent(order *model.Order, presents []*model.Present) *dto.Result[bool] {
	//根据req.getBizTimestamp() 获取yyyyMM
	c := carbon.CreateFromTimestamp(order.BizTimestamp)
	// 格式化为 yyyyMM
	monthStr := c.Format("Ymd")[:6]
	db := dao.GetDb()

	err := db.Transaction(func(tx *gorm.DB) error {
		// 插入主订单
		if err := dao.LotteryOrderDao.Insert(monthStr, order, tx); err != nil {
			// 处理主键冲突
			if isDuplicateKeyError(err) {
				log.Warn("主订单已存在",
					"orderID", order.OrderId,
					"table", monthStr)
				return fmt.Errorf("%w: order already exists", errs.Processing)
			}

			log.Error("主订单插入失败",
				"orderID", order.OrderId,
				"error", err.Error())
			return fmt.Errorf("order insert failed: %w", err)
		}

		// 处理奖品列表
		if len(presents) == 0 {
			log.Warn("奖品列表为空",
				"orderID", order.OrderId)
			return nil
		}

		// 批量插入奖品
		if err := dao.LotteryPresentDao.BatchInsert(monthStr, presents, tx); err != nil {
			// 处理奖品主键冲突
			if isDuplicateKeyError(err) {
				log.Error("奖品记录已存在",
					"orderID", order.OrderId,
					"count", len(presents))
				return fmt.Errorf("%w: present records exist", errs.Processing)
			}

			log.Error("奖品批量插入失败",
				"orderID", order.OrderId,
				"count", len(presents),
				"error", err.Error())
			return fmt.Errorf("batch insert presents failed: %w", err)
		}

		return nil
	})

	if err != nil {
		// 处理中间态错误
		var pe *errs.ProcessingError
		if errors.As(err, &pe) {
			return dto.Fail[bool](errs.Processing.Code, errs.Processing.Msg).
				WithError(err)
		}

		return dto.Fail[bool](errs.DBAccessError.Code, errs.DBAccessError.Msg).
			WithError(err).
			WithMsg("数据库操作异常: %v", err)
	}

	return dto.Success(true)
}

// 判断是否主键冲突错误（根据具体数据库实现调整）
func isDuplicateKeyError(err error) bool {
	if err == nil {
		return false
	}

	// 使用GORM的错误判断
	if errors.Is(err, gorm.ErrDuplicatedKey) {
		return true
	}

	// 原生错误判断
	if strings.Contains(strings.ToLower(err.Error()), "duplicate") {
		return true
	}

	return false
}

func (h *ConsumeAndAwardOrderService) insertWithConsume(order *model.Order, consume *model.Consume) *dto.Result[bool] {
	//根据req.getBizTimestamp() 获取yyyyMM
	lo, _ := time.LoadLocation(shanghaiLocName)
	localTime := time.Unix(order.BizTimestamp, 0).In(lo)
	// 格式化为 yyyyMM
	monthStr := localTime.Format(monthFormat)
	db := dao.GetDb()

	err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Error; err != nil {
			return fmt.Errorf("事务初始化失败: %w", err)
		}

		// 插入主订单（使用事务句柄）
		if err := dao.LotteryOrderDao.Insert(monthStr, order, tx); err != nil {
			log.Error("主订单插入失败",
				"err", err,
				"orderID", order.OrderId,
				"table", monthStr)
			return fmt.Errorf("主订单插入失败: %w", err)
		}

		if err := dao.LotteryConsumeDao.InitConsume(monthStr, consume, tx); err != nil {
			log.Error("消费记录初始化插入失败",
				"err", err,
				"orderID", order.OrderId,
				"consume", consume)
			return fmt.Errorf("消费记录初始化插入失败: %w", err)
		}

		return nil
	})

	if err != nil {
		return dto.Fail[bool](errs.Processing.Code, errs.Processing.Msg).
			WithError(err).
			WithMsg("数据库操作异常: %v", err).
			WithData(false)
	}
	return dto.Success(true)

}

func (h *ConsumeAndAwardOrderService) updateOrderFailAndInsert(order *model.RefundOrder) error {

	// 2. 时区处理
	c := carbon.CreateFromTimestamp(order.BizTimestamp)
	monthStr := c.Format("Ymd")[:6]

	// 3. 获取数据库连接（增加错误检查）
	db := dao.GetDb()
	// 4. 事务处理
	return db.Transaction(func(tx *gorm.DB) error {
		// 4.1 插入退款订单
		if err := dao.LotteryRefundDao.Insert(order, tx); err != nil {
			log.Error("Refund order insert failed",
				"error", err,
				"orderID", order.OrderId,
				"refundID", order.RefundId,
				"function", "updateOrderFailAndInsert")
			return fmt.Errorf("refund order insert failed: %w", err)
		}

		// 4.2 更新主订单状态
		affectedRows, err := dao.LotteryOrderDao.UpdateWithFail(monthStr, order.OrderId, tx)
		if err != nil {
			log.Error("Main order update failed",
				"error", err,
				"orderID", order.OrderId,
				"refundID", order.RefundId,
				"table", monthStr,
				"function", "updateOrderFailAndInsert")
			return fmt.Errorf("main order update failed: %w", err)
		}

		if affectedRows != 1 {
			log.Error("Unexpected affected rows",
				"orderID", order.OrderId,
				"refundID", order.RefundId,
				"affectedRows", affectedRows,
				"expectedRows", 1,
				"table", monthStr,
				"function", "updateOrderFailAndInsert")
			return fmt.Errorf("unexpected affected rows: got %d, want 1", affectedRows)
		}

		return nil
	})
}

func (baseService *BaseOrderService) getByOrderIdSupportCrossMonth(context context.Context, bizTime int64, orderId string) (*dto.OrderDTO, error) {

	//先查上个月订单
	c := carbon.CreateFromTimestamp(bizTime)
	currentMonth := c.Format("Ymd")[:6]
	order, err := dao.LotteryOrderDao.SelectByOrderId(context, currentMonth, orderId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("query current month failed: %w", err)
	}
	if order != nil {
		return orderToOrderDTO(order)
	}
	// 未找到时查询上个月表
	lastMonth := c.SubMonth().Format("Ymd")[:6]
	lastMonthOrder, err := dao.LotteryOrderDao.SelectByOrderId(context, lastMonth, orderId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 两表都未找到返回nil
		}
		return nil, fmt.Errorf("query last month failed: %w", err)
	}
	if lastMonthOrder != nil {
		return orderToOrderDTO(lastMonthOrder)
	}
	return nil, nil
}

func orderToOrderDTO(order *model.Order) (*dto.OrderDTO, error) {
	return &dto.OrderDTO{
		OrderID:      order.OrderId,
		PlatUid:      cast.ToString(order.PlatUid),
		OrderType:    order.OrderType,
		Status:       order.Status,
		BizTimestamp: order.BizTimestamp,
		MapExt:       order.MapExt,
		CreateTime:   order.CreateTime,
		UpdateTime:   order.UpdateTime,
	}, nil
}
