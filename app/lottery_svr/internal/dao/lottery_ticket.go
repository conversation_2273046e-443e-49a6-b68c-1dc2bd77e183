package dao

import (
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"

	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
)

type lotteryTicketDao struct {
}

var LotteryTicketDao *lotteryTicketDao = &lotteryTicketDao{}

func (d *lotteryTicketDao) InsertLotteryTicketIgnore(ctx context.Context, ticket *model.LotteryTicket) (int64, error) {
	sql := `INSERT IGNORE INTO lottery_ticket
             (ticket_id, plat_code, lottery_id, ticket_name, ticket_desc, total_num, used_num,
              expire_secs, single_draw_ticket_num, create_time, update_time)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
	result := GetDb("bmpAdmin").WithContext(ctx).Exec(sql,
		ticket.TicketId,
		ticket.PlatCode,
		ticket.LotteryId,
		ticket.TicketName,
		ticket.TicketDesc,
		ticket.TotalNum,
		ticket.UsedNum,
		ticket.ExpireSecs,
		ticket.SingleDrawTicketNum,
		ticket.CreateTime,
		ticket.UpdateTime,
	)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (d *lotteryTicketDao) QueryLotteryTickets(ctx context.Context, platCode string, activityId string, ticketId int32, ticketName string, pagination *model.Pagination) ([]*model.LotteryTicket, int64, error) {
	sql := `SELECT ticket_id, plat_code, lottery_id, ticket_name, ticket_desc, total_num, used_num,
            expire_secs, single_draw_ticket_num, create_time, update_time
            FROM lottery_ticket WHERE 1=1`
	countSql := `SELECT COUNT(*) FROM lottery_ticket WHERE 1=1`
	var params []interface{}
	var countParams []interface{}

	if platCode != "" {
		sql += " AND plat_code = ?"
		countSql += " AND plat_code = ?"
		params = append(params, platCode)
		countParams = append(countParams, platCode)
	}

	if activityId != "" {
		sql += " AND lottery_id = ?"
		countSql += " AND lottery_id = ?"
		params = append(params, activityId)
		countParams = append(countParams, activityId)
	}

	if ticketId > 0 {
		sql += " AND ticket_id = ?"
		countSql += " AND ticket_id = ?"
		params = append(params, ticketId)
		countParams = append(countParams, ticketId)
	}

	if ticketName != "" {
		sql += " AND ticket_name LIKE ?"
		countSql += " AND ticket_name LIKE ?"
		params = append(params, "%"+ticketName+"%")
		countParams = append(countParams, "%"+ticketName+"%")
	}

	sql += " ORDER BY create_time DESC LIMIT ? OFFSET ?"
	params = append(params, pagination.Limit, pagination.Offset)

	var tickets []*model.LotteryTicket
	var total int64

	// 查询总数
	if err := GetDb("bmpAdmin").WithContext(ctx).Raw(countSql, countParams...).Scan(&total).Error; err != nil {
		return nil, 0, err
	}

	// 查询分页数据
	result := GetDb("bmpAdmin").WithContext(ctx).Raw(sql, params...).Scan(&tickets)
	if result.Error != nil {
		return nil, 0, result.Error
	}
	return tickets, total, nil
}

func (d *lotteryTicketDao) GetLotteryTicketByActivityId(ctx context.Context, activityId int32) (*model.LotteryTicket, error) {
	sql := `SELECT ticket_id, plat_code, lottery_id, ticket_name, ticket_desc, total_num, used_num,
			expire_secs, single_draw_ticket_num, create_time, update_time
			FROM lottery_ticket
			WHERE lottery_id = ?`
	var ticket model.LotteryTicket
	db := GetDb("bmpAdmin").WithContext(ctx).Raw(sql, activityId).Scan(&ticket)
	if db.RowsAffected == 0 {
		return nil, nil
	}
	if db.Error != nil {
		return nil, db.Error
	}
	return &ticket, nil
}

func (d *lotteryTicketDao) GetLotteryTicketByTicketId(ctx context.Context, ticketId int32) (*model.LotteryTicket, error) {
	sql := `SELECT ticket_id, plat_code, lottery_id, ticket_name, ticket_desc, total_num, used_num,
			expire_secs, single_draw_ticket_num, create_time, update_time
			FROM lottery_ticket
			WHERE ticket_id = ?`
	var ticket model.LotteryTicket
	db := GetDb("bmpAdmin").WithContext(ctx).Raw(sql, ticketId).Scan(&ticket)
	if db.RowsAffected == 0 {
		return nil, nil
	}
	if db.Error != nil {
		return nil, db.Error
	}
	return &ticket, nil
}

func (d *lotteryTicketDao) UpdateLotteryTicket(ctx context.Context, ticket *model.LotteryTicket) (int64, error) {
	sql := `UPDATE lottery_ticket
			SET ticket_name = ?, ticket_desc = ?, total_num = ?,
			expire_secs = ?, single_draw_ticket_num = ?, update_time = ?
			WHERE ticket_id = ?`
	// 打印更新前的数据
	log.Warnf("开始更新抽奖券,ticket_id=%d,更新数据:%+v", ticket.TicketId, ticket)
	result := GetDb("bmpAdmin").WithContext(ctx).Exec(sql,
		ticket.TicketName,
		ticket.TicketDesc,
		ticket.TotalNum,
		ticket.ExpireSecs,
		ticket.SingleDrawTicketNum,
		ticket.UpdateTime,
		ticket.TicketId,
	)
	if result.Error != nil {
		log.Warnf("更新抽奖券失败,ticket_id=%d,err:%v", ticket.TicketId, result.Error)
		return 0, result.Error
	}
	log.Warnf("更新抽奖券成功,ticket_id=%d,影响行数:%d", ticket.TicketId, result.RowsAffected)
	return result.RowsAffected, nil
}
