package dao

import (
	"context"
	"fmt"
	"time"

	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"gorm.io/gorm"
)

var LotteryPresentDao *lotteryPresentDao = &lotteryPresentDao{}

type lotteryPresentDao struct {
}

func (d *lotteryPresentDao) presentTableField() string {
	return "present_id,order_id, biz_timestamp,open_id,plat_uid,access_id,app_id,prize_type, prize_id, " +
		"prize_name,prize_num, expire_time ,retry_count,status, map_ext, client_ip, device_id, create_time, update_time "
}

func (d *lotteryPresentDao) presentTableName(yearMonth string) string {
	return "t_present_" + yearMonth
}

func (d *lotteryPresentDao) SelectPresentListByOrderId(ctx context.Context, yearMonth string, orderId string) ([]*model.Present, error) {
	var presents []*model.Present
	sql := "SELECT " + d.presentTableField() + " FROM " + d.presentTableName(yearMonth) + " WHERE orderId = ? "
	db := GetDb().WithContext(ctx).Raw(sql, orderId).Scan(&presents)
	if err := db.Error; err != nil {
		return nil, err
	}
	if db.RowsAffected == 0 {
		return nil, nil
	}
	return presents, nil
}

func (d *lotteryPresentDao) BatchInsert(yearMonth string, presentList []*model.Present, db *gorm.DB) error {
	tx := db.Table(d.presentTableName(yearMonth)).Create(presentList)
	if err := tx.Error; err != nil {
		return err
	}
	return nil
}

func (d *lotteryPresentDao) UpdatePresentWithSuccess(ctx context.Context, yearMonth string, presentId string) (int64, error) {
	updateTime := time.Now()

	sql := "UPDATE " + d.presentTableName(yearMonth) +
		" SET status = 1, update_time = ? WHERE present_id = ? AND status = 0"

	result := GetDb().WithContext(ctx).Exec(sql, updateTime, presentId)
	if err := result.Error; err != nil {
		return 0, fmt.Errorf("failed to update lottery order: %w", err)
	}
	return result.RowsAffected, nil
}

func (d *lotteryPresentDao) FindPresentByOrderId(ctx context.Context, yearMonth string, presentId string) (*model.Present, error) {
	var present model.Present
	sql := "SELECT " + d.presentTableField() + " FROM " + d.presentTableName(yearMonth) + " WHERE present_id = ? LIMIT 1"
	db := GetDb().WithContext(ctx).Raw(sql, presentId).Scan(&present)
	if err := db.Error; err != nil {
		return nil, err
	}
	if db.RowsAffected == 0 {
		return nil, nil
	}
	return &present, nil
}
