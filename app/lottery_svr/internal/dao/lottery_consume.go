package dao

import (
	"context"
	"fmt"
	"time"

	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"gorm.io/gorm"
)

var LotteryConsumeDao *lotteryConsumeDao = &lotteryConsumeDao{}

type lotteryConsumeDao struct {
}

func consumeTableField() string {
	return "consume_id,order_id, order_time,open_id,plat_uid,access_id,app_id, consume_type, " +
		"asset_id, asset_num, status, map_ext, client_ip, device_id, create_time, update_time "
}

func consumeTableName(yearMonth string) string {
	return "t_consume_" + yearMonth
}

func (d *lotteryConsumeDao) SelectConsumeListByOrderId(ctx context.Context, yearMonth string, orderId string) ([]*model.Consume, error) {
	var consumes []*model.Consume
	sql := "SELECT " + consumeTableField() + " FROM " + consumeTableName(yearMonth) + " WHERE order_id = ? "
	db := GetDb().WithContext(ctx).Raw(sql, orderId).Scan(&consumes)
	if err := db.Error; err != nil {
		return nil, err
	}
	if db.RowsAffected == 0 {
		return nil, nil
	}
	return consumes, nil
}

func (d *lotteryConsumeDao) InitConsume(yearMonth string, consume *model.Consume, db *gorm.DB) error {
	tx := db.Table(consumeTableName(yearMonth)).Create(consume)
	if err := tx.Error; err != nil {
		return err
	}
	return nil

}

func (d *lotteryConsumeDao) updateWithSuccess(ctx context.Context, yearMonth string, consumeId string) (int64, error) {
	updateTime := time.Now()

	sql := "UPDATE " + consumeTableName(yearMonth) +
		" SET status = 1, update_time = ? WHERE consume_id = ? AND status = 0"

	result := GetDb().WithContext(ctx).Exec(sql, updateTime, consumeId)
	if err := result.Error; err != nil {
		return 0, fmt.Errorf("failed to update lottery consume: %w", err)
	}
	return result.RowsAffected, nil
}
