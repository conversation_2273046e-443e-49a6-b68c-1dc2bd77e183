package dao

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
)

type lotteryRecordDao struct {
}

var LotteryRecordDao *lotteryRecordDao = &lotteryRecordDao{}

func (d *lotteryRecordDao) InsertLotteryRecordIgnore(ctx context.Context, month string, record *model.LotteryRecord) (int64, error) {
	sql := `INSERT IGNORE INTO t_lottery_` + month + `
             (draw_id, draw_time, open_id, plat_uid, lottery_id, lottery_pool_id, draw_num, draw_result, create_time, update_time)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
	result := GetDb().WithContext(ctx).Exec(sql,
		record.DrawId,
		record.DrawTime,
		record.OpenId,
		record.PlatUid,
		record.LotteryId,
		record.LotteryPoolId,
		record.DrawNum,
		record.DrawResult,
		record.CreateTime,
		record.UpdateTime,
	)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (d *lotteryRecordDao) GetLotteryRecordByDrawId(ctx context.Context, month string, drawId string) (*model.LotteryRecord, error) {
	sql := `SELECT draw_id, draw_time, open_id, plat_uid, lottery_id, lottery_pool_id, draw_num, draw_result, create_time, update_time 
            FROM t_lottery_` + month + ` WHERE draw_id = ?`
	var record model.LotteryRecord
	db := GetDb().WithContext(ctx).Raw(sql, drawId).Scan(&record)
	// 检查是否有记录
	if db.RowsAffected == 0 {
		return nil, nil
	}
	if db.Error != nil {
		return nil, db.Error
	}
	return &record, nil
}

// SelectLotteryRecordsByOrderId 根据订单ID查询抽奖记录（order_id等于draw_id）
func (d *lotteryRecordDao) SelectLotteryRecordsByOrderId(ctx context.Context, yearMonth string, orderId string) ([]*model.LotteryRecord, error) {
	var records []*model.LotteryRecord
	sql := `SELECT draw_id, draw_time, open_id, plat_uid, lottery_id, lottery_pool_id, draw_num, draw_result, create_time, update_time
            FROM t_lottery_` + yearMonth + ` WHERE draw_id = ?`

	db := GetDb().WithContext(ctx).Raw(sql, orderId).Scan(&records)
	if err := db.Error; err != nil {
		log.Warnf("SelectLotteryRecordsByOrderId failed, yearMonth: %s, orderId: %s, error: %v", yearMonth, orderId, err)
		return nil, err
	}
	return records, nil
}
