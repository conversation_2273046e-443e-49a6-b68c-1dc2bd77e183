package dao

import (
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"context"
	"fmt"
	"time"

	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"gorm.io/gorm"
)

var LotteryOrderDao *lotteryOrderDao = &lotteryOrderDao{}

type lotteryOrderDao struct {
}

func (d *lotteryOrderDao) tableName(yearMonth string) string {
	return "t_order_" + yearMonth
}

func (d *lotteryOrderDao) tableField() string {
	return "order_id,access_id,app_id,open_id,plat_uid, order_type, status, biz_timestamp, map_ext, " +
		"client_ip, device_id, create_time, update_time "
}

func (d *lotteryOrderDao) SelectByOrderId(ctx context.Context, yearMonth string, orderId string) (*model.Order, error) {
	var order model.Order
	sql := "SELECT " + d.table<PERSON>ield() + " FROM " + d.tableName(yearMonth) + " WHERE order_id = ? "
	db := GetDb().WithContext(ctx).Raw(sql, orderId).Scan(&order)
	if err := db.Error; err != nil {
		log.Warnf("SelectByOrderId failed, yearMonth: %s, orderId: %s, error: %v", yearMonth, orderId, err)
		return nil, err
	}
	if db.RowsAffected == 0 {
		return nil, nil
	}
	return &order, nil
}

func (d *lotteryOrderDao) Insert(yearMonth string, order *model.Order, db *gorm.DB) error {
	tx := db.Table(d.tableName(yearMonth)).Create(order)
	if err := tx.Error; err != nil {
		log.Warnf("Insert lottery order failed, yearMonth: %s, orderId: %s, error: %v", yearMonth, order.OrderId, err)
		return err
	}
	return nil

}

func (d *lotteryOrderDao) UpdateWithSuccess(ctx context.Context, yearMonth string, orderId string) (int64, error) {
	updateTime := time.Now()

	sql := "UPDATE " + d.tableName(yearMonth) +
		" SET status = 1, update_time = ? WHERE order_id = ? AND status = 0"

	result := GetDb().WithContext(ctx).Exec(sql, updateTime, orderId)
	if err := result.Error; err != nil {
		log.Warnf("UpdateWithSuccess failed, yearMonth: %s, orderId: %s, error: %v", yearMonth, orderId, err)
		return 0, fmt.Errorf("failed to update lottery order: %w", err)
	}
	return result.RowsAffected, nil
}

func (d *lotteryOrderDao) UpdateWithFail(yearMonth string, orderId string, db *gorm.DB) (int64, error) {
	updateTime := time.Now()
	sql := "UPDATE " + d.tableName(yearMonth) + " SET status = 2, update_time = ? WHERE order_id = ? AND status = 0"
	result := db.Exec(sql, updateTime, orderId)
	if err := result.Error; err != nil {
		log.Warnf("UpdateWithFail failed, yearMonth: %s, orderId: %s, error: %v", yearMonth, orderId, err)
		return 0, fmt.Errorf("failed to update lottery order: %w", err)
	}
	return result.RowsAffected, nil
}

// SelectPendingOrdersInTimeRange 查询指定时间范围内处于初始化状态的订单
func (d *lotteryOrderDao) SelectPendingOrdersInTimeRange(ctx context.Context, yearMonth string, startMinutes, endMinutes int, limit int) ([]*model.Order, error) {
	var orders []*model.Order
	sql := "SELECT " + d.tableField() + " FROM " + d.tableName(yearMonth) +
		" WHERE status = 0 AND create_time BETWEEN DATE_SUB(NOW(), INTERVAL ? MINUTE) AND DATE_SUB(NOW(), INTERVAL ? MINUTE) ORDER BY create_time ASC LIMIT ?"

	db := GetDb().WithContext(ctx).Raw(sql, startMinutes, endMinutes, limit).Scan(&orders)
	if err := db.Error; err != nil {
		log.Warnf("SelectPendingOrdersInTimeRange failed, yearMonth: %s, startMinutes: %d, endMinutes: %d, error: %v",
			yearMonth, startMinutes, endMinutes, err)
		return nil, err
	}
	return orders, nil
}
