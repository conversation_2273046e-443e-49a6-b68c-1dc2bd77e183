package dao

import (
	"context"
	"time"

	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
)

type lotteryActivityDao struct {
}

var LotteryActivityDao *lotteryActivityDao = &lotteryActivityDao{}

func (d *lotteryActivityDao) InsertLotteryActivityIgnore(ctx context.Context, activity *model.LotteryActivity) (int64, error) {
	sql := `INSERT IGNORE INTO lottery_activity
             (lottery_id, plat_code, activity_name, activity_desc, start_time, end_time, status, pool_info, boss_account_id, 
              boss_app_id, boss_app_secret, boss_bid, lottery_admin, create_user, update_user, create_time, update_time)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
	result := GetDb("bmpAdmin").WithContext(ctx).Exec(sql,
		activity.LotteryId,
		activity.PlatCode,
		activity.ActivityName,
		activity.ActivityDesc,
		activity.StartTime,
		activity.EndTime,
		activity.Status,
		activity.PoolInfo,
		activity.BossAccountId,
		activity.BossAppId,
		activity.BossAppSecret,
		activity.BossBid,
		activity.LotteryAdmin,
		activity.CreateUser,
		activity.UpdateUser,
		activity.CreateTime,
		activity.UpdateTime,
	)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (d *lotteryActivityDao) GetLotteryActivities(ctx context.Context, platCode string, activityId string, activityName string,
	activityStatus int32, pagination *model.Pagination) ([]*model.LotteryActivity, int64, error) {
	sql := `SELECT lottery_id, plat_code, activity_name, activity_desc, start_time, end_time, status, pool_info, 
			boss_account_id, boss_app_id, boss_app_secret, boss_bid, lottery_admin, create_user, update_user, create_time, update_time
			FROM lottery_activity
			WHERE plat_code = ?`
	countSql := `SELECT COUNT(*) FROM lottery_activity WHERE plat_code = ?`
	params := []interface{}{platCode}
	countParams := []interface{}{platCode}

	if activityId != "" {
		sql += ` AND lottery_id = ?`
		countSql += ` AND lottery_id = ?`
		params = append(params, activityId)
		countParams = append(countParams, activityId)
	}

	if activityName != "" {
		sql += ` AND activity_name LIKE ?`
		countSql += ` AND activity_name LIKE ?`
		params = append(params, "%"+activityName+"%")
		countParams = append(countParams, "%"+activityName+"%")
	}

	if activityStatus != 0 {
		sql += ` AND status = ?`
		countSql += ` AND status = ?`
		params = append(params, activityStatus)
		countParams = append(countParams, activityStatus)
	}

	sql += ` ORDER BY create_time DESC LIMIT ?, ?`
	params = append(params, pagination.Offset, pagination.Limit)

	var activities []*model.LotteryActivity
	err := GetDb("bmpAdmin").WithContext(ctx).Raw(sql, params...).Scan(&activities).Error
	if err != nil {
		return nil, 0, err
	}

	var totalNum int64
	err = GetDb("bmpAdmin").WithContext(ctx).Raw(countSql, countParams...).Scan(&totalNum).Error
	if err != nil {
		return nil, 0, err
	}

	return activities, totalNum, nil
}

func (d *lotteryActivityDao) GetLotteryActivityById(ctx context.Context, lotteryId int32) (*model.LotteryActivity, error) {
	sql := `SELECT lottery_id, plat_code, activity_name, activity_desc, start_time, end_time, status, pool_info,
			boss_account_id, boss_app_id, boss_app_secret, boss_bid, lottery_admin, create_user, update_user, create_time, update_time
			FROM lottery_activity
			WHERE lottery_id = ?`
	var activity model.LotteryActivity
	db := GetDb("bmpAdmin").WithContext(ctx).Raw(sql, lotteryId).Scan(&activity)
	if db.RowsAffected == 0 {
		return nil, nil
	}
	if db.Error != nil {
		return nil, db.Error
	}
	return &activity, nil
}

func (d *lotteryActivityDao) UpdateLotteryActivityById(ctx context.Context, activity *model.LotteryActivity) (int64, error) {
	sql := `UPDATE lottery_activity SET
			plat_code = ?,
			activity_name = ?,
			activity_desc = ?,
			start_time = ?,
			end_time = ?,
			status = ?,
			pool_info = ?,
			boss_account_id = ?,
			boss_app_id = ?,
			boss_app_secret = ?,
			boss_bid = ?,	
			lottery_admin = ?,
			update_user = ?,
			update_time = ?
			WHERE lottery_id = ?`
	result := GetDb("bmpAdmin").WithContext(ctx).Exec(sql,
		activity.PlatCode,
		activity.ActivityName,
		activity.ActivityDesc,
		activity.StartTime,
		activity.EndTime,
		activity.Status,
		activity.PoolInfo,
		activity.BossAccountId,
		activity.BossAppId,
		activity.BossAppSecret,
		activity.BossBid,
		activity.LotteryAdmin,
		activity.UpdateUser,
		time.Now(),
		activity.LotteryId,
	)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (d *lotteryActivityDao) GetMaxLotteryId(ctx context.Context, platCode string) (int32, error) {
	sql := `SELECT max(lottery_id) FROM lottery_activity WHERE plat_code = ?`
	var maxLotteryId *int32
	err := GetDb("bmpAdmin").WithContext(ctx).Raw(sql, platCode).Scan(&maxLotteryId).Error
	if err != nil {
		return 0, err
	}
	if maxLotteryId == nil {
		return 0, nil
	}
	return *maxLotteryId, nil
}
