package dao

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"gorm.io/gorm"
)

var LotteryRefundDao *lotteryRefundDao = &lotteryRefundDao{}

type lotteryRefundDao struct {
}

// 创建退款订单

func (dao *lotteryRefundDao) Insert(order *model.RefundOrder, db *gorm.DB) error {
	tx := db.Table(dao.refundTableName()).Create(order)
	if err := tx.Error; err != nil {
		return err
	}
	return nil
}

func (dao *lotteryRefundDao) refundTableName() string {
	return "t_refund_order"
}
