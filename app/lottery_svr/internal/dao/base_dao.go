package dao

import (
	kgmysql "cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/common/mysql"
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/config"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"github.com/samber/lo"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

//	type Dao struct {
//		//gormDB *gorm.DB
//		//kgDB   *kgmysql.DB
//	}
//
// var d *Dao = &Dao{}
var dbMap = make(map[string]*gorm.DB)

func GetDb(section ...string) *gorm.DB {
	if len(section) == 0 {
		return dbMap["bmpLottery"]
	}
	return dbMap[section[0]]
}

func Init(c *config.Config) (map[string]*gorm.DB, error) {
	lo.ForEach(lo.Entries(c.MysqlConfig), func(item lo.Entry[string, *kgmysql.Config], _ int) {
		kgDB, err := kgmysql.Open(item.Value, kgmysql.WithPing())
		if err != nil {
			log.Warnf("mysql.Open err:%v", err)
			panic(err)
		}
		gormDB, err := gorm.Open(mysql.New(mysql.Config{
			Conn: kgDB.GetDefaultSQLDB(),
		}), &gorm.Config{TranslateError: true})
		if err != nil {
			log.Warnf("gorm.Open err:%v", err)
			panic(err)
		}
		//d = &Dao{
		//	kgDB:   kgDB,
		//	gormDB: gormDB,
		//}
		dbMap[item.Key] = gormDB
	})
	return dbMap, nil
}
