package errs

import (
	"fmt"
	"reflect"

	"github.com/pkg/errors"
)

type RespCode struct {
	Code int64
	Msg  string
}

func (e *RespCode) WithMsg(msg string) {
	e.Msg = msg
}

type JsonResult struct {
	RespCode
	Data interface{} `json:"data"`
}

// 中间态错误包装类型
type ProcessingError struct {
	Code    RespCode
	Message string
	Origin  error
}

func (e *ProcessingError) Error() string {
	return fmt.Sprintf("[%d]%s: %v", e.Code.Code, e.Message, e.Origin)
}

var (

	// 成功
	Success = RespCode{0, "操作成功"}

	// 中间态 (1xxxxx)
	Processing        = RespCode{100001, "处理中"}
	ThirdPartyPending = RespCode{100002, "第三方处理中"}
	OperateConflict   = RespCode{100003, "操作太快，并发冲突"}

	// 通用错误，明确错误 (2xxxxx)
	InvalidRequest      = RespCode{200001, "非法请求"}
	ParamError          = RespCode{200002, "参数错误"}
	Failure             = RespCode{200003, "操作失败"}
	NotImplement        = RespCode{200004, "方法未实现"}
	PlatCodeNotFound    = RespCode{200005, "平台代号错误"}
	RewardIdNotFound    = RespCode{200006, "福利ID错误"}
	ActivityNotFound    = RespCode{200007, "抽奖配置ID不存在"}
	CommonParamError    = RespCode{200008, "公共参数错误"}
	ActivityRightError  = RespCode{200009, "无新增活动权限，请申请运营者权限"}
	LotteryRightError   = RespCode{200010, "无新增抽奖券权限，请申请运营者权限"}
	AdminNotFound       = RespCode{200011, "管理员不存在"}
	HasNoPermission     = RespCode{200012, "无权限操作，请联系管理员"}
	OrderRecheckFail    = RespCode{200013, "福利礼包订单回查校验失败"}
	OpenIdNotFound      = RespCode{200014, "Openid转平台Uid失败"}
	DuplicatePoolId     = RespCode{200015, "奖池ID重复"}
	ParsePoolFailure    = RespCode{200016, "奖池解析失败"}
	TicketNotFound      = RespCode{200017, "抽奖券不存在"}
	ActivityIdInvalid   = RespCode{200018, "抽奖活动ID无效"}
	InvalidActivityJson = RespCode{200019, "导入抽奖活动JSON格式错误"}

	// 系统错误
	DBAccessError       = RespCode{200101, "数据库访问异常"}
	RPCError            = RespCode{200102, "服务通信异常"}
	NetworkError        = RespCode{200103, "网络异常"}
	GrpcConnectionError = RespCode{200104, "grpc服务链接失败"}
	HandlerNotFound     = RespCode{200105, "handler未实现"}

	// 业务错误
	OrderStatusError       = RespCode{200201, "订单状态异常"}
	NotEnough              = RespCode{200202, "余额不足"}
	IdempotentConflict     = RespCode{200203, "幂等请求冲突"}
	AssetPresentFailed     = RespCode{200204, "资产发放失败"}
	IdempotentOrderTypeErr = RespCode{200205, "幂等处理有误,原订单类型与所调用接口类型不符合"}
	LotteryFailedRefund    = RespCode{200206, "抽奖失败，订单退款"}
	PoolCheckFailure       = RespCode{200208, "奖池检查不通过"}
	LotteryFailed          = RespCode{200209, "订单不存在"}

	// 第三方错误
	ThirdPartyError   = RespCode{200301, "第三方服务异常"}
	ThirdPartyTimeout = RespCode{200302, "第三方服务超时"}
)

func Response[T any](respCode RespCode) *T {
	resp := new(T)
	val := reflect.ValueOf(resp).Elem()
	if codeField := val.FieldByName("Code"); codeField.IsValid() {
		codeField.SetInt(respCode.Code)
	}
	if msgField := val.FieldByName("Msg"); msgField.IsValid() {
		msgField.SetString(respCode.Msg)
	}
	return resp
}

func ResponseWithData[T any](respCode RespCode, data interface{}) *T {
	resp := new(T)
	val := reflect.ValueOf(resp).Elem()
	if codeField := val.FieldByName("Code"); codeField.IsValid() {
		codeField.SetInt(respCode.Code)
	}
	if msgField := val.FieldByName("Msg"); msgField.IsValid() {
		msgField.SetString(respCode.Msg)
	}
	if dataField := val.FieldByName("Data"); dataField.IsValid() {
		dataField.Set(reflect.ValueOf(data))
	}
	return resp
}

func IsProcessingError(err error) bool {
	var pe *ProcessingError
	return errors.As(err, &pe)
}
