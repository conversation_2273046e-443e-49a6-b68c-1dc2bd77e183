package constant

type PrizeType int

// PrizeType 奖项类型（PrizeType）只能追加禁止修改
const (
	PrizeTypeNothing    PrizeType = 0 // 谢谢参与
	PrizeTypeLocalProps PrizeType = 1 // 本地道具
	PrizeTypeReward     PrizeType = 2 // 平台福利（https://dayu.tmeoa.com/home/<USER>/t_uni_welfare_info)
	PrizeTypeGameReward PrizeType = 3 // 游戏福利（https://kube.tmeoa.com/projects/minigame_platform/#/reward)
	PrizeTypeBossAsset  PrizeType = 4 // Boss资产
)

type PrizeTypeInfo struct {
	Id       PrizeType // 奖项标识
	Code     string    // 奖项编码
	Name     string    // 奖项名称
	PlatCode []string  // 展示平台
}

var PrizeTypeInfos = map[PrizeType]PrizeTypeInfo{
	PrizeTypeLocalProps: {PrizeTypeLocalProps, "local_props", "本地道具", []string{PlatIdQm.Code(), PlatIdKugou.Code()}},
	PrizeTypeNothing:    {PrizeTypeNothing, "reward_nothing", "谢谢参与", []string{PlatIdQm.Code(), PlatIdKugou.Code()}},
	PrizeTypeReward:     {PrizeTypeReward, "reward_pack", "平台福利礼包", []string{PlatIdQm.Code()}},
	PrizeTypeGameReward: {PrizeTypeGameReward, "game_reward", "游戏福利礼包", []string{PlatIdQm.Code()}},
	PrizeTypeBossAsset:  {PrizeTypeBossAsset, "boss_asset", "Boss资产", []string{}},
}

var PrizeTypeCodeMap = func() map[string]PrizeTypeInfo {
	m := make(map[string]PrizeTypeInfo)
	for _, info := range PrizeTypeInfos {
		m[info.Code] = info
	}
	return m
}()

func (p PrizeType) Code() string {
	if info, ok := PrizeTypeInfos[p]; ok {
		return info.Code
	}
	return "unknown"
}

func (p PrizeType) Name() string {
	if info, ok := PrizeTypeInfos[p]; ok {
		return info.Name
	}
	return "Unknown platform"
}

func GetPrizeTypeInfoByCode(code string) (*PrizeTypeInfo, bool) {
	info, ok := PrizeTypeCodeMap[code]
	return &info, ok
}

func GetPrizeTypeInfoById(id PrizeType) (*PrizeTypeInfo, bool) {
	info, ok := PrizeTypeInfos[id]
	return &info, ok
}
