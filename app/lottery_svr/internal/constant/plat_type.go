package constant

import "cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"

type PlatId int

const (
	PlatIdKugou   PlatId = 1
	PlatIdQm      PlatId = 2
	PlatIdQQMusic PlatId = 3
)

type PlatInfo struct {
	Id   PlatId
	Code string
	Name string
}

var PlatIdInfos = map[PlatId]PlatInfo{
	PlatIdQm:      {PlatIdQm, "qm", "全民"},
	PlatIdQQMusic: {PlatIdQm, "qqmusic", "Q音"},
	PlatIdKugou:   {PlatIdKugou, "kugou", "酷狗"},
}

var PlatCodeMap = func() map[string]PlatInfo {
	m := make(map[string]PlatInfo)
	for _, info := range PlatIdInfos {
		m[info.Code] = info
	}
	return m
}()

func (p PlatId) Code() string {
	if info, ok := PlatIdInfos[p]; ok {
		return info.Code
	}
	return "unknown"
}

func (p PlatId) Name() string {
	if info, ok := PlatIdInfos[p]; ok {
		return info.Name
	}
	return "Unknown platform"
}

func (p PlatInfo) IsKugouPlat() bool {
	return p.Id == PlatIdKugou
}

func GetPlatInfoByCode(code string) (*PlatInfo, bool) {
	info, ok := PlatCodeMap[code]
	return &info, ok
}

func (p PlatInfo) GetNextLotteryId(maxLotteryId int32) int32 {
	// 根据平台ID计算下一个抽奖ID
	if maxLotteryId < int32(p.Id)*10000000 {
		return int32(p.Id) * 10000000
	}
	return maxLotteryId + 1
}

func (p PlatInfo) IsValidLotteryId(lotteryId int32) bool {
	startLotteryId := int32(p.Id) * 10000000
	endLotteryId := (int32(p.Id) + 1) * 10000000
	log.Warnf("检查LotteryId合法性校验，取值范围[%d, %d]，当前值[%d]", startLotteryId, endLotteryId, lotteryId)
	return lotteryId >= startLotteryId && lotteryId < endLotteryId
}
