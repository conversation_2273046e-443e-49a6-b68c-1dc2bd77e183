package constant

type OrderType int

// OrderType 奖项类型（OrderType）只能追加禁止修改
const (
	OrderTypeLottery                OrderType = 1
	OrderTypeLotteryDelivery        OrderType = 2
	OrderTypeConsumeLotteryDelivery OrderType = 3
	OrderTypeDeliveryTicket         OrderType = 4
)

type OrderTypeInfo struct {
	Id   OrderType
	Code string
	Name string
}

var OrderTypeInfos = map[OrderType]OrderTypeInfo{
	OrderTypeLottery:                {OrderTypeLottery, "lottery", "纯抽奖"},
	OrderTypeLotteryDelivery:        {OrderTypeLotteryDelivery, "lottery_delivery", "抽奖发奖"},
	OrderTypeConsumeLotteryDelivery: {OrderTypeConsumeLotteryDelivery, "consume_lottery_delivery", "扣抽奖券抽奖发奖"},
	OrderTypeDeliveryTicket:         {OrderTypeDeliveryTicket, "delivery_ticket", "发放抽奖券"},
}

var OrderTypeCodeMap = func() map[string]OrderTypeInfo {
	m := make(map[string]OrderTypeInfo)
	for _, info := range OrderTypeInfos {
		m[info.Code] = info
	}
	return m
}()

func (p OrderType) Code() string {
	if info, ok := OrderTypeInfos[p]; ok {
		return info.Code
	}
	return "unknown"
}

func (p OrderType) Name() string {
	if info, ok := OrderTypeInfos[p]; ok {
		return info.Name
	}
	return "Unknown Name"
}

func GetOrderTypeInfoByCode(code string) (*OrderTypeInfo, bool) {
	info, ok := OrderTypeCodeMap[code]
	return &info, ok
}

func GetOrderTypeInfoById(id OrderType) (*OrderTypeInfo, bool) {
	info, ok := OrderTypeInfos[id]
	return &info, ok
}
