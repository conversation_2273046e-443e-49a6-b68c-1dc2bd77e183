package utils

import (
	"errors"
	"sync"
	"time"
)

// snowflake 结构体定义
type snowflake struct {
	startTime    int64 // 起始时间戳
	machineID    int64 // 机器ID
	sequence     int64 // 序列号
	lastGenerate int64 // 上次生成ID的时间戳
	mutex        sync.Mutex
}

var sf *snowflake
var once sync.Once

// NewSnowflake 创建一个Snowflake实例
func newSnowflake(startTime, machineID int64) (*snowflake, error) {
	if machineID < 0 || machineID > 1023 {
		return nil, errors.New("machine ID must be between 0 and 1023")
	}
	return &snowflake{
		startTime:    startTime,
		machineID:    machineID,
		sequence:     0,
		lastGenerate: 0,
		mutex:        sync.Mutex{},
	}, nil
}

// GetInstance 获取Snowflake单例实例
func getInstance() *snowflake {
	once.Do(func() {
		// 在此处初始化Snowflake实例，设置合适的起始时间戳和机器ID
		startTime := 1609459200000 // 默认的起始时间戳为2021年1月1日
		machineID := 1             // 示例的机器ID
		sf, _ = newSnowflake(int64(startTime), int64(machineID))
	})
	return sf
}

// GenerateId 生成一个唯一的ID
func GenerateID() int64 {
	s := getInstance()
	s.mutex.Lock()
	defer s.mutex.Unlock()

	timestamp := time.Now().UnixNano() / 1e6
	if timestamp < s.lastGenerate {
		timestamp = s.lastGenerate
	}

	if timestamp == s.lastGenerate {
		s.sequence++
		if s.sequence >= 4096 {
			for timestamp <= s.lastGenerate {
				timestamp = time.Now().UnixNano() / 1e6
			}
			s.sequence = 0
		}
	} else {
		s.sequence = 0
	}

	s.lastGenerate = timestamp

	result := (timestamp-s.startTime)<<22 | (s.machineID << 12) | (s.sequence)
	return result
}
