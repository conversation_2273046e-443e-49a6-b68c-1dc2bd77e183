package utils

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
)

// JsonString 将对象转换为json字符串
func JsonString(v interface{}) (string, error) {
	if v == nil {
		return "", nil
	}
	data, err := json.Marshal(v)
	if err != nil {
		return "", err
	}
	return string(data), nil

}

// Contains 判断字符串是否在数组中
func Contains(strArray []string, str string) bool {
	sort.Strings(strArray)
	index := sort.SearchStrings(strArray, str)
	if index < len(strArray) && strArray[index] == str {
		return true
	}
	return false
}

// InArray 判断元素是否在数组中
func InArray[T int64 | float64 | int | int8](slice []T, a T) bool {
	for _, item := range slice {
		if item == a {
			return true
		}
	}
	return false
}

// IsDigit 判断字符串是否是数字
func IsDigit(s string) bool {
	_, err := strconv.ParseFloat(s, 64)
	return err == nil
}

// Md5 获取Md5值
// @param data
// @return string
func Md5(data interface{}) string {
	h := md5.New()
	stringData := fmt.Sprint(data)
	h.Write([]byte(stringData))
	return hex.EncodeToString(h.Sum(nil))
}

func Int64SliceToString(numbers []int64, sep string) string {
	strNumbers := make([]string, len(numbers))
	for i, num := range numbers {
		strNumbers[i] = strconv.FormatInt(num, 10)
	}
	return strings.Join(strNumbers, sep)
}

// IsEmpty 判断string是否为空
func IsEmpty(str string) bool {
	return len(str) == 0
}

// IsNotEmpty 判断字符串是否不为空
func IsNotEmpty(str string) bool {
	return len(str) != 0
}

func IsBlank(s string) bool {
	return strings.TrimSpace(s) == ""
}

func IsNotBlank(s string) bool {
	return !IsBlank(s)
}

// IsAnyBlank CheckAnyEmpty 检查任意一个字符串是否为空
func IsAnyBlank(strs ...string) bool {
	for _, s := range strs {
		if strings.TrimSpace(s) == "" {
			return true
		}
	}
	return false
}

func ToJSON(data map[string]string, ignoreError bool) (string, error) {
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		if ignoreError {
			return "", nil
		}
		return "", fmt.Errorf("JSON 序列化失败: %w", err)
	}
	return string(jsonBytes), nil
}

func ToJSONIgnoreError(v any) string {
	jsonBytes, err := json.Marshal(v)
	if err != nil {
		return ""
	}
	return string(jsonBytes)
}
