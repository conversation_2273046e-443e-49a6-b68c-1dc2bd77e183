package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
)

type UserInfo struct {
	Id    string `json:"id"`    // 用户在HR唯一id
	Ename string `json:"ename"` // 用户英文名
	Cname string `json:"cname"` // 用户中文名
	Email string `json:"email"` // 用户邮箱
}

// DecodeToken 解析token
// token: 从Header：x-token获取的值
// timestamp：从Header：x-timestamp获取的值
// appsecret：系统在TPP的密钥
func DecodeToken(token, timestamp, appsecret string) (*UserInfo, error) {
	// base64 decode
	ciphertext, err := base64.StdEncoding.DecodeString(token)
	if err != nil {
		return nil, err
	}
	// 获取密钥32位 MD5 字符串
	h := md5.New()
	if _, err = h.Write([]byte(timestamp + appsecret + timestamp)); err != nil {
		return nil, err
	}
	hash := hex.EncodeToString(h.Sum(nil))
	// md5(timestamp + appsecret + timestamp)
	key := []byte(hash[0:16]) // 取MD5哈希字符串前16位作为密钥
	iv := []byte(hash[16:])   // 取MD5哈希字符串后16位作为向量值

	// 解密token(算法：aes-128-cbc)
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockMode := cipher.NewCBCDecrypter(block, iv) // 指定分组模式，返回一个BlockMode接口对象
	plainText := make([]byte, len(ciphertext))     // 接收明文的byte数组
	blockMode.CryptBlocks(plainText, ciphertext)   // 解密
	plainText = unpadding(plainText)               // 删除填充
	// 解析json字符串到结构体
	var user UserInfo
	if err = json.Unmarshal(plainText, &user); err != nil {
		return nil, err
	}
	return &user, err
}

// 对密文删除填充
func unpadding(cipherText []byte) []byte {
	if len(cipherText) == 0 {
		return cipherText
	}
	// 取出密文最后一个字节
	end := cipherText[len(cipherText)-1]

	// 删除填充
	size := len(cipherText) - int(end)
	if size <= 0 || size > len(cipherText) {
		return cipherText
	}

	cipherText = cipherText[:size]
	return cipherText
}
