package model

import "cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/utils"

type TicketCostReq struct {
	ActivityId int32
	DrawNum    int64
}

type TicketCostRsp struct {
	AssetType string
	TicketId  int32
	TicketNum int64
}

type LotteryPresent struct {
	PresentId   int64
	PrizeItemId int64
	PrizeType   string
	PrizeId     int64
	PrizeName   string
	PrizePic    string
	PrizeNum    int64
	ExpireTime  int64
}

type CommonParam struct {
	Token      string
	Timestamp  string
	MethodName string
	UserInfo   *utils.UserInfo
}

// StaffSearchResponse 定义结构体以匹配返回的 JSON 数据
type StaffSearchResponse struct {
	Code int32             `json:"code"`
	Msg  string            `json:"msg"`
	Data []StaffSearchData `json:"data"`
}

type StaffSearchData struct {
	EmplID        string `json:"emplid"`
	NameAC        string `json:"name_ac"`
	NameDisplay   string `json:"name_display"`
	NameFormal    string `json:"name_formal"`
	EmailBusiness string `json:"t_email_busn"`
}
