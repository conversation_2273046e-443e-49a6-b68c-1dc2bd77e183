package model

import (
	"time"
)

type LotteryActivity struct {
	LotteryId     int32     `gorm:"column:lottery_id;type:int(11);comment:抽奖ID;primaryKey;not null;" json:"lottery_id"`                     // 抽奖ID
	PlatCode      string    `gorm:"column:plat_code;type:varchar(10);comment:平台代码;not null;" json:"plat_code"`                              // 平台代码
	ActivityName  string    `gorm:"column:activity_name;type:varchar(100);comment:活动名称;not null;" json:"activity_name"`                     // 活动名称
	ActivityDesc  string    `gorm:"column:activity_desc;type:varchar(200);comment:活动描述;not null;" json:"activity_desc"`                     // 活动描述
	StartTime     time.Time `gorm:"column:start_time;type:datetime;comment:开始时间;not null;default:2025-01-01 00:00:00;" json:"start_time"`   // 开始时间
	EndTime       time.Time `gorm:"column:end_time;type:datetime;comment:结束时间;not null;default:2025-01-01 00:00:00;" json:"end_time"`       // 结束时间
	Status        int8      `gorm:"column:status;type:tinyint(4);comment:状态：0-未开始 1-进行中 2-已结束 3-已关闭;not null;default:0;" json:"status"`     // 状态：0-未开始 1-进行中 2-已结束 3-已关闭
	PoolInfo      string    `gorm:"column:pool_info;type:text;comment:奖池信息;not null;" json:"pool_info"`                                     // 奖池信息
	CreateUser    string    `gorm:"column:create_user;type:varchar(30);comment:创建用户;not null;" json:"create_user"`                          // 创建用户
	UpdateUser    string    `gorm:"column:update_user;type:varchar(30);comment:更新用户;not null;" json:"update_user"`                          // 更新用户
	BossAppId     string    `gorm:"column:boss_app_id;type:varchar(30);comment:boss系统AppId;not null;" json:"boss_app_id"`                   // boss系统AppId
	BossAppSecret string    `gorm:"column:boss_app_secret;type:varchar(50);comment:boss系统密钥;not null;" json:"boss_app_secret"`              // boss系统密钥
	BossBid       int32     `gorm:"column:boss_bid;type:int(11);comment:boss系统bid;not null;default:0;" json:"boss_bid"`                     // boss系统bid
	BossAccountId string    `gorm:"column:boss_account_id;type:varchar(100);comment:Boss账户ID;not null;" json:"boss_account_id"`             // Boss账户ID
	LotteryAdmin  string    `gorm:"column:lottery_admin;type:varchar(200);comment:专属管理员;not null;" json:"lottery_admin"`                    // 专属管理员
	CreateTime    time.Time `gorm:"column:create_time;type:datetime;comment:创建时间;not null;default:2025-01-01 00:00:00;" json:"create_time"` // 创建时间
	UpdateTime    time.Time `gorm:"column:update_time;type:datetime;comment:更新时间;not null;default:2025-01-01 00:00:00;" json:"update_time"` // 更新时间
}

type LotteryTicket struct {
	TicketId            int32     `gorm:"column:ticket_id;type:int(11);comment:抽奖券ID;primaryKey;not null;default:0;" json:"ticket_id"`                   // 抽奖券ID
	PlatCode            string    `gorm:"column:plat_code;type:varchar(10);comment:平台代号;not null;default:kugou;" json:"plat_code"`                       // 平台代号
	LotteryId           int32     `gorm:"column:lottery_id;type:int(11);comment:抽奖活动ID;not null;default:0;" json:"lottery_id"`                           // 抽奖活动ID
	TicketName          string    `gorm:"column:ticket_name;type:varchar(100);comment:券名称;not null;" json:"ticket_name"`                                 // 券名称
	TicketDesc          string    `gorm:"column:ticket_desc;type:varchar(200);comment:抽奖券备注;not null;" json:"ticket_desc"`                               // 抽奖券备注
	TotalNum            int64     `gorm:"column:total_num;type:bigint(20);comment:券总量;not null;default:0;" json:"total_num"`                             // 券总量
	UsedNum             int64     `gorm:"column:used_num;type:bigint(20);comment:消耗数量;not null;default:0;" json:"used_num"`                              // 消耗数量
	ExpireSecs          int64     `gorm:"column:expire_secs;type:bigint(20);comment:有效时间（秒）;not null;default:0;" json:"expire_secs"`                     // 有效时间（秒）
	SingleDrawTicketNum int32     `gorm:"column:single_draw_ticket_num;type:int(10);comment:单次抽奖消耗数量;not null;default:1;" json:"single_draw_ticket_num"` // 单次抽奖消耗数量
	CreateTime          time.Time `gorm:"column:create_time;type:datetime;comment:创建时间;not null;default:2025-01-01 00:00:00;" json:"create_time"`        // 创建时间
	UpdateTime          time.Time `gorm:"column:update_time;type:datetime;comment:更新时间;not null;default:2025-01-01 00:00:00;" json:"update_time"`        // 更新时间
}

// 主订单表
type Order struct {
	OrderId      string    `gorm:"column:order_id;type:varchar(64);comment:主订单;primaryKey;not null;" json:"order_id"`                        // 主订单
	BizTimestamp int64     `gorm:"column:biz_timestamp;type:bigint(20);comment:业务下单时间;not null;default:0;" json:"biz_timestamp"`             // 业务下单时间
	AccessId     string    `gorm:"column:access_id;type:varchar(64);comment:应用接入ID;not null;" json:"access_id"`                              // 应用接入ID
	AppId        string    `gorm:"column:app_id;type:varchar(64);comment:应用ID;not null;" json:"app_id"`                                      // 应用ID
	PlatUid      uint64    `gorm:"column:plat_uid;type:bigint(20) UNSIGNED;comment:平台用户标识;not null;default:0;" json:"plat_uid"`              // 平台用户标识
	OpenId       string    `gorm:"column:open_id;type:varchar(64);comment:中台用户标识;not null;" json:"open_id"`                                  // 中台用户标识
	OrderType    int32     `gorm:"column:order_type;type:int(11);comment:订单类型;not null;default:0;" json:"order_type"`                        // 订单类型
	Status       int32     `gorm:"column:status;type:int(11);comment:订单状态;not null;default:0;" json:"status"`                                // 订单状态
	MapExt       string    `gorm:"column:map_ext;type:varchar(128);comment:业务扩展倀性;" json:"map_ext"`                                          // 业务扩展倀性
	ClientIp     string    `gorm:"column:client_ip;type:varchar(50);comment:客户端IP;not null;" json:"client_ip"`                               // 客户端IP
	DeviceId     string    `gorm:"column:device_id;type:varchar(100);comment:设备号;not null;" json:"device_id"`                                // 设备号
	CreateTime   time.Time `gorm:"column:create_time;type:datetime;comment:系统下单时间;not null;default:2025-01-01 00:00:00;" json:"create_time"` // 系统下单时间
	UpdateTime   time.Time `gorm:"column:update_time;type:datetime;comment:更新时间;not null;default:2025-01-01 00:00:00;" json:"update_time"`   // 更新时间
}

// 消费订单表
type Consume struct {
	ConsumeId   string    `gorm:"column:consume_id;type:varchar(64);comment:消费订单ID;primaryKey;not null;" json:"consume_id"`                 // 消费订单ID
	OrderId     string    `gorm:"column:order_id;type:varchar(64);comment:订单ID;not null;" json:"order_id"`                                  // 订单ID
	OrderTime   int64     `gorm:"column:order_time;type:bigint(20);comment:业务请求时间，重试必传;not null;default:0;" json:"order_time"`              // 业务请求时间，重试必传
	AppId       string    `gorm:"column:app_id;type:varchar(64);comment:应用ID;not null;" json:"app_id"`                                      // 应用ID
	AccessId    string    `gorm:"column:access_id;type:varchar(64);comment:应用接入ID;not null;" json:"access_id"`                              // 应用接入ID
	OpenId      string    `gorm:"column:open_id;type:varchar(50);comment:中台用户标识;not null;" json:"open_id"`                                  // 中台用户标识
	PlatUid     uint64    `gorm:"column:plat_uid;type:bigint(20) UNSIGNED;comment:平台用户标识;not null;default:0;" json:"plat_uid"`              // 平台用户标识
	ConsumeType string    `gorm:"column:consume_type;type:varchar(64);comment:消费类型（从goods信息提取）;not null;" json:"consume_type"`              // 消费类型（从goods信息提取）
	AssetId     int64     `gorm:"column:asset_id;type:bigint(20);comment:资产ID;not null;default:0;" json:"asset_id"`                         // 资产ID
	AssetNum    int64     `gorm:"column:asset_num;type:bigint(20);comment:扣减数量;not null;default:0;" json:"asset_num"`                       // 扣减数量
	Status      int32     `gorm:"column:status;type:int(4);comment:订单状态;not null;default:0;" json:"status"`                                 // 订单状态
	MapExt      string    `gorm:"column:map_ext;type:varchar(4096);comment:ext信息扩展字段;" json:"map_ext"`                                      // ext信息扩展字段
	DeviceId    string    `gorm:"column:device_id;type:varchar(100);comment:设备号;not null;" json:"device_id"`                                // 设备号
	ClientIp    string    `gorm:"column:client_ip;type:varchar(50);comment:客户端IP;not null;" json:"client_ip"`                               // 客户端IP
	CreateTime  time.Time `gorm:"column:create_time;type:datetime;comment:系统下单时间;not null;default:2025-01-01 00:00:00;" json:"create_time"` // 系统下单时间
	UpdateTime  time.Time `gorm:"column:update_time;type:datetime;comment:更新时间;not null;default:2025-01-01 00:00:00;" json:"update_time"`   // 更新时间
}

// 发奖订单表
type Present struct {
	PresentId    string    `gorm:"column:present_id;type:varchar(64);comment:发奖订单ID;primaryKey;not null;" json:"present_id"`                 // 发奖订单ID
	OrderId      string    `gorm:"column:order_id;type:varchar(64);comment:主订单ID;not null;" json:"order_id"`                                 // 主订单ID
	BizTimestamp int64     `gorm:"column:biz_timestamp;type:bigint(20);comment:业务请求时间;not null;default:0;" json:"biz_timestamp"`             // 业务请求时间
	AppId        string    `gorm:"column:app_id;type:varchar(50);comment:应用ID;not null;" json:"app_id"`                                      // 应用ID
	AccessId     string    `gorm:"column:access_id;type:varchar(64);comment:应用接入ID;not null;" json:"access_id"`                              // 应用接入ID
	OpenId       string    `gorm:"column:open_id;type:varchar(64);comment:中台用户标识;not null;" json:"open_id"`                                  // 中台用户标识
	PlatUid      uint64    `gorm:"column:plat_uid;type:bigint(20) UNSIGNED;comment:平台用户标识;not null;default:0;" json:"plat_uid"`              // 平台用户标识
	PrizeType    string    `gorm:"column:prize_type;type:varchar(20);comment:发奖类型（从礼物配置获取）;not null;" json:"prize_type"`                     // 发奖类型（从礼物配置获取）
	PrizeId      int64     `gorm:"column:prize_id;type:bigint(20);comment:礼物ID;not null;default:0;" json:"prize_id"`                         // 礼物ID
	PrizeName    string    `gorm:"column:prize_name;type:varchar(64);comment:礼物名（冗余字段）;not null;" json:"prize_name"`                         // 礼物名（冗余字段）
	PrizeNum     int64     `gorm:"column:prize_num;type:bigint(20);comment:礼物发放数量;not null;default:0;" json:"prize_num"`                     // 礼物发放数量
	PrizePic     string    `gorm:"column:prize_pic;type:varchar(200);comment:奖品图片;not null;" json:"prize_pic"`                               // 奖品图片
	ExpireTime   int64     `gorm:"column:expire_time;type:bigint(20);comment:过期时间（精确到秒）;not null;default:0;" json:"expire_time"`             // 过期时间（精确到秒）
	Status       int32     `gorm:"column:status;type:int(11);comment:订单状态;not null;default:0;" json:"status"`                                // 订单状态
	MapExt       string    `gorm:"column:map_ext;type:varchar(128);comment:业务扩展属性;not null;" json:"map_ext"`                                 // 业务扩展属性
	ClientIp     string    `gorm:"column:client_ip;type:varchar(50);comment:客户端IP;not null;" json:"client_ip"`                               // 客户端IP
	DeviceId     string    `gorm:"column:device_id;type:varchar(100);comment:设备号;not null;" json:"device_id"`                                // 设备号
	CreateTime   time.Time `gorm:"column:create_time;type:datetime;comment:系统下单时间;not null;default:2025-01-01 00:00:00;" json:"create_time"` // 系统下单时间
	UpdateTime   time.Time `gorm:"column:update_time;type:datetime;comment:更新时间;not null;default:2025-01-01 00:00:00;" json:"update_time"`   // 更新时间
}

type RefundOrder struct {
	RefundId     string    `gorm:"column:refund_id;type:varchar(64);comment:退款单号;primaryKey;not null;" json:"refund_id"`                   // 退款单号
	BizTimestamp int64     `gorm:"column:biz_timestamp;type:bigint(20);comment:业务时间;not null;default:0;" json:"biz_timestamp"`             // 业务时间
	AppId        string    `gorm:"column:app_id;type:varchar(64);comment:appId(兼容全民);not null;" json:"app_id"`                             // appId(兼容全民)
	AccessId     string    `gorm:"column:access_id;type:varchar(64);comment:应用接入ID;not null;" json:"access_id"`                            // 应用接入ID
	OrderId      string    `gorm:"column:order_id;type:varchar(64);comment:主订单id;not null;" json:"order_id"`                               // 主订单id
	ConsumeId    string    `gorm:"column:consume_id;type:varchar(64);comment:扣费订单id;not null;" json:"consume_id"`                          // 扣费订单id
	PlatUid      uint64    `gorm:"column:plat_uid;type:bigint(20) UNSIGNED;comment:平台用户标识;not null;default:0;" json:"plat_uid"`            // 平台用户标识
	OpenId       string    `gorm:"column:open_id;type:varchar(64);comment:中台用户标识;not null;" json:"open_id"`                                // 中台用户标识
	ConsumeType  string    `gorm:"column:consume_type;type:varchar(64);comment:扣费类型;not null;" json:"consume_type"`                        // 扣费类型
	AssetId      int64     `gorm:"column:asset_id;type:bigint(20);comment:资产id;not null;default:0;" json:"asset_id"`                       // 资产id
	AssetNum     int64     `gorm:"column:asset_num;type:bigint(20);comment:资产数量;not null;default:0;" json:"asset_num"`                     // 资产数量
	Status       int32     `gorm:"column:status;type:int(11);comment:订单状态;not null;default:0;" json:"status"`                              // 订单状态
	MapExt       string    `gorm:"column:map_ext;type:varchar(2048);comment:业务扩展参数;not null;" json:"map_ext"`                              // 业务扩展参数
	ClientIp     string    `gorm:"column:client_ip;type:varchar(50);comment:客户端IP;not null;" json:"client_ip"`                             // 客户端IP
	DeviceId     string    `gorm:"column:device_id;type:varchar(100);comment:设备号;not null;" json:"device_id"`                              // 设备号
	CreateTime   time.Time `gorm:"column:create_time;type:datetime;comment:创建时间;not null;default:2025-01-01 00:00:00;" json:"create_time"` // 创建时间
	UpdateTime   time.Time `gorm:"column:update_time;type:datetime;comment:更新时间;not null;default:2025-01-01 00:00:00;" json:"update_time"` // 更新时间
}

type LotteryRecord struct {
	DrawId        string    `gorm:"column:draw_id;type:varchar(64);comment:抽奖ID;primaryKey;not null;" json:"draw_id"`                       // 抽奖ID
	DrawTime      uint64    `gorm:"column:draw_time;type:bigint(20) UNSIGNED;comment:抽奖时间;not null;default:0;" json:"draw_time"`            // 抽奖时间
	OpenId        string    `gorm:"column:open_id;type:varchar(100);comment:中台用户标识;not null;" json:"open_id"`                               // 中台用户标识
	PlatUid       uint64    `gorm:"column:plat_uid;type:bigint(20) UNSIGNED;comment:平台用户标识;not null;default:0;" json:"plat_uid"`            // 平台用户标识
	LotteryId     int32     `gorm:"column:lottery_id;type:bigint(20);comment:抽奖配置ID;not null;default:0;" json:"lottery_id"`                 // 抽奖配置ID
	LotteryPoolId string    `gorm:"column:lottery_pool_id;type:varchar(100);comment:抽奖奖池ID;not null;" json:"lottery_pool_id"`               // 抽奖奖池ID
	DrawNum       int64     `gorm:"column:draw_num;type:bigint(20);comment:抽奖数量;not null;default:0;" json:"draw_num"`                       // 抽奖数量
	DrawResult    string    `gorm:"column:draw_result;type:text;comment:抽奖结果;not null;" json:"draw_result"`                                 // 抽奖结果
	CreateTime    time.Time `gorm:"column:create_time;type:datetime;comment:创建时间;not null;default:2025-01-01 00:00:00;" json:"create_time"` // 创建时间
	UpdateTime    time.Time `gorm:"column:update_time;type:datetime;comment:更新时间;not null;default:2025-01-01 00:00:00;" json:"update_time"` // 更新时间
}
