package model

import "errors"

type Pagination struct {
	Page     int32 `json:"page"`
	PageSize int32 `json:"page_size"`
	Offset   int32 `json:"offset"`
	Limit    int32 `json:"limit"`
}

func NewPagination(page, pageSize int32) (*Pagination, error) {
	if page <= 0 || pageSize <= 0 || pageSize > 500 {
		return nil, errors.New("分页参数错误")
	}
	p := &Pagination{
		Page:     page,
		PageSize: pageSize,
		Offset:   (page - 1) * pageSize,
		Limit:    pageSize,
	}
	return p, nil
}

func (p *Pagination) CalculateOffsetLimit() {
	page := p.Page
	if page <= 0 {
		page = 1
	}
	pageSize := p.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}
	p.Offset = (page - 1) * pageSize
	p.Limit = pageSize
	p.Page = page
	p.PageSize = pageSize
}
