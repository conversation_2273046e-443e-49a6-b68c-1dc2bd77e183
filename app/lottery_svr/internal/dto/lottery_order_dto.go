package dto

import (
	"cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/model"
	"time"
)

type LotteryOrderReqDTO struct {
	AppID                string
	AccessId             string
	OrderID              string
	OrderType            string
	PlatUid              string
	OpenId               string
	BizTime              int64
	MapExt               map[string]string
	LotteryInfoReqDTO    *LotteryInfoReqDTO      // 抽奖参数
	ConsumeReqDTO        *LotteryConsumeReqDTO   // 扣资产参数
	LotteryPresentReqDTO []*LotteryPresentReqDTO // 指定发资产参数
	LotteryActivity      *model.LotteryActivity  // 抽奖配置
}

type LotteryConsumeReqDTO struct {
	ConsumeID   string
	UserID      string
	AssetID     int64
	AssetNum    int64
	ConsumeType string
}

type LotteryConfigDTO struct {
}

type LotteryTicketConsumeDTO struct {
	AppID       string
	AccessId    string
	ConsumeType string
	AssetID     int32
	AssetNum    int32
	AssetName   string
}

type LotteryInfoReqDTO struct {
	AppID         string
	AccessId      string
	PlatID        string
	OrderID       string
	UserID        string
	LotteryId     int32
	LotteryPoolId string
	LotteryNum    int64
	BizTime       int64
}

type LotteryPresentReqDTO struct {
	UserID     string
	PrizeID    int64
	PrizeNum   int64
	PrizeName  string
	PrizePic   string
	PrizeType  string
	ExpireTime int64
	BizTime    int64
	MapExt     string
}

type LotteryOrderInfoDTO struct {
	OrderID        string
	UserID         string
	Status         int32
	BizTime        int64
	CreateTime     time.Time
	ConsumeRespDTO []LotteryConsumeRespDTO
	PresentRespDTO []LotteryPresentRespDTO
	MapExt         string
}

type LotteryConsumeRespDTO struct {
	ConsumeID string
	UserID    string
	AssetID   int32
	AssetNum  int32
	Status    int
	MapExt    string
}

type LotteryPresentRespDTO struct {
	PresentId  string
	UserId     string
	PrizeId    int32
	PrizeNum   int64
	PrizeName  string
	PrizePic   string
	ExpireTime int64
	Status     int32
	MapExt     string
}

type OrderDTO struct {
	OrderID      string
	PlatUid      string
	OrderType    int32
	Status       int32
	BizTimestamp int64
	MapExt       string
	CreateTime   time.Time
	UpdateTime   time.Time
}

type PresentDTO struct {
	PresentID  string
	UserID     string
	PrizeID    int32
	PrizeNum   int32
	PrizeName  string
	ExpireTime int64
}
