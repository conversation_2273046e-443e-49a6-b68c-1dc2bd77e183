package dto

import (
	"fmt"
)

const (
	CodeSuccess = 0

	MsgSuccess = "操作成功"
)

type Result[T any] struct {
	Code int64  `json:"code"`
	Msg  string `json:"msg"`
	Data T      `json:"data"`
	Err  error  `json:"-"` // Go 风格错误处理
}

// 成功构造器
func Success[T any](data T) *Result[T] {
	return &Result[T]{
		Code: CodeSuccess,
		Msg:  MsgSuccess,
		Data: data,
	}
}

// 自定义消息（链式调用）
func (r *Result[T]) WithMsg(format string, args ...interface{}) *Result[T] {
	r.Msg = fmt.Sprintf(format, args...)
	return r
}

// 自定义数据（链式调用）
func (r *Result[T]) WithData(data T) *Result[T] {
	r.Data = data
	return r
}

// 判断是否成功
func (r *Result[T]) IsSuccess() bool {
	return r.Code == CodeSuccess
}

// 获取错误信息
func (r *Result[T]) GetError() string {
	if r.Err != nil {
		return r.Err.Error()
	}
	return ""
}

// 失败构造器（新增方法）
func Fail[T any](code int64, msg string) *Result[T] {
	return &Result[T]{
		Code: code,
		Msg:  msg,
	}
}

func (r *Result[T]) WithError(err error) *Result[T] {
	r.Err = err
	return r
}
