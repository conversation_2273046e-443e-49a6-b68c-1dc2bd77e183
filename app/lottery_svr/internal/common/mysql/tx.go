package kgmysql

import (
	"context"
	"database/sql"
	"time"
)

type Tx struct {
	tx  *sql.Tx
	db  *DB
	ins *Instance

	serviceName ServiceName
}

func (tx *Tx) Commit() (err error) {
	tx.reportWrap("tx_commit", func() (error, bool) {
		err = tx.tx.Commit()
		return err, true
	})
	return
}

func (tx *Tx) Rollback() (err error) {
	tx.reportWrap("tx_rollback", func() (error, bool) {
		err = tx.tx.Rollback()
		return err, err != sql.ErrTxDone // 因为代码里都是 defer rollback 这种无效的就不上报了
	})
	return
}

func (tx *Tx) ExecContext(ctx context.Context, query string, args ...interface{}) (res sql.Result, err error) {
	tx.reportWrap("tx_exec", func() (error, bool) {
		res, err = tx.tx.ExecContext(ctx, query, args...)
		return err, true
	})
	return
}

func (tx *Tx) ExecContextMustAffected(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	r, err := tx.ExecContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}

	n, err := r.RowsAffected()
	if err != nil {
		return nil, err
	}

	if n == 0 {
		return r, ErrExecMustAffected
	}
	return r, nil
}

func (tx *Tx) QueryContext(ctx context.Context, query string, args ...interface{}) (rows *sql.Rows, err error) {
	tx.reportWrap("tx_query", func() (error, bool) {
		rows, err = tx.tx.QueryContext(ctx, query, args...)
		return err, true
	})
	return
}

func (tx *Tx) reportWrap(operation string, f func() (error, bool)) {
	begin := time.Now()
	if err, ok := f(); ok {
		serviceName := tx.serviceName
		serviceName.Operation = operation
		tx.db.report(serviceName.String(), err, tx.ins, time.Now().Sub(begin))
	}
}
