package kgmysql

import (
	"context"
	"errors"
	"net"

	driver "github.com/go-sql-driver/mysql"
	"google.golang.org/grpc/codes"
)

var ErrExecMustAffected = errors.New("exec must affected")

const (
	TableAlreadyExists = 1050
	DuplicateEntry     = 1062
	TableDoesNotExist  = 1146
)

func IsMysqlError(err error, number uint16) bool {
	me, ok := err.(*driver.MySQLError)
	return ok && me.Number == number
}

func error2Code(err error) int {
	if err == nil {
		return 0
	} else if err == context.Canceled {
		return int(codes.Canceled)
	} else if err == context.DeadlineExceeded {
		return int(codes.DeadlineExceeded)
	} else if me, ok := err.(*driver.MySQLError); ok {
		return int(me.Number)
	} else if ne, ok := err.(net.Error); ok && ne.Timeout() {
		return int(codes.Unavailable)
	}
	return int(codes.Unknown)
}
