package kgmysql

import (
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/dcreport"
	"strings"
	"time"
)

type Config struct {
	Mod int32
	Cmd int32

	IP   string
	Port int

	ServerName string

	Username string
	Password string
	Database string

	MaxIdleConns    int
	ConnMaxIdleTime time.Duration

	DialTimeout       time.Duration
	InterpolateParams bool
	LocalTime         bool
}

type Option struct {
	reporter *dcreport.Reporter
	ping     bool
}

type OptionFunc func(o *Option)

func WithReporter(reporter *dcreport.Reporter) OptionFunc {
	return func(o *Option) {
		o.reporter = reporter
	}
}

func WithPing() OptionFunc {
	return func(o *Option) {
		o.ping = true
	}
}

func optionsFromConfig(c *Config) string {
	var options []string
	if c.DialTimeout > 0 {
		options = append(options, "timeout="+c.DialTimeout.String())
	}
	if c.InterpolateParams {
		options = append(options, "interpolateParams=true")
	}

	if c.LocalTime {
		options = append(options, "parseTime=true")
		options = append(options, "loc=Local")
	}

	return strings.Join(options, "&")
}
