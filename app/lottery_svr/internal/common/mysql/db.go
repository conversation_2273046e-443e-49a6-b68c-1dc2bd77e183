package kgmysql

import (
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/dcreport"
	"context"
	"database/sql"
	"fmt"
	"net"
	"strings"
	"sync"

	"sync/atomic"
	"syscall"
	"time"

	"cnb.tmeoa.com/going/l5"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
)

type Instance struct {
	svr *l5.Server
	db  *sql.DB
}

func (ins *Instance) updateStat(err error, cost time.Duration) {
	if ins.svr == nil {
		return
	}
	if err != nil {
		if ne, ok := err.(net.Error); ok && ne.Timeout() { // 网络错误上报 l5
			ins.svr.StatUpdate(-1, uint64(cost.Milliseconds()))
			return
		}

		switch ne := err.(type) {
		case *net.OpError:
			if ne.Op == "dial" { // dial 有问题的都踢了先吧
				ins.svr.StatUpdate(-1, uint64(cost.Milliseconds()))
				return
			}
		case syscall.Errno:
			if ne == syscall.ECONNREFUSED {
				ins.svr.StatUpdate(-1, uint64(cost.Milliseconds()))
				return
			}
		}
	}
	ins.svr.StatUpdate(0, uint64(cost.Milliseconds()))
}

// 直接用 sql.Register 注册 driver.Driver 的话
// 所有地址会共享连接池 担心负载不均衡 所以还是 wrap 吧
// 先写几个基本的 后续有需要再补充

type DB struct {
	reporter *dcreport.Reporter

	instances atomic.Value // 失效连接先不管吧
	mu        sync.Mutex

	c Config

	dsnOptions string
}

func (db *DB) report(serviceName string, err error, ins *Instance, cost time.Duration) {
	ins.updateStat(err, cost)

	if db.reporter == nil {
		return
	} // TODO: 分离接口
	db.reporter.Report(serviceName, "", error2Code(err), ins.svr.Ip(), ins.svr.Port(), cost)
}

func (db *DB) Query(query string, args ...interface{}) (*sql.Rows, error) {
	return db.QueryContext(context.Background(), query, args...)
}

func (db *DB) QueryRowContext(ctx context.Context, query string, args ...interface{}) *sql.Row {
	ins, err := db.getInstance()
	if err != nil {
		// 这里可能是有问题的，这个结构体的注释要求 One of these two will be non-nil
		// 但是参考gorm@v1.23.4/prepare_stmt.go#(db *PreparedStmtDB) QueryRowContext 方法中的返回也有类似的做法
		// 先这样吧，影响不大
		return &sql.Row{}
	}

	begin := time.Now()
	rows := ins.db.QueryRowContext(ctx, query, args...)
	db.report(db.serviceNameFromContext(ctx, "query row context").String(), err, ins, time.Now().Sub(begin))
	return rows
}

func (db *DB) QueryContext(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	ins, err := db.getInstance()
	if err != nil {
		return nil, err
	}

	begin := time.Now()
	rows, err := ins.db.QueryContext(ctx, query, args...)
	db.report(db.serviceNameFromContext(ctx, "query").String(), err, ins, time.Since(begin))
	return rows, err
}

func (db *DB) Exec(query string, args ...interface{}) (sql.Result, error) {
	return db.ExecContext(context.Background(), query, args...)
}

func (db *DB) ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	ins, err := db.getInstance()
	if err != nil {
		return nil, err
	}

	begin := time.Now()
	res, err := ins.db.ExecContext(ctx, query, args...)
	db.report(db.serviceNameFromContext(ctx, "exec").String(), err, ins, time.Now().Sub(begin))
	return res, err
}

func (db *DB) PrepareContext(ctx context.Context, query string) (*sql.Stmt, error) {
	ins, err := db.getInstance()
	if err != nil {
		return nil, err
	}

	begin := time.Now()
	res, err := ins.db.PrepareContext(ctx, query)
	db.report(db.serviceNameFromContext(ctx, "prep").String(), err, ins, time.Now().Sub(begin))
	return res, err
}

func (db *DB) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	ins, err := db.getInstance()
	if err != nil {
		return nil, err
	}

	begin := time.Now()
	tx, err := ins.db.BeginTx(ctx, opts)
	db.report(db.serviceNameFromContext(ctx, "tx_begin").String(), err, ins, time.Now().Sub(begin))
	if err != nil {
		return nil, err
	}
	return &Tx{tx: tx, db: db, ins: ins, serviceName: db.serviceNameFromContext(ctx, "")}, nil
}

func (db *DB) RunTx(ctx context.Context, f func(tx *Tx) error) error {
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return err
	}
	defer tx.Rollback()

	if err := f(tx); err != nil {
		return err
	}
	return tx.Commit()
}

func (db *DB) getInstance() (*Instance, error) {
	var svr *l5.Server
	var err error
	ip, port := db.c.IP, db.c.Port
	if db.c.Mod != 0 && db.c.Cmd != 0 {
		svr, err = l5.ApiGetRoute(db.c.Mod, db.c.Cmd)
		if err != nil {
			return nil, err
		}
		ip, port = svr.Ip(), svr.Port()
	}
	addr := fmt.Sprintf("%s:%d", ip, port)

	instances := db.instances.Load().(map[string]*sql.DB)
	instance, ok := instances[addr]
	if ok {
		return &Instance{svr: svr, db: instance}, nil
	}

	db.mu.Lock() // 这个锁可以更细粒度 但好像没啥必要 反正没几个 ip
	defer db.mu.Unlock()

	instances = db.instances.Load().(map[string]*sql.DB)
	instance, ok = instances[addr]
	if ok {
		return &Instance{svr: svr, db: instance}, nil
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s)/%s", db.c.Username, db.c.Password, addr, db.c.Database)
	if db.dsnOptions != "" {
		dsn += "?" + db.dsnOptions
	}
	log.Info("connect db", "dsn", dsn)

	instance, err = sql.Open("mysql", dsn)
	if err != nil {
		return nil, err
	}
	instance.SetMaxIdleConns(db.c.MaxIdleConns)
	instance.SetConnMaxIdleTime(db.c.ConnMaxIdleTime)

	newInstances := make(map[string]*sql.DB, len(instances)+1)
	for key, value := range instances {
		newInstances[key] = value
	}
	newInstances[addr] = instance
	db.instances.Store(newInstances)

	return &Instance{svr: svr, db: instance}, nil
}

func (db *DB) serviceNameFromContext(ctx context.Context, operation string) ServiceName {
	interfaceName := "default"
	v := ctx.Value(ctxInterfaceName{})
	if s, ok := v.(string); ok && s != "" {
		interfaceName = s
	}
	return ServiceName{
		ServerName:    db.c.ServerName,
		InterfaceName: interfaceName,
		Operation:     operation,
	}
}

func Open(c *Config, opts ...OptionFunc) (*DB, error) {
	var o Option
	for _, opt := range opts {
		opt(&o)
	}

	db := &DB{c: *c, reporter: o.reporter, dsnOptions: optionsFromConfig(c)}
	db.instances.Store(make(map[string]*sql.DB))

	//ping下
	if o.ping {
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		defer cancel()
		_, err := db.QueryContext(ctx, "SELECT 1")
		if err != nil {
			return nil, err
		}
	}

	return db, nil
}

type ctxInterfaceName struct{}

func WithInterface(ctx context.Context, interfaceName string) context.Context {
	return context.WithValue(ctx, ctxInterfaceName{}, interfaceName)
}

type ServiceName struct {
	ServerName    string
	InterfaceName string
	Operation     string
}

func (sn ServiceName) String() string {
	return strings.Join([]string{"mysql", sn.ServerName, sn.InterfaceName, sn.Operation}, ".")
}

// GetDefaultSQLDB 提取底层的 *sql.DB 对象
func (db *DB) GetDefaultSQLDB() *sql.DB {
	instance := db.instances.Load() // 假设默认实例键为空字符串
	instances := instance.(map[string]*sql.DB)

	// 2. 获取第一个实例（或按需选择）
	var sqlDB *sql.DB
	for _, idb := range instances {
		sqlDB = idb
		break // 仅取第一个实例（需根据实际逻辑调整）
	}
	if sqlDB == nil {
		panic("未找到数据库实例")
	}
	return sqlDB
}
