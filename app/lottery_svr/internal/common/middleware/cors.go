package middleware

import (
	stringutil "cnb.tmeoa.com/tme_bmp/component/app/lottery_svr/internal/utils"
	"net/http"
)

func CorsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		origin := r.Header.Get("Origin")
		if stringutil.IsNotBlank(origin) {
			w.Header().Set("Access-Control-Allow-Origin", origin)
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
			if r.Method == "OPTIONS" {
				w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusOK)
				return
			}
		}
		c := r.Header.Get("Connection")
		println("读取Connection请求头内容：" + c)
		r.Header.Del("Connection")
		next.ServeHTTP(w, r)
	})
}
