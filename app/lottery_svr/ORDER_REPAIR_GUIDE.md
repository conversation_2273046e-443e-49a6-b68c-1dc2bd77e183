# 订单补单系统使用指南

## 概述

订单补单系统是一个基于`github.com/robfig/cron/v3`的定时任务，用于自动处理lottery_svr项目中因调用失败而导致订单状态未能正确更新的问题。系统会自动查找t_order分月表中过去30分钟到5分钟前status=0的订单，并根据不同的orderType执行相应的补单逻辑。

## 系统架构

### 核心组件

1. **OrderRepairJob** - 主要的定时任务调度器
2. **OrderRepairHelper** - 补单逻辑辅助工具
3. **RepairContext** - 补单上下文信息
4. **RepairResult** - 补单结果封装

### 订单类型支持

系统支持以下4种订单类型的补单：

| 订单类型 | 类型ID | 代码 | 名称 | 补单方法 |
|---------|--------|------|------|----------|
| OrderTypeLottery | 1 | lottery | 纯抽奖 | repairLotteryOrder |
| OrderTypeLotteryDelivery | 2 | lottery_delivery | 抽奖发奖 | repairLotteryDeliveryOrder |
| OrderTypeConsumeLotteryDelivery | 3 | consume_lottery_delivery | 扣抽奖券抽奖发奖 | repairConsumeLotteryDeliveryOrder |
| OrderTypeDeliveryTicket | 4 | delivery_ticket | 发放抽奖券 | repairDeliveryTicketOrder |

## 定时任务配置

- **调度器**: `github.com/robfig/cron/v3`
- **执行频率**: 每5分钟执行一次
- **Cron表达式**: `0 */5 * * * *`
- **时间范围**: 查找30分钟到5分钟前的订单
- **批次大小**: 每次最多处理100条订单
- **分表支持**: 自动处理当前月份和上个月份的订单表

## 补单逻辑

### 通用流程

1. **查找订单**: 按月表查找符合条件的订单
2. **按类型分组**: 将订单按orderType分组处理
3. **检查业务记录**: 查询相关的发奖记录、消费记录等
4. **判断状态**: 根据业务记录判断订单应该的最终状态
5. **更新状态**: 将订单状态更新为成功(1)或失败(2)

### 各类型补单逻辑

#### 1. 纯抽奖订单 (OrderTypeLottery)
```
检查发奖记录 → 有记录=成功，无记录=失败
```

#### 2. 抽奖发奖订单 (OrderTypeLotteryDelivery)
```
检查发奖记录 → 检查发奖状态 → 全部成功=成功，其他=失败
```

#### 3. 扣抽奖券抽奖发奖订单 (OrderTypeConsumeLotteryDelivery)
```
检查消费记录 → 检查发奖记录 → 消费成功且发奖全部成功=成功，其他=失败
```

#### 4. 发放抽奖券订单 (OrderTypeDeliveryTicket)
```
检查发奖记录 → 检查发奖状态 → 全部成功=成功，其他=失败
```

## 自定义补单逻辑

### 实现步骤

每种订单类型都有对应的补单方法，您可以根据具体业务需求进行自定义：

1. **修改补单方法**: 在`order_repair_job.go`中找到对应的方法
2. **使用辅助工具**: 利用`OrderRepairHelper`提供的工具方法
3. **返回处理结果**: 返回true表示成功，false表示失败

### 示例：自定义纯抽奖订单补单逻辑

```go
func (j *OrderRepairJob) repairLotteryOrder(ctx context.Context, order *model.Order, yearMonth string) bool {
    log.Infof("处理纯抽奖订单补单逻辑: %s", order.OrderId)
    
    // 1. 检查发奖记录
    presents, err := j.helper.CheckPresentRecords(ctx, order.OrderId, yearMonth)
    if err != nil {
        log.Errorf("检查发奖记录失败: %v", err)
        return false
    }
    
    // 2. 自定义业务逻辑
    // TODO: 在这里添加您的自定义逻辑
    
    // 3. 判断订单状态
    repairCtx := &RepairContext{
        Order:     order,
        YearMonth: yearMonth,
        Ctx:       ctx,
    }
    
    result := j.helper.DetermineOrderStatus(repairCtx, presents, nil)
    if !result.Success {
        log.Errorf("判断订单状态失败: %s", result.Reason)
        return false
    }
    
    // 4. 更新订单状态
    success, err := j.helper.UpdateOrderStatus(ctx, order.OrderId, yearMonth, result.TargetStatus)
    if err != nil {
        log.Errorf("更新订单状态失败: %v", err)
        return false
    }
    
    if success {
        log.Infof("纯抽奖订单补单成功: %s, 状态: %d, 原因: %s", 
            order.OrderId, result.TargetStatus, result.Reason)
    }
    
    return success
}
```

### 辅助工具方法

`OrderRepairHelper`提供了以下工具方法：

- `CheckPresentRecords()` - 检查发奖记录
- `CheckConsumeRecords()` - 检查消费记录
- `CheckLotteryRecords()` - 检查抽奖记录
- `UpdateOrderStatus()` - 更新订单状态
- `DetermineOrderStatus()` - 判断订单状态

## 监控和日志

### 关键日志

```
订单补单定时任务启动成功 (每5分钟执行一次)
开始执行订单补单任务
开始处理年月202506的订单补单
年月202506找到5个需要补单的订单
开始处理订单类型: 抽奖发奖 (lottery_delivery), 订单数量: 3
处理抽奖发奖订单补单逻辑: order_123456
抽奖发奖订单补单成功: order_123456, 状态: 1, 原因: 所有发奖记录(2条)都成功
订单类型抽奖发奖处理完成, 成功: 2, 失败: 1
年月202506订单补单完成, 成功: 4, 失败: 1
订单补单任务执行完成
```

### 监控指标

- 补单任务执行频率
- 各类型订单补单成功率
- 补单处理时间
- 错误日志统计

## 部署和启动

1. **编译项目**: 确保项目包含了补单相关代码
2. **启动服务**: 补单任务会随服务自动启动
3. **查看日志**: 观察补单任务的执行情况

## 注意事项

1. **时间范围**: 只处理30分钟到5分钟前的订单，避免干扰正在进行的业务
2. **批量限制**: 每次最多处理100条订单，避免对数据库造成压力
3. **状态安全**: 只更新status=0的订单，避免误操作已处理的订单
4. **分表支持**: 自动处理当前月份和上个月份的分月表
5. **错误处理**: 单个订单处理失败不会影响其他订单的处理

## 故障排查

### 常见问题

1. **补单任务未启动**: 检查服务启动日志
2. **订单未被处理**: 检查订单是否在时间范围内
3. **状态更新失败**: 检查数据库连接和权限
4. **补单逻辑错误**: 检查业务记录查询是否正常

### 调试方法

1. **查看任务状态**: 调用`GetStatus()`方法
2. **手动测试**: 可以单独调用补单方法进行测试
3. **日志分析**: 通过日志跟踪补单过程

## 扩展功能

如需添加新的订单类型或修改补单逻辑，请：

1. 在`constant/order_type.go`中添加新的订单类型
2. 在`order_repair_job.go`中添加对应的处理方法
3. 在`order_repair_helper.go`中添加相应的辅助方法
4. 更新本文档说明新增的功能
