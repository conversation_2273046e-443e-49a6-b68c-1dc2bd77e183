-- 测试补单相关DAO方法的SQL脚本
-- 使用方法：在MySQL客户端中执行这些SQL语句来验证DAO方法的正确性

-- 设置当前年月（请根据实际情况修改）
SET @year_month = '202506';

-- 1. 测试SelectPendingOrdersInTimeRange方法对应的SQL
-- 查询30分钟到5分钟前status=0的订单
SET @sql = CONCAT('
SELECT 
    order_id,
    biz_timestamp,
    access_id,
    app_id,
    plat_uid,
    open_id,
    order_type,
    status,
    map_ext,
    client_ip,
    device_id,
    create_time,
    update_time
FROM t_order_', @year_month, '
WHERE status = 0 
AND create_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE_SUB(NOW(), INTERVAL 5 MINUTE) 
ORDER BY create_time ASC 
LIMIT 100');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 测试SelectPresentListByOrderId方法对应的SQL
-- 查询指定订单的发奖记录
-- SET @test_order_id = 'your_order_id_here';
-- SET @sql = CONCAT('
-- SELECT 
--     present_id,
--     order_id,
--     biz_timestamp,
--     app_id,
--     access_id,
--     open_id,
--     plat_uid,
--     prize_type,
--     prize_id,
--     prize_name,
--     prize_num,
--     prize_pic,
--     expire_time,
--     status,
--     map_ext,
--     client_ip,
--     device_id,
--     create_time,
--     update_time
-- FROM t_present_', @year_month, ' 
-- WHERE order_id = ?');
-- PREPARE stmt FROM @sql;
-- EXECUTE stmt USING @test_order_id;
-- DEALLOCATE PREPARE stmt;

-- 3. 测试SelectConsumeListByOrderId方法对应的SQL
-- 查询指定订单的消费记录
-- SET @sql = CONCAT('
-- SELECT 
--     consume_id,
--     order_id,
--     order_time,
--     app_id,
--     access_id,
--     open_id,
--     plat_uid,
--     consume_type,
--     asset_id,
--     asset_num,
--     status,
--     map_ext,
--     device_id,
--     client_ip,
--     create_time,
--     update_time
-- FROM t_consume_', @year_month, ' 
-- WHERE order_id = ?');
-- PREPARE stmt FROM @sql;
-- EXECUTE stmt USING @test_order_id;
-- DEALLOCATE PREPARE stmt;

-- 4. 验证表结构是否正确
-- 检查订单表字段
SET @sql = CONCAT('DESCRIBE t_order_', @year_month);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查发奖表字段
SET @sql = CONCAT('DESCRIBE t_present_', @year_month);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查消费表字段
SET @sql = CONCAT('DESCRIBE t_consume_', @year_month);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 检查索引是否存在
-- 检查订单表索引
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = CONCAT('t_order_', @year_month)
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 检查发奖表索引
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = CONCAT('t_present_', @year_month)
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 检查消费表索引
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = CONCAT('t_consume_', @year_month)
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 6. 测试时间范围查询的性能
-- 检查是否有合适的索引支持时间范围查询
SET @sql = CONCAT('
EXPLAIN SELECT 
    order_id, order_type, status, create_time
FROM t_order_', @year_month, '
WHERE status = 0 
AND create_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE_SUB(NOW(), INTERVAL 5 MINUTE) 
ORDER BY create_time ASC 
LIMIT 100');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 统计各表的数据量
SET @sql = CONCAT('SELECT COUNT(*) as order_count FROM t_order_', @year_month);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('SELECT COUNT(*) as present_count FROM t_present_', @year_month);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('SELECT COUNT(*) as consume_count FROM t_consume_', @year_month);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 使用说明：
-- 1. 修改 @year_month 变量为当前需要测试的年月
-- 2. 如果需要测试特定订单，取消注释相关部分并设置 @test_order_id
-- 3. 检查EXPLAIN结果，确保查询使用了合适的索引
-- 4. 如果发现性能问题，可能需要添加相应的索引

-- 建议的索引（如果不存在的话）：
-- ALTER TABLE t_order_202506 ADD INDEX idx_status_create_time (status, create_time);
-- ALTER TABLE t_present_202506 ADD INDEX idx_order_id (order_id);
-- ALTER TABLE t_consume_202506 ADD INDEX idx_order_id (order_id);
