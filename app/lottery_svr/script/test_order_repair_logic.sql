-- 测试订单补单逻辑的SQL脚本
-- 使用方法：在MySQL客户端中执行这些SQL语句来验证补单逻辑

-- 设置当前年月（请根据实际情况修改）
SET @year_month = '202506';

-- 1. 测试纯抽奖订单（OrderTypeLottery = 1）
-- 写入表：t_order + t_lottery
-- 补单逻辑：有抽奖记录 → 成功，无抽奖记录 → 失败

SELECT 
    '=== 纯抽奖订单补单逻辑测试 ===' as test_type;

-- 查找纯抽奖订单
SET @sql = CONCAT('
SELECT 
    o.order_id,
    o.order_type,
    o.status,
    o.create_time,
    CASE WHEN l.draw_id IS NOT NULL THEN "有抽奖记录" ELSE "无抽奖记录" END as lottery_status,
    CASE 
        WHEN l.draw_id IS NOT NULL THEN "应补成成功"
        ELSE "应补成失败"
    END as repair_suggestion
FROM t_order_', @year_month, ' o
LEFT JOIN t_lottery_', @year_month, ' l ON o.order_id = l.draw_id
WHERE o.order_type = 1 
AND o.status = 0
AND o.create_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE_SUB(NOW(), INTERVAL 5 MINUTE)
LIMIT 10');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 测试抽奖发奖订单（OrderTypeLotteryDelivery = 2）
-- 写入表：t_order + t_lottery + t_present
-- 补单逻辑：有抽奖记录且发奖全部成功 → 成功，其他 → 失败

SELECT 
    '=== 抽奖发奖订单补单逻辑测试 ===' as test_type;

SET @sql = CONCAT('
SELECT 
    o.order_id,
    o.order_type,
    o.status,
    o.create_time,
    CASE WHEN l.draw_id IS NOT NULL THEN "有抽奖记录" ELSE "无抽奖记录" END as lottery_status,
    CASE WHEN p.order_id IS NOT NULL THEN "有发奖记录" ELSE "无发奖记录" END as present_status,
    COALESCE(present_stats.total_count, 0) as present_total,
    COALESCE(present_stats.success_count, 0) as present_success,
    CASE 
        WHEN l.draw_id IS NOT NULL AND present_stats.total_count > 0 AND present_stats.success_count = present_stats.total_count THEN "应补成成功"
        ELSE "应补成失败"
    END as repair_suggestion
FROM t_order_', @year_month, ' o
LEFT JOIN t_lottery_', @year_month, ' l ON o.order_id = l.draw_id
LEFT JOIN t_present_', @year_month, ' p ON o.order_id = p.order_id
LEFT JOIN (
    SELECT 
        order_id,
        COUNT(*) as total_count,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count
    FROM t_present_', @year_month, '
    GROUP BY order_id
) present_stats ON o.order_id = present_stats.order_id
WHERE o.order_type = 2 
AND o.status = 0
AND o.create_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE_SUB(NOW(), INTERVAL 5 MINUTE)
LIMIT 10');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 测试扣抽奖券抽奖发奖订单（OrderTypeConsumeLotteryDelivery = 3）
-- 写入表：t_order + t_lottery + t_present + t_consume
-- 补单逻辑：消费成功且抽奖成功且发奖全部成功 → 成功，其他 → 失败

SELECT 
    '=== 扣抽奖券抽奖发奖订单补单逻辑测试 ===' as test_type;

SET @sql = CONCAT('
SELECT 
    o.order_id,
    o.order_type,
    o.status,
    o.create_time,
    CASE WHEN c.order_id IS NOT NULL THEN "有消费记录" ELSE "无消费记录" END as consume_status,
    COALESCE(consume_stats.success_count, 0) as consume_success,
    CASE WHEN l.draw_id IS NOT NULL THEN "有抽奖记录" ELSE "无抽奖记录" END as lottery_status,
    CASE WHEN p.order_id IS NOT NULL THEN "有发奖记录" ELSE "无发奖记录" END as present_status,
    COALESCE(present_stats.total_count, 0) as present_total,
    COALESCE(present_stats.success_count, 0) as present_success,
    CASE 
        WHEN consume_stats.success_count > 0 AND l.draw_id IS NOT NULL AND present_stats.total_count > 0 AND present_stats.success_count = present_stats.total_count THEN "应补成成功"
        ELSE "应补成失败"
    END as repair_suggestion
FROM t_order_', @year_month, ' o
LEFT JOIN t_consume_', @year_month, ' c ON o.order_id = c.order_id
LEFT JOIN (
    SELECT 
        order_id,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count
    FROM t_consume_', @year_month, '
    GROUP BY order_id
) consume_stats ON o.order_id = consume_stats.order_id
LEFT JOIN t_lottery_', @year_month, ' l ON o.order_id = l.draw_id
LEFT JOIN t_present_', @year_month, ' p ON o.order_id = p.order_id
LEFT JOIN (
    SELECT 
        order_id,
        COUNT(*) as total_count,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count
    FROM t_present_', @year_month, '
    GROUP BY order_id
) present_stats ON o.order_id = present_stats.order_id
WHERE o.order_type = 3 
AND o.status = 0
AND o.create_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE_SUB(NOW(), INTERVAL 5 MINUTE)
LIMIT 10');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 测试发放抽奖券订单（OrderTypeDeliveryTicket = 4）
-- 写入表：t_order + t_present
-- 补单逻辑：发奖全部成功 → 成功，其他 → 失败

SELECT 
    '=== 发放抽奖券订单补单逻辑测试 ===' as test_type;

SET @sql = CONCAT('
SELECT 
    o.order_id,
    o.order_type,
    o.status,
    o.create_time,
    CASE WHEN p.order_id IS NOT NULL THEN "有发奖记录" ELSE "无发奖记录" END as present_status,
    COALESCE(present_stats.total_count, 0) as present_total,
    COALESCE(present_stats.success_count, 0) as present_success,
    CASE 
        WHEN present_stats.total_count > 0 AND present_stats.success_count = present_stats.total_count THEN "应补成成功"
        ELSE "应补成失败"
    END as repair_suggestion
FROM t_order_', @year_month, ' o
LEFT JOIN t_present_', @year_month, ' p ON o.order_id = p.order_id
LEFT JOIN (
    SELECT 
        order_id,
        COUNT(*) as total_count,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count
    FROM t_present_', @year_month, '
    GROUP BY order_id
) present_stats ON o.order_id = present_stats.order_id
WHERE o.order_type = 4 
AND o.status = 0
AND o.create_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE_SUB(NOW(), INTERVAL 5 MINUTE)
LIMIT 10');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 统计各订单类型的待补单数量
SELECT 
    '=== 各订单类型待补单统计 ===' as summary_type;

SET @sql = CONCAT('
SELECT 
    order_type,
    CASE 
        WHEN order_type = 1 THEN "纯抽奖"
        WHEN order_type = 2 THEN "抽奖发奖"
        WHEN order_type = 3 THEN "扣抽奖券抽奖发奖"
        WHEN order_type = 4 THEN "发放抽奖券"
        ELSE "未知类型"
    END as order_type_name,
    COUNT(*) as pending_count
FROM t_order_', @year_month, '
WHERE status = 0 
AND create_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE_SUB(NOW(), INTERVAL 5 MINUTE)
GROUP BY order_type
ORDER BY order_type');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 使用说明：
-- 1. 修改 @year_month 变量为当前需要测试的年月
-- 2. 这个脚本会模拟补单逻辑，显示每个订单应该补成什么状态
-- 3. 可以用来验证补单程序的逻辑是否正确
-- 4. 关注 repair_suggestion 列，它显示了根据业务记录判断的补单建议

-- 补单逻辑总结：
-- 1. 纯抽奖(1): t_order + t_lottery → 有抽奖记录就成功
-- 2. 抽奖发奖(2): t_order + t_lottery + t_present → 有抽奖记录且发奖全部成功
-- 3. 扣抽奖券抽奖发奖(3): t_order + t_lottery + t_present + t_consume → 消费成功且抽奖成功且发奖全部成功
-- 4. 发放抽奖券(4): t_order + t_present → 发奖全部成功
