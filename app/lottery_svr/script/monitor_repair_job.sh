#!/bin/bash

# 订单补单功能监控脚本
# 使用方法: ./monitor_repair_job.sh [log_file_path]

LOG_FILE=${1:-"/path/to/lottery_svr/log/slog.log"}
YEAR_MONTH=$(date +%Y%m)

echo "=== 订单补单功能监控报告 ==="
echo "监控时间: $(date)"
echo "日志文件: $LOG_FILE"
echo "当前年月: $YEAR_MONTH"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查日志文件是否存在
if [ ! -f "$LOG_FILE" ]; then
    echo -e "${RED}错误: 日志文件不存在: $LOG_FILE${NC}"
    echo "请指定正确的日志文件路径"
    exit 1
fi

echo -e "${BLUE}=== 1. 补单任务启动状态 ===${NC}"
if grep -q "订单补单定时任务启动成功" "$LOG_FILE"; then
    echo -e "${GREEN}✓ 补单定时任务已启动 (使用cron调度器)${NC}"
    # 检查是否使用了cron调度器
    if grep -q "每5分钟执行一次" "$LOG_FILE"; then
        echo -e "${GREEN}  调度器: github.com/robfig/cron/v3${NC}"
        echo -e "${GREEN}  表达式: 0 */5 * * * * (每5分钟执行)${NC}"
    fi
else
    echo -e "${RED}✗ 补单定时任务未启动或启动失败${NC}"
fi
echo ""

echo -e "${BLUE}=== 2. 最近补单执行情况 ===${NC}"
echo "最近10次补单任务执行:"
grep "开始执行订单补单任务\|订单补单任务执行完成" "$LOG_FILE" | tail -20 | while read line; do
    timestamp=$(echo "$line" | grep -o '[0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\} [0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}')
    if echo "$line" | grep -q "开始执行"; then
        echo -e "${YELLOW}[$timestamp] 开始执行补单任务${NC}"
    else
        echo -e "${GREEN}[$timestamp] 补单任务执行完成${NC}"
    fi
done
echo ""

echo -e "${BLUE}=== 3. 最近补单处理统计 ===${NC}"
echo "最近补单处理结果:"
grep "订单补单完成.*成功.*失败" "$LOG_FILE" | tail -10 | while read line; do
    timestamp=$(echo "$line" | grep -o '[0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\} [0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}')
    year_month=$(echo "$line" | grep -o '年月[0-9]\{6\}' | sed 's/年月//')
    success=$(echo "$line" | grep -o '成功: [0-9]\+' | sed 's/成功: //')
    fail=$(echo "$line" | grep -o '失败: [0-9]\+' | sed 's/失败: //')
    echo -e "${GREEN}[$timestamp] 年月$year_month: 成功$success, 失败$fail${NC}"
done
echo ""

echo -e "${BLUE}=== 4. 补单错误统计 ===${NC}"
error_count=$(grep -c "补单失败\|更新订单状态失败\|查询.*失败.*error" "$LOG_FILE" 2>/dev/null || echo "0")
if [ "$error_count" -gt 0 ]; then
    echo -e "${RED}发现 $error_count 个补单相关错误${NC}"
    echo "最近的错误:"
    grep "补单失败\|更新订单状态失败\|查询.*失败.*error" "$LOG_FILE" | tail -5 | while read line; do
        timestamp=$(echo "$line" | grep -o '[0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\} [0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}')
        echo -e "${RED}[$timestamp] $(echo "$line" | sed 's/.*\] //')${NC}"
    done
else
    echo -e "${GREEN}✓ 没有发现补单相关错误${NC}"
fi
echo ""

echo -e "${BLUE}=== 5. 补单频率检查 ===${NC}"
last_hour_count=$(grep "开始执行订单补单任务" "$LOG_FILE" | grep "$(date -d '1 hour ago' '+%Y-%m-%d %H')\|$(date '+%Y-%m-%d %H')" | wc -l)
echo "最近1小时补单执行次数: $last_hour_count"
echo "调度器类型: Cron调度器 (github.com/robfig/cron/v3)"
echo "默认表达式: 0 */5 * * * * (每5分钟的第0秒执行)"
if [ "$last_hour_count" -ge 10 ] && [ "$last_hour_count" -le 15 ]; then
    echo -e "${GREEN}✓ 补单频率正常 (预期每5分钟执行一次，1小时约12次)${NC}"
elif [ "$last_hour_count" -lt 10 ]; then
    echo -e "${YELLOW}⚠ 补单频率偏低，可能cron调度器有问题${NC}"
else
    echo -e "${YELLOW}⚠ 补单频率偏高${NC}"
fi
echo ""

echo -e "${BLUE}=== 6. 最近处理的订单样例 ===${NC}"
echo "最近补单成功的订单:"
grep "订单补单成功:" "$LOG_FILE" | tail -5 | while read line; do
    timestamp=$(echo "$line" | grep -o '[0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\} [0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}')
    order_id=$(echo "$line" | grep -o 'order_[^,]*\|[0-9]\{10,\}')
    old_status=$(echo "$line" | grep -o '原状态: [0-9]\+' | sed 's/原状态: //')
    new_status=$(echo "$line" | grep -o '新状态: [0-9]\+' | sed 's/新状态: //')
    echo -e "${GREEN}[$timestamp] 订单$order_id: $old_status → $new_status${NC}"
done
echo ""

echo -e "${BLUE}=== 7. 状态判断逻辑统计 ===${NC}"
success_repair_count=$(grep -c "有发奖记录.*补成成功状态" "$LOG_FILE" 2>/dev/null || echo "0")
fail_repair_count=$(grep -c "无发奖记录.*补成失败状态\|有消费记录但无发奖记录.*补成失败状态" "$LOG_FILE" 2>/dev/null || echo "0")
echo "补成成功状态的订单数: $success_repair_count"
echo "补成失败状态的订单数: $fail_repair_count"
echo ""

echo -e "${BLUE}=== 8. 建议的监控命令 ===${NC}"
echo "实时监控补单日志:"
echo "tail -f $LOG_FILE | grep '订单补单'"
echo ""
echo "查看补单统计:"
echo "grep '订单补单完成' $LOG_FILE | tail -10"
echo ""
echo "查看补单错误:"
echo "grep '补单失败\\|更新订单状态失败' $LOG_FILE"
echo ""

echo -e "${BLUE}=== 9. 数据库检查建议 ===${NC}"
echo "检查待补单订单数量:"
echo "SELECT COUNT(*) FROM t_order_$YEAR_MONTH WHERE status = 0 AND create_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE_SUB(NOW(), INTERVAL 5 MINUTE);"
echo ""
echo "查看最近补单的订单:"
echo "SELECT order_id, status, update_time FROM t_order_$YEAR_MONTH WHERE update_time > DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND status IN (1, 2) ORDER BY update_time DESC LIMIT 10;"
echo ""

echo "=== 监控报告完成 ==="
