-- lottery_activity: table
CREATE TABLE `lottery_activity` (
  `lottery_id` bigint(20) NOT NULL COMMENT '抽奖ID',
  `plat_code` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '平台代码',
  `activity_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动名称',
  `activity_desc` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动描述',
  `start_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '开始时间',
  `end_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '结束时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-未开始 1-进行中 2-已结束 3-已关闭',
  `pool_info` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '奖池信息',
  `create_user` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '创建用户',
  `update_user` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '更新用户',
  `boss_app_id` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'boss系统AppId',
  `boss_app_secret` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'boss系统密钥',
  `boss_bid` int(11) NOT NULL DEFAULT '0' COMMENT 'boss系统bid',
  `boss_account_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'Boss账户ID',
  `lottery_admin` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '专属管理员',
  `create_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`lottery_id`),
  KEY `idx_time` (`start_time`,`end_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抽奖活动表';

-- lottery_ticket: table
CREATE TABLE `lottery_ticket` (
  `ticket_id` int(11) NOT NULL DEFAULT '0' COMMENT '抽奖券ID',
  `plat_code` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'kugou' COMMENT '平台代号',
  `lottery_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '抽奖活动ID',
  `ticket_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '券名称',
  `ticket_desc` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '抽奖券备注',
  `total_num` bigint(20) NOT NULL DEFAULT '0' COMMENT '券总量',
  `used_num` bigint(20) NOT NULL DEFAULT '0' COMMENT '消耗数量',
  `expire_secs` bigint(20) NOT NULL DEFAULT '0' COMMENT '有效时间（秒）',
  `single_draw_ticket_num` int(10) NOT NULL DEFAULT '1' COMMENT '单次抽奖消耗数量',
  `create_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`ticket_id`),
  UNIQUE KEY `uniq_activity_id` (`lottery_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抽奖券配置';

-- t_consume_202506: table
CREATE TABLE `t_consume_202506` (
  `consume_id` varchar(64) NOT NULL DEFAULT '' COMMENT '消费订单ID',
  `order_id` varchar(64) NOT NULL DEFAULT '' COMMENT '订单ID',
  `order_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '业务请求时间，重试必传',
  `application_id` varchar(64) NOT NULL DEFAULT '' COMMENT '接入应用ID',
  `app_id` varchar(64) NOT NULL DEFAULT '' COMMENT '应用ID(兼容全民)',
  `plat_id` varchar(20) NOT NULL DEFAULT '' COMMENT '平台ID',
  `open_id` varchar(50) NOT NULL DEFAULT '' COMMENT '中台用户标识',
  `plat_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '平台用户标识',
  `consume_type` varchar(64) NOT NULL DEFAULT '' COMMENT '消费类型（从goods信息提取）',
  `asset_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '资产ID',
  `asset_num` bigint(20) NOT NULL DEFAULT '0' COMMENT '扣减数量',
  `status` int(4) NOT NULL DEFAULT '0' COMMENT '订单状态',
  `map_ext` varchar(4096) DEFAULT '' COMMENT 'ext信息扩展字段',
  `create_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '系统下单时间',
  `update_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`consume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣费订单表';

-- t_lottery_202506: table
CREATE TABLE `t_lottery_202506` (
  `draw_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '抽奖ID',
  `draw_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '抽奖时间',
  `open_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '中台用户标识',
  `plat_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '平台用户标识',
  `lottery_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '抽奖配置ID',
  `lottery_pool_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '抽奖奖池ID',
  `draw_num` bigint(20) NOT NULL DEFAULT '0' COMMENT '抽奖数量',
  `draw_result` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '抽奖结果',
  `create_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`draw_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抽奖订单表';

-- t_order_202505: table
CREATE TABLE `t_order_202505` (
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '主订单',
  `application_id` varchar(64) NOT NULL DEFAULT '0' COMMENT '接入应用ID',
  `app_id` varchar(64) DEFAULT '' COMMENT '应用ID',
  `plat_id` varchar(64) DEFAULT '' COMMENT '平台号',
  `plat_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '平台用户标识',
  `open_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '中台用户标识',
  `order_type` int(11) NOT NULL DEFAULT '0' COMMENT '订单类型',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '订单状态',
  `biz_timestamp` bigint(20) NOT NULL DEFAULT '0' COMMENT '业务下单时间',
  `map_ext` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '业务扩展倀性',
  `create_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '系统下单时间',
  `update_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单主表';

-- t_order_202506: table
CREATE TABLE `t_order_202506` (
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '主订单',
  `application_id` varchar(64) NOT NULL DEFAULT '0' COMMENT '接入应用ID',
  `app_id` varchar(64) DEFAULT '' COMMENT '应用ID',
  `plat_id` varchar(64) DEFAULT '' COMMENT '平台号',
  `plat_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '平台用户标识',
  `open_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '中台用户标识',
  `order_type` int(11) NOT NULL DEFAULT '0' COMMENT '订单类型',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '订单状态',
  `biz_timestamp` bigint(20) NOT NULL DEFAULT '0' COMMENT '业务下单时间',
  `map_ext` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '业务扩展倀性',
  `create_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '系统下单时间',
  `update_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单主表';

-- t_present_202506: table
CREATE TABLE `t_present_202506` (
  `present_id` varchar(64) NOT NULL DEFAULT '' COMMENT '发奖订单ID',
  `order_id` varchar(64) NOT NULL DEFAULT '' COMMENT '主订单ID',
  `biz_timestamp` bigint(20) NOT NULL DEFAULT '0' COMMENT '业务请求时间',
  `app_id` varchar(50) NOT NULL DEFAULT '' COMMENT '应用ID',
  `application_id` varchar(64) NOT NULL DEFAULT '' COMMENT '接入应用ID',
  `plat_id` varchar(64) NOT NULL DEFAULT '' COMMENT '接入平台ID',
  `open_id` varchar(64) NOT NULL DEFAULT '' COMMENT '中台用户标识',
  `plat_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '平台用户标识',
  `prize_type` varchar(20) NOT NULL DEFAULT '' COMMENT '发奖类型（从礼物配置获取）',
  `prize_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '礼物ID',
  `prize_name` varchar(64) NOT NULL DEFAULT '' COMMENT '礼物名（冗余字段）',
  `prize_num` bigint(20) NOT NULL DEFAULT '0' COMMENT '礼物发放数量',
  `prize_pic` varchar(200) NOT NULL DEFAULT '' COMMENT '奖品图片',
  `expire_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '过期时间（精确到秒）',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '订单状态',
  `map_ext` varchar(128) NOT NULL DEFAULT '' COMMENT '业务扩展属性',
  `create_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '系统下单时间',
  `update_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`present_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发奖订单表';

-- t_refund_order: table
CREATE TABLE `t_refund_order` (
  `refund_id` varchar(64) NOT NULL DEFAULT '' COMMENT '退款单号',
  `application_id` varchar(64) DEFAULT '' COMMENT '应用id',
  `app_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'appId(兼容全民)',
  `plat_id` varchar(64) NOT NULL DEFAULT '' COMMENT '平台id',
  `order_id` varchar(64) NOT NULL DEFAULT '' COMMENT '主订单id',
  `consume_id` varchar(64) NOT NULL DEFAULT '' COMMENT '扣费订单id',
  `plat_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '平台用户标识',
  `open_id` varchar(64) NOT NULL DEFAULT '' COMMENT '中台用户标识',
  `consume_type` varchar(64) NOT NULL DEFAULT '' COMMENT '扣费类型',
  `asset_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '资产id',
  `asset_num` bigint(20) NOT NULL DEFAULT '0' COMMENT '资产数量',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `biz_timestamp` bigint(20) NOT NULL DEFAULT '0' COMMENT '业务时间',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '订单状态',
  `map_ext` varchar(2048) NOT NULL DEFAULT '' COMMENT '业务扩展参数',
  `create_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT '2025-01-01 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`refund_id`),
  UNIQUE KEY `uk_consume` (`consume_id`),
  KEY `idx_order` (`order_id`),
  KEY `idx_create` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款订单表';

