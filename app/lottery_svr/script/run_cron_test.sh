#!/bin/bash

# Cron调度器测试脚本
# 使用方法: ./run_cron_test.sh

echo "=== Cron调度器功能测试 ==="
echo "测试时间: $(date)"
echo ""

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go环境，请先安装Go"
    exit 1
fi

# 进入脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "当前目录: $(pwd)"
echo ""

# 初始化Go模块（如果需要）
if [ ! -f "go.mod" ]; then
    echo "初始化Go模块..."
    go mod init cron-test
    go get github.com/robfig/cron/v3
    echo ""
fi

# 运行测试
echo "开始运行Cron调度器测试..."
echo "注意: 测试将运行30秒，期间会看到定时任务的执行输出"
echo ""

go run test_cron_repair.go

echo ""
echo "=== 测试说明 ==="
echo ""
echo "1. 测试展示了如何使用 github.com/robfig/cron/v3 库"
echo "2. 支持秒级精度的cron表达式"
echo "3. 可以同时运行多个定时任务"
echo "4. 支持优雅停止和等待任务完成"
echo ""
echo "=== 在lottery_svr中的应用 ==="
echo ""
echo "补单任务的cron表达式配置:"
echo "- 默认: '0 */5 * * * *' (每5分钟执行)"
echo "- 每分钟: '0 * * * * *'"
echo "- 每10分钟: '0 */10 * * * *'"
echo "- 每小时: '0 0 * * * *'"
echo "- 每天凌晨2点: '0 0 2 * * *'"
echo ""
echo "修改执行频率的方法:"
echo "1. 在 order_repair_job.go 中修改默认的cron表达式"
echo "2. 或者使用 StartWithCustomCron() 方法传入自定义表达式"
echo ""
echo "=== 测试完成 ==="
