-- 订单补单功能检查SQL脚本
-- 使用方法：在MySQL客户端中执行这些SQL语句来检查补单功能的效果

-- 设置当前年月（请根据实际情况修改）
SET @year_month = '202506';

-- 1. 查看当前待补单的订单数量
SELECT 
    CONCAT('t_order_', @year_month) AS table_name,
    COUNT(*) AS pending_orders_count
FROM information_schema.tables t
WHERE t.table_schema = DATABASE() 
AND t.table_name = CONCAT('t_order_', @year_month)
AND EXISTS (
    SELECT 1 FROM information_schema.columns c 
    WHERE c.table_schema = DATABASE() 
    AND c.table_name = t.table_name 
    AND c.column_name = 'status'
);

-- 2. 查看30分钟到5分钟前status=0的订单（补单目标订单）
SET @sql = CONCAT('
SELECT 
    order_id,
    status,
    create_time,
    update_time,
    TIMESTAMPDIFF(MINUTE, create_time, NOW()) as minutes_ago
FROM t_order_', @year_month, '
WHERE status = 0 
AND create_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE_SUB(NOW(), INTERVAL 5 MINUTE)
ORDER BY create_time DESC
LIMIT 10');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 查看最近更新的订单（可能是补单结果）
SET @sql = CONCAT('
SELECT 
    order_id,
    status,
    create_time,
    update_time,
    CASE 
        WHEN status = 0 THEN "初始化"
        WHEN status = 1 THEN "成功"
        WHEN status = 2 THEN "失败"
        ELSE "未知"
    END as status_desc,
    TIMESTAMPDIFF(MINUTE, update_time, NOW()) as updated_minutes_ago
FROM t_order_', @year_month, '
WHERE update_time > DATE_SUB(NOW(), INTERVAL 10 MINUTE)
AND status IN (1, 2)
ORDER BY update_time DESC
LIMIT 10');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 统计各状态订单数量
SET @sql = CONCAT('
SELECT 
    status,
    CASE 
        WHEN status = 0 THEN "初始化"
        WHEN status = 1 THEN "成功"
        WHEN status = 2 THEN "失败"
        ELSE "未知"
    END as status_desc,
    COUNT(*) as count
FROM t_order_', @year_month, '
GROUP BY status
ORDER BY status');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 查看特定订单的详细信息（请替换为实际的order_id）
-- SET @order_id = 'your_order_id_here';
-- 
-- -- 查看订单信息
-- SET @sql = CONCAT('SELECT * FROM t_order_', @year_month, ' WHERE order_id = ?');
-- PREPARE stmt FROM @sql;
-- EXECUTE stmt USING @order_id;
-- DEALLOCATE PREPARE stmt;
-- 
-- -- 查看发奖记录
-- SET @sql = CONCAT('SELECT * FROM t_present_', @year_month, ' WHERE order_id = ?');
-- PREPARE stmt FROM @sql;
-- EXECUTE stmt USING @order_id;
-- DEALLOCATE PREPARE stmt;
-- 
-- -- 查看消费记录
-- SET @sql = CONCAT('SELECT * FROM t_consume_', @year_month, ' WHERE order_id = ?');
-- PREPARE stmt FROM @sql;
-- EXECUTE stmt USING @order_id;
-- DEALLOCATE PREPARE stmt;

-- 6. 查看订单表的索引情况（确保查询性能）
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE CONCAT('t_order_', @year_month)
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 7. 检查最近5分钟内的订单（不应该被补单处理）
SET @sql = CONCAT('
SELECT 
    COUNT(*) as recent_orders_count,
    "最近5分钟内的订单（不会被补单处理）" as description
FROM t_order_', @year_month, '
WHERE status = 0 
AND create_time > DATE_SUB(NOW(), INTERVAL 5 MINUTE)');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 8. 检查30分钟前的订单（不应该被补单处理）
SET @sql = CONCAT('
SELECT 
    COUNT(*) as old_orders_count,
    "30分钟前的订单（不会被补单处理）" as description
FROM t_order_', @year_month, '
WHERE status = 0 
AND create_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE)');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 使用说明：
-- 1. 修改 @year_month 变量为当前需要检查的年月
-- 2. 如果需要检查特定订单，取消注释第5部分并设置 @order_id
-- 3. 定期执行此脚本来监控补单功能的效果
-- 4. 关注待补单订单数量的变化趋势
