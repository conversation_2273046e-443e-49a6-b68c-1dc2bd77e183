package main

import (
	"fmt"
	"log"
	"time"

	"github.com/robfig/cron/v3"
)

// 测试cron调度器功能的简单示例
func main() {
	fmt.Println("=== Cron调度器测试 ===")
	
	// 创建cron调度器，支持秒级精度
	c := cron.New(cron.WithSeconds())
	
	// 添加测试任务1：每5秒执行一次
	_, err := c.AddFunc("*/5 * * * * *", func() {
		fmt.Printf("[%s] 每5秒执行的任务\n", time.Now().Format("2006-01-02 15:04:05"))
	})
	if err != nil {
		log.Fatalf("添加任务1失败: %v", err)
	}
	
	// 添加测试任务2：每10秒执行一次
	_, err = c.AddFunc("*/10 * * * * *", func() {
		fmt.Printf("[%s] 每10秒执行的任务\n", time.Now().Format("2006-01-02 15:04:05"))
	})
	if err != nil {
		log.Fatalf("添加任务2失败: %v", err)
	}
	
	// 添加测试任务3：每分钟执行一次
	_, err = c.AddFunc("0 * * * * *", func() {
		fmt.Printf("[%s] 每分钟执行的任务\n", time.Now().Format("2006-01-02 15:04:05"))
	})
	if err != nil {
		log.Fatalf("添加任务3失败: %v", err)
	}
	
	// 启动调度器
	c.Start()
	fmt.Println("Cron调度器已启动")
	
	// 显示下次执行时间
	fmt.Println("\n下次执行时间:")
	for i, entry := range c.Entries() {
		fmt.Printf("任务%d: %s\n", i+1, entry.Next.Format("2006-01-02 15:04:05"))
	}
	
	// 运行30秒后停止
	fmt.Println("\n运行30秒后自动停止...")
	time.Sleep(30 * time.Second)
	
	// 停止调度器
	ctx := c.Stop()
	fmt.Println("\n正在停止调度器...")
	
	// 等待所有任务完成
	<-ctx.Done()
	fmt.Println("调度器已停止")
	
	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("在实际的补单任务中:")
	fmt.Println("- 默认使用 '0 */5 * * * *' 表达式（每5分钟执行）")
	fmt.Println("- 可以通过 StartWithCustomCron() 方法使用自定义表达式")
	fmt.Println("- 支持标准的6位cron表达式（秒 分 时 日 月 周）")
}
