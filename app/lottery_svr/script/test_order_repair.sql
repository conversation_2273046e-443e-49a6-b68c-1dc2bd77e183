-- 订单补单功能测试SQL脚本
-- 使用方法：在MySQL客户端中执行这些SQL语句来测试补单功能

-- 设置当前年月（请根据实际情况修改）
SET @year_month = '202506';

-- 1. 查看当前待补单的订单（30分钟到5分钟前，status=0）
SET @sql = CONCAT('
SELECT 
    order_id,
    order_type,
    status,
    create_time,
    update_time,
    TIMESTAMPDIFF(MINUTE, create_time, NOW()) as minutes_ago,
    CASE 
        WHEN order_type = 1 THEN "纯抽奖"
        WHEN order_type = 2 THEN "抽奖发奖"
        WHEN order_type = 3 THEN "扣抽奖券抽奖发奖"
        WHEN order_type = 4 THEN "发放抽奖券"
        ELSE "未知类型"
    END as order_type_name
FROM t_order_', @year_month, '
WHERE status = 0 
AND create_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE_SUB(NOW(), INTERVAL 5 MINUTE)
ORDER BY order_type, create_time DESC
LIMIT 20');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 按订单类型统计待补单订单数量
SET @sql = CONCAT('
SELECT 
    order_type,
    CASE 
        WHEN order_type = 1 THEN "纯抽奖"
        WHEN order_type = 2 THEN "抽奖发奖"
        WHEN order_type = 3 THEN "扣抽奖券抽奖发奖"
        WHEN order_type = 4 THEN "发放抽奖券"
        ELSE "未知类型"
    END as order_type_name,
    COUNT(*) as pending_count
FROM t_order_', @year_month, '
WHERE status = 0 
AND create_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE_SUB(NOW(), INTERVAL 5 MINUTE)
GROUP BY order_type
ORDER BY order_type');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 查看最近补单成功的订单（最近10分钟内状态从0变为1或2的订单）
SET @sql = CONCAT('
SELECT 
    order_id,
    order_type,
    status,
    create_time,
    update_time,
    CASE 
        WHEN status = 0 THEN "初始化"
        WHEN status = 1 THEN "成功"
        WHEN status = 2 THEN "失败"
        ELSE "未知"
    END as status_desc,
    CASE 
        WHEN order_type = 1 THEN "纯抽奖"
        WHEN order_type = 2 THEN "抽奖发奖"
        WHEN order_type = 3 THEN "扣抽奖券抽奖发奖"
        WHEN order_type = 4 THEN "发放抽奖券"
        ELSE "未知类型"
    END as order_type_name,
    TIMESTAMPDIFF(MINUTE, update_time, NOW()) as updated_minutes_ago
FROM t_order_', @year_month, '
WHERE update_time > DATE_SUB(NOW(), INTERVAL 10 MINUTE)
AND status IN (1, 2)
AND create_time < DATE_SUB(NOW(), INTERVAL 5 MINUTE)
ORDER BY update_time DESC
LIMIT 20');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 检查特定订单的补单情况（请替换为实际的order_id）
-- SET @test_order_id = 'your_order_id_here';
-- 
-- -- 查看订单基本信息
-- SET @sql = CONCAT('
-- SELECT 
--     order_id,
--     order_type,
--     status,
--     biz_timestamp,
--     create_time,
--     update_time,
--     map_ext
-- FROM t_order_', @year_month, ' 
-- WHERE order_id = ?');
-- PREPARE stmt FROM @sql;
-- EXECUTE stmt USING @test_order_id;
-- DEALLOCATE PREPARE stmt;
-- 
-- -- 查看发奖记录
-- SET @sql = CONCAT('
-- SELECT 
--     present_id,
--     order_id,
--     prize_type,
--     prize_id,
--     prize_name,
--     prize_num,
--     status,
--     create_time,
--     update_time
-- FROM t_present_', @year_month, ' 
-- WHERE order_id = ?');
-- PREPARE stmt FROM @sql;
-- EXECUTE stmt USING @test_order_id;
-- DEALLOCATE PREPARE stmt;
-- 
-- -- 查看消费记录
-- SET @sql = CONCAT('
-- SELECT 
--     consume_id,
--     order_id,
--     consume_type,
--     asset_id,
--     asset_num,
--     status,
--     create_time,
--     update_time
-- FROM t_consume_', @year_month, ' 
-- WHERE order_id = ?');
-- PREPARE stmt FROM @sql;
-- EXECUTE stmt USING @test_order_id;
-- DEALLOCATE PREPARE stmt;

-- 5. 统计各状态订单数量
SET @sql = CONCAT('
SELECT 
    status,
    CASE 
        WHEN status = 0 THEN "初始化"
        WHEN status = 1 THEN "成功"
        WHEN status = 2 THEN "失败"
        ELSE "未知"
    END as status_desc,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM t_order_', @year_month, '), 2) as percentage
FROM t_order_', @year_month, '
GROUP BY status
ORDER BY status');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 查看补单效果统计（最近1小时内的状态变化）
SET @sql = CONCAT('
SELECT 
    "最近1小时补单效果统计" as description,
    COUNT(CASE WHEN status = 1 AND update_time > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as success_repairs,
    COUNT(CASE WHEN status = 2 AND update_time > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as fail_repairs,
    COUNT(CASE WHEN status = 0 AND create_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 1 END) as pending_repairs
FROM t_order_', @year_month);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 检查分月表是否存在
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    CREATE_TIME
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE 't_order_%'
ORDER BY TABLE_NAME DESC;

-- 使用说明：
-- 1. 修改 @year_month 变量为当前需要检查的年月
-- 2. 如果需要检查特定订单，取消注释第4部分并设置 @test_order_id
-- 3. 定期执行此脚本来监控补单功能的效果
-- 4. 关注待补单订单数量的变化趋势

-- 补单逻辑验证：
-- - 纯抽奖订单：有发奖记录 → 成功，无发奖记录 → 失败
-- - 抽奖发奖订单：发奖记录全部成功 → 成功，其他 → 失败
-- - 扣抽奖券抽奖发奖订单：消费成功且发奖全部成功 → 成功，其他 → 失败
-- - 发放抽奖券订单：发奖记录全部成功 → 成功，其他 → 失败
