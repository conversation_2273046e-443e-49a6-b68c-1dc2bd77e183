// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: moe_risk_proto/moe_risk_adapter/server/moe_risk_adapter.proto

package server

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	MoeRiskAdapter_RiskReport_FullMethodName = "/moe_risk_adapter.MoeRiskAdapter/RiskReport"
	MoeRiskAdapter_RiskCheck_FullMethodName  = "/moe_risk_adapter.MoeRiskAdapter/RiskCheck"
	MoeRiskAdapter_RiskQuery_FullMethodName  = "/moe_risk_adapter.MoeRiskAdapter/RiskQuery"
)

// MoeRiskAdapterClient is the client API for MoeRiskAdapter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MoeRiskAdapterClient interface {
	// 风控上报
	RiskReport(ctx context.Context, in *RiskReportReq, opts ...grpc.CallOption) (*RiskReportRsp, error)
	// 统一校验（同步）
	RiskCheck(ctx context.Context, in *RiskCheckReq, opts ...grpc.CallOption) (*RiskCheckRsp, error)
	// 风控查询
	RiskQuery(ctx context.Context, in *RiskQueryReq, opts ...grpc.CallOption) (*RiskQueryRsp, error)
}

type moeRiskAdapterClient struct {
	cc grpc.ClientConnInterface
}

func NewMoeRiskAdapterClient(cc grpc.ClientConnInterface) MoeRiskAdapterClient {
	return &moeRiskAdapterClient{cc}
}

func (c *moeRiskAdapterClient) RiskReport(ctx context.Context, in *RiskReportReq, opts ...grpc.CallOption) (*RiskReportRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RiskReportRsp)
	err := c.cc.Invoke(ctx, MoeRiskAdapter_RiskReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moeRiskAdapterClient) RiskCheck(ctx context.Context, in *RiskCheckReq, opts ...grpc.CallOption) (*RiskCheckRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RiskCheckRsp)
	err := c.cc.Invoke(ctx, MoeRiskAdapter_RiskCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moeRiskAdapterClient) RiskQuery(ctx context.Context, in *RiskQueryReq, opts ...grpc.CallOption) (*RiskQueryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RiskQueryRsp)
	err := c.cc.Invoke(ctx, MoeRiskAdapter_RiskQuery_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MoeRiskAdapterServer is the server API for MoeRiskAdapter service.
// All implementations should embed UnimplementedMoeRiskAdapterServer
// for forward compatibility
type MoeRiskAdapterServer interface {
	// 风控上报
	RiskReport(context.Context, *RiskReportReq) (*RiskReportRsp, error)
	// 统一校验（同步）
	RiskCheck(context.Context, *RiskCheckReq) (*RiskCheckRsp, error)
	// 风控查询
	RiskQuery(context.Context, *RiskQueryReq) (*RiskQueryRsp, error)
}

// UnimplementedMoeRiskAdapterServer should be embedded to have forward compatible implementations.
type UnimplementedMoeRiskAdapterServer struct {
}

func (UnimplementedMoeRiskAdapterServer) RiskReport(context.Context, *RiskReportReq) (*RiskReportRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RiskReport not implemented")
}
func (UnimplementedMoeRiskAdapterServer) RiskCheck(context.Context, *RiskCheckReq) (*RiskCheckRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RiskCheck not implemented")
}
func (UnimplementedMoeRiskAdapterServer) RiskQuery(context.Context, *RiskQueryReq) (*RiskQueryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RiskQuery not implemented")
}

// UnsafeMoeRiskAdapterServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MoeRiskAdapterServer will
// result in compilation errors.
type UnsafeMoeRiskAdapterServer interface {
	mustEmbedUnimplementedMoeRiskAdapterServer()
}

func RegisterMoeRiskAdapterServer(s grpc.ServiceRegistrar, srv MoeRiskAdapterServer) {
	s.RegisterService(&MoeRiskAdapter_ServiceDesc, srv)
}

func _MoeRiskAdapter_RiskReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RiskReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoeRiskAdapterServer).RiskReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoeRiskAdapter_RiskReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoeRiskAdapterServer).RiskReport(ctx, req.(*RiskReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoeRiskAdapter_RiskCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RiskCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoeRiskAdapterServer).RiskCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoeRiskAdapter_RiskCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoeRiskAdapterServer).RiskCheck(ctx, req.(*RiskCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoeRiskAdapter_RiskQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RiskQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoeRiskAdapterServer).RiskQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoeRiskAdapter_RiskQuery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoeRiskAdapterServer).RiskQuery(ctx, req.(*RiskQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

// MoeRiskAdapter_ServiceDesc is the grpc.ServiceDesc for MoeRiskAdapter service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MoeRiskAdapter_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moe_risk_adapter.MoeRiskAdapter",
	HandlerType: (*MoeRiskAdapterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RiskReport",
			Handler:    _MoeRiskAdapter_RiskReport_Handler,
		},
		{
			MethodName: "RiskCheck",
			Handler:    _MoeRiskAdapter_RiskCheck_Handler,
		},
		{
			MethodName: "RiskQuery",
			Handler:    _MoeRiskAdapter_RiskQuery_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moe_risk_proto/moe_risk_adapter/server/moe_risk_adapter.proto",
}
