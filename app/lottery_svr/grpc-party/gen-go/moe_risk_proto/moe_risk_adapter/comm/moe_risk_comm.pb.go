// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: moe_risk_proto/moe_risk_adapter/comm/moe_risk_comm.proto

package comm

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 风控校验类型
type RiskCheckType int32

const (
	RiskCheckType_RiskCheckType_Unknown  RiskCheckType = 0 // 无效类型
	RiskCheckType_RiskCheckType_Receive  RiskCheckType = 1 // 领取校验(eg. 用户手动点击领奖)
	RiskCheckType_RiskCheckType_Direct   RiskCheckType = 2 // 直充校验(eg. 看完广告/活动结束系统自动发奖)
	RiskCheckType_RiskCheckType_Exchange RiskCheckType = 3 // 兑换校验
	RiskCheckType_RiskCheckType_Cashout  RiskCheckType = 4 // 提现校验
)

// Enum value maps for RiskCheckType.
var (
	RiskCheckType_name = map[int32]string{
		0: "RiskCheckType_Unknown",
		1: "RiskCheckType_Receive",
		2: "RiskCheckType_Direct",
		3: "RiskCheckType_Exchange",
		4: "RiskCheckType_Cashout",
	}
	RiskCheckType_value = map[string]int32{
		"RiskCheckType_Unknown":  0,
		"RiskCheckType_Receive":  1,
		"RiskCheckType_Direct":   2,
		"RiskCheckType_Exchange": 3,
		"RiskCheckType_Cashout":  4,
	}
)

func (x RiskCheckType) Enum() *RiskCheckType {
	p := new(RiskCheckType)
	*p = x
	return p
}

func (x RiskCheckType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskCheckType) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes[0].Descriptor()
}

func (RiskCheckType) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes[0]
}

func (x RiskCheckType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskCheckType.Descriptor instead.
func (RiskCheckType) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{0}
}

// 风控维度
type RiskDimension int32

const (
	RiskDimension_RiskDimension_Unknown  RiskDimension = 0 // 无效类型
	RiskDimension_RiskDimension_BizId    RiskDimension = 1 // 某个货币体系的整体维度(eg. 全民短剧金币体系)
	RiskDimension_RiskDimension_Scene    RiskDimension = 2 // 主场景维度(eg. 任务、广告、兑换、提现)
	RiskDimension_RiskDimension_SubScene RiskDimension = 3 // 子场景维度(eg. 主场景=任务 -> 子场景=吃饭、睡觉、宝箱...)
	RiskDimension_RiskDimension_ActId    RiskDimension = 4 // 活动Id维度(eg. 某一类任务、某一期运营活动#包含多个奖励发放#)
	RiskDimension_RiskDimension_PlayId   RiskDimension = 5 // 具体玩法ID维度(eg. 任务taskID、广告位ID、活动ID)
	RiskDimension_RiskDimension_ExtId    RiskDimension = 6 // 玩法下自定义扩展维度(eg. 某个任务taskID下额外的奖励发放, 某个任务taskID下广告的发放)
)

// Enum value maps for RiskDimension.
var (
	RiskDimension_name = map[int32]string{
		0: "RiskDimension_Unknown",
		1: "RiskDimension_BizId",
		2: "RiskDimension_Scene",
		3: "RiskDimension_SubScene",
		4: "RiskDimension_ActId",
		5: "RiskDimension_PlayId",
		6: "RiskDimension_ExtId",
	}
	RiskDimension_value = map[string]int32{
		"RiskDimension_Unknown":  0,
		"RiskDimension_BizId":    1,
		"RiskDimension_Scene":    2,
		"RiskDimension_SubScene": 3,
		"RiskDimension_ActId":    4,
		"RiskDimension_PlayId":   5,
		"RiskDimension_ExtId":    6,
	}
)

func (x RiskDimension) Enum() *RiskDimension {
	p := new(RiskDimension)
	*p = x
	return p
}

func (x RiskDimension) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskDimension) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes[1].Descriptor()
}

func (RiskDimension) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes[1]
}

func (x RiskDimension) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskDimension.Descriptor instead.
func (RiskDimension) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{1}
}

// 出入账类型
type RiskOpType int32

const (
	RiskOpType_RiskOpType_Unknown RiskOpType = 0   // 无效类型
	RiskOpType_RiskOpType_Income  RiskOpType = 100 // 用户入账
	RiskOpType_RiskOpType_Outcome RiskOpType = 200 // 用户出账
)

// Enum value maps for RiskOpType.
var (
	RiskOpType_name = map[int32]string{
		0:   "RiskOpType_Unknown",
		100: "RiskOpType_Income",
		200: "RiskOpType_Outcome",
	}
	RiskOpType_value = map[string]int32{
		"RiskOpType_Unknown": 0,
		"RiskOpType_Income":  100,
		"RiskOpType_Outcome": 200,
	}
)

func (x RiskOpType) Enum() *RiskOpType {
	p := new(RiskOpType)
	*p = x
	return p
}

func (x RiskOpType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskOpType) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes[2].Descriptor()
}

func (RiskOpType) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes[2]
}

func (x RiskOpType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskOpType.Descriptor instead.
func (RiskOpType) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{2}
}

// 触发类型
type RiskTrigType int32

const (
	RiskTrigType_RiskTrigType_Unknown      RiskTrigType = 0   // 无效类型
	RiskTrigType_RiskTrigType_User_Direct  RiskTrigType = 100 // 用户手动触发
	RiskTrigType_RiskTrigType_Sys_Direct   RiskTrigType = 200 // 系统直充
	RiskTrigType_RiskTrigType_Sys_Rollback RiskTrigType = 300 // 系统回滚
	RiskTrigType_RiskTrigType_Sys_Expire   RiskTrigType = 400 // 系统过期淘汰
	RiskTrigType_RiskTrigType_Sys_Clear    RiskTrigType = 500 // 系统清空
)

// Enum value maps for RiskTrigType.
var (
	RiskTrigType_name = map[int32]string{
		0:   "RiskTrigType_Unknown",
		100: "RiskTrigType_User_Direct",
		200: "RiskTrigType_Sys_Direct",
		300: "RiskTrigType_Sys_Rollback",
		400: "RiskTrigType_Sys_Expire",
		500: "RiskTrigType_Sys_Clear",
	}
	RiskTrigType_value = map[string]int32{
		"RiskTrigType_Unknown":      0,
		"RiskTrigType_User_Direct":  100,
		"RiskTrigType_Sys_Direct":   200,
		"RiskTrigType_Sys_Rollback": 300,
		"RiskTrigType_Sys_Expire":   400,
		"RiskTrigType_Sys_Clear":    500,
	}
)

func (x RiskTrigType) Enum() *RiskTrigType {
	p := new(RiskTrigType)
	*p = x
	return p
}

func (x RiskTrigType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskTrigType) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes[3].Descriptor()
}

func (RiskTrigType) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes[3]
}

func (x RiskTrigType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskTrigType.Descriptor instead.
func (RiskTrigType) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{3}
}

// 打击类型
type PunishType int32

const (
	PunishType_PunishType_Unknown    PunishType = 0 // 无效类型
	PunishType_PunishType_Block      PunishType = 1 // 阻断, 提示语从punishDetail.strPrompt获取
	PunishType_PunishType_NeedVerify PunishType = 2 // 需要安全验证, 验证url从 punishDetail.strCommVerifyUrl 获取
	PunishType_PunishType_Degrade    PunishType = 3 // 降级保护
)

// Enum value maps for PunishType.
var (
	PunishType_name = map[int32]string{
		0: "PunishType_Unknown",
		1: "PunishType_Block",
		2: "PunishType_NeedVerify",
		3: "PunishType_Degrade",
	}
	PunishType_value = map[string]int32{
		"PunishType_Unknown":    0,
		"PunishType_Block":      1,
		"PunishType_NeedVerify": 2,
		"PunishType_Degrade":    3,
	}
)

func (x PunishType) Enum() *PunishType {
	p := new(PunishType)
	*p = x
	return p
}

func (x PunishType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PunishType) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes[4].Descriptor()
}

func (PunishType) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes[4]
}

func (x PunishType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PunishType.Descriptor instead.
func (PunishType) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{4}
}

// 风控类型
type RiskType int32

const (
	RiskType_RiskType_Unknown      RiskType = 0 // 无效值
	RiskType_RiskType_Safety       RiskType = 1 // 安全打击
	RiskType_RiskType_RiskPolicy   RiskType = 2 // 风控策略
	RiskType_RiskType_CustomSafety RiskType = 3 // 自定义安全打击
)

// Enum value maps for RiskType.
var (
	RiskType_name = map[int32]string{
		0: "RiskType_Unknown",
		1: "RiskType_Safety",
		2: "RiskType_RiskPolicy",
		3: "RiskType_CustomSafety",
	}
	RiskType_value = map[string]int32{
		"RiskType_Unknown":      0,
		"RiskType_Safety":       1,
		"RiskType_RiskPolicy":   2,
		"RiskType_CustomSafety": 3,
	}
)

func (x RiskType) Enum() *RiskType {
	p := new(RiskType)
	*p = x
	return p
}

func (x RiskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskType) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes[5].Descriptor()
}

func (RiskType) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes[5]
}

func (x RiskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskType.Descriptor instead.
func (RiskType) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{5}
}

// 统一风控打击原因枚举
type RiskPunishReason int32

const (
	RiskPunishReason_RiskPunishReason_Unknown        RiskPunishReason = 0  //无效值
	RiskPunishReason_RiskPunishReason_TotalCntLimit  RiskPunishReason = 1  //整体限频
	RiskPunishReason_RiskPunishReason_TotalValLimit  RiskPunishReason = 2  //整体限额
	RiskPunishReason_RiskPunishReason_UserCntLimit   RiskPunishReason = 3  //用户限频
	RiskPunishReason_RiskPunishReason_UserValLimit   RiskPunishReason = 4  //用户限额
	RiskPunishReason_RiskPunishReason_LargeBalance   RiskPunishReason = 5  //大额结余
	RiskPunishReason_RiskPunishReason_SingleLargeVal RiskPunishReason = 6  //单笔大额
	RiskPunishReason_RiskPunishReason_WhiteListLock  RiskPunishReason = 7  //白名单锁定
	RiskPunishReason_RiskPunishReason_AllLock        RiskPunishReason = 8  //全站锁定
	RiskPunishReason_RiskPunishReason_TotalRTPLimit  RiskPunishReason = 9  //整体返奖率(rtp=return to player)限制
	RiskPunishReason_RiskPunishReason_UserRTPLimit   RiskPunishReason = 10 //用户返奖率限制
)

// Enum value maps for RiskPunishReason.
var (
	RiskPunishReason_name = map[int32]string{
		0:  "RiskPunishReason_Unknown",
		1:  "RiskPunishReason_TotalCntLimit",
		2:  "RiskPunishReason_TotalValLimit",
		3:  "RiskPunishReason_UserCntLimit",
		4:  "RiskPunishReason_UserValLimit",
		5:  "RiskPunishReason_LargeBalance",
		6:  "RiskPunishReason_SingleLargeVal",
		7:  "RiskPunishReason_WhiteListLock",
		8:  "RiskPunishReason_AllLock",
		9:  "RiskPunishReason_TotalRTPLimit",
		10: "RiskPunishReason_UserRTPLimit",
	}
	RiskPunishReason_value = map[string]int32{
		"RiskPunishReason_Unknown":        0,
		"RiskPunishReason_TotalCntLimit":  1,
		"RiskPunishReason_TotalValLimit":  2,
		"RiskPunishReason_UserCntLimit":   3,
		"RiskPunishReason_UserValLimit":   4,
		"RiskPunishReason_LargeBalance":   5,
		"RiskPunishReason_SingleLargeVal": 6,
		"RiskPunishReason_WhiteListLock":  7,
		"RiskPunishReason_AllLock":        8,
		"RiskPunishReason_TotalRTPLimit":  9,
		"RiskPunishReason_UserRTPLimit":   10,
	}
)

func (x RiskPunishReason) Enum() *RiskPunishReason {
	p := new(RiskPunishReason)
	*p = x
	return p
}

func (x RiskPunishReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskPunishReason) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes[6].Descriptor()
}

func (RiskPunishReason) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes[6]
}

func (x RiskPunishReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskPunishReason.Descriptor instead.
func (RiskPunishReason) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{6}
}

// 请求通用字段
type CommReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
}

func (x *CommReq) Reset() {
	*x = CommReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommReq) ProtoMessage() {}

func (x *CommReq) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommReq.ProtoReflect.Descriptor instead.
func (*CommReq) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{0}
}

func (x *CommReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

// 场景信息
type RiskSceneInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Biz      string `protobuf:"bytes,1,opt,name=biz,proto3" json:"biz,omitempty"`            // 不同app自行定义, eg. 短剧bizId定义见: proto_risk_scene_dj.jce RISK_DJ_BIZID_xxxx
	Scene    int32  `protobuf:"varint,2,opt,name=scene,proto3" json:"scene,omitempty"`       // 主场景, 定义见: proto_risk_scene_comm.jce:: EmRiskScene
	Subscene int32  `protobuf:"varint,3,opt,name=subscene,proto3" json:"subscene,omitempty"` // 子场景, 公共定义见: proto_risk_scene_comm.jce::  EmRiskSubSceneXXX, 业务自定义枚举见: proto_risk_scene_xxx.jce EmRiskSubSceneXXX
	ActId    string `protobuf:"bytes,4,opt,name=actId,proto3" json:"actId,omitempty"`        // 活动ID(一个活动可能包含多个玩法playId, 由业务自行定义)
	PlayId   string `protobuf:"bytes,5,opt,name=playId,proto3" json:"playId,omitempty"`      // 玩法ID(eg. 任务taskID、广告位ID、活动ID, 由业务自行定义)
	ExtId    string `protobuf:"bytes,6,opt,name=extId,proto3" json:"extId,omitempty"`        // 自定义扩展ID(eg. 某个任务taskID下额外的奖励发放, 某个任务taskID下广告的发放)
}

func (x *RiskSceneInfo) Reset() {
	*x = RiskSceneInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskSceneInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskSceneInfo) ProtoMessage() {}

func (x *RiskSceneInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskSceneInfo.ProtoReflect.Descriptor instead.
func (*RiskSceneInfo) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{1}
}

func (x *RiskSceneInfo) GetBiz() string {
	if x != nil {
		return x.Biz
	}
	return ""
}

func (x *RiskSceneInfo) GetScene() int32 {
	if x != nil {
		return x.Scene
	}
	return 0
}

func (x *RiskSceneInfo) GetSubscene() int32 {
	if x != nil {
		return x.Subscene
	}
	return 0
}

func (x *RiskSceneInfo) GetActId() string {
	if x != nil {
		return x.ActId
	}
	return ""
}

func (x *RiskSceneInfo) GetPlayId() string {
	if x != nil {
		return x.PlayId
	}
	return ""
}

func (x *RiskSceneInfo) GetExtId() string {
	if x != nil {
		return x.ExtId
	}
	return ""
}

// 用户信息
type RiskUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid        string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Qua        string `protobuf:"bytes,2,opt,name=qua,proto3" json:"qua,omitempty"`
	DeviceInfo string `protobuf:"bytes,3,opt,name=deviceInfo,proto3" json:"deviceInfo,omitempty"`
	ClientIp   string `protobuf:"bytes,4,opt,name=clientIp,proto3" json:"clientIp,omitempty"` //风控校验接口需传，用于安全上报
	OpenId     string `protobuf:"bytes,5,opt,name=openId,proto3" json:"openId,omitempty"`
	Qimei36    string `protobuf:"bytes,6,opt,name=qimei36,proto3" json:"qimei36,omitempty"`
}

func (x *RiskUserInfo) Reset() {
	*x = RiskUserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskUserInfo) ProtoMessage() {}

func (x *RiskUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskUserInfo.ProtoReflect.Descriptor instead.
func (*RiskUserInfo) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{2}
}

func (x *RiskUserInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *RiskUserInfo) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *RiskUserInfo) GetDeviceInfo() string {
	if x != nil {
		return x.DeviceInfo
	}
	return ""
}

func (x *RiskUserInfo) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *RiskUserInfo) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *RiskUserInfo) GetQimei36() string {
	if x != nil {
		return x.Qimei36
	}
	return ""
}

// 操作信息
type RiskOpInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpType   int32 `protobuf:"varint,1,opt,name=opType,proto3" json:"opType,omitempty"`     // 操作类型, 定义见: RiskOpType
	TrigType int32 `protobuf:"varint,2,opt,name=trigType,proto3" json:"trigType,omitempty"` // 触发类型, 定义见: RiskTrigType
	OpTimeMs int64 `protobuf:"varint,3,opt,name=opTimeMs,proto3" json:"opTimeMs,omitempty"` // 操作时间, 单位ms(废弃， 后续都用opTs)
	OpTs     int64 `protobuf:"varint,4,opt,name=opTs,proto3" json:"opTs,omitempty"`         // 操作时间, 单位ms
}

func (x *RiskOpInfo) Reset() {
	*x = RiskOpInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskOpInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskOpInfo) ProtoMessage() {}

func (x *RiskOpInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskOpInfo.ProtoReflect.Descriptor instead.
func (*RiskOpInfo) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{3}
}

func (x *RiskOpInfo) GetOpType() int32 {
	if x != nil {
		return x.OpType
	}
	return 0
}

func (x *RiskOpInfo) GetTrigType() int32 {
	if x != nil {
		return x.TrigType
	}
	return 0
}

func (x *RiskOpInfo) GetOpTimeMs() int64 {
	if x != nil {
		return x.OpTimeMs
	}
	return 0
}

func (x *RiskOpInfo) GetOpTs() int64 {
	if x != nil {
		return x.OpTs
	}
	return 0
}

// 资产信息
type RiskAssetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetType    int32  `protobuf:"varint,1,opt,name=assetType,proto3" json:"assetType,omitempty"`       // 资产类型(福利/商品/货币), eg. 短剧资产类型定义见 proto_risk_scene_dj.jce EmDJAssetType
	AssetId      string `protobuf:"bytes,2,opt,name=assetId,proto3" json:"assetId,omitempty"`            // 资产ID(福利ID/商品ID/货币可不填)
	AssetNum     int64  `protobuf:"varint,3,opt,name=assetNum,proto3" json:"assetNum,omitempty"`         // 资产总数量
	AssetValue   int64  `protobuf:"varint,4,opt,name=assetValue,proto3" json:"assetValue,omitempty"`     // 资产总价值
	AssetBalance int64  `protobuf:"varint,5,opt,name=assetBalance,proto3" json:"assetBalance,omitempty"` // 资产余额(选填, 出账时余额必填)
}

func (x *RiskAssetInfo) Reset() {
	*x = RiskAssetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskAssetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskAssetInfo) ProtoMessage() {}

func (x *RiskAssetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskAssetInfo.ProtoReflect.Descriptor instead.
func (*RiskAssetInfo) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{4}
}

func (x *RiskAssetInfo) GetAssetType() int32 {
	if x != nil {
		return x.AssetType
	}
	return 0
}

func (x *RiskAssetInfo) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *RiskAssetInfo) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *RiskAssetInfo) GetAssetValue() int64 {
	if x != nil {
		return x.AssetValue
	}
	return 0
}

func (x *RiskAssetInfo) GetAssetBalance() int64 {
	if x != nil {
		return x.AssetBalance
	}
	return 0
}

// 系统信息(风控系统内部的一些信息)
type RiskSysInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SysExt map[string]string `protobuf:"bytes,1,rep,name=sysExt,proto3" json:"sysExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 操作时间, 单位ms
}

func (x *RiskSysInfo) Reset() {
	*x = RiskSysInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskSysInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskSysInfo) ProtoMessage() {}

func (x *RiskSysInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskSysInfo.ProtoReflect.Descriptor instead.
func (*RiskSysInfo) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{5}
}

func (x *RiskSysInfo) GetSysExt() map[string]string {
	if x != nil {
		return x.SysExt
	}
	return nil
}

// 风控事件
type RiskEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventId   string            `protobuf:"bytes,1,opt,name=eventId,proto3" json:"eventId,omitempty"`                                                                                       // 幂等ID
	SceneInfo *RiskSceneInfo    `protobuf:"bytes,2,opt,name=sceneInfo,proto3" json:"sceneInfo,omitempty"`                                                                                   // 场景信息
	UserInfo  *RiskUserInfo     `protobuf:"bytes,3,opt,name=userInfo,proto3" json:"userInfo,omitempty"`                                                                                     // 主人态信息
	OpInfo    *RiskOpInfo       `protobuf:"bytes,4,opt,name=opInfo,proto3" json:"opInfo,omitempty"`                                                                                         // 操作信息
	AssetInfo *RiskAssetInfo    `protobuf:"bytes,5,opt,name=assetInfo,proto3" json:"assetInfo,omitempty"`                                                                                   // (福利/商品/货币)变更的资产信息
	SysInfo   *RiskSysInfo      `protobuf:"bytes,6,opt,name=sysInfo,proto3" json:"sysInfo,omitempty"`                                                                                       // 系统信息(风控系统内部的一些信息), 业务不需要填
	MapExt    map[string]string `protobuf:"bytes,7,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 风控扩展信息, 方便不同接入场景功能扩展
}

func (x *RiskEvent) Reset() {
	*x = RiskEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskEvent) ProtoMessage() {}

func (x *RiskEvent) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskEvent.ProtoReflect.Descriptor instead.
func (*RiskEvent) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{6}
}

func (x *RiskEvent) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *RiskEvent) GetSceneInfo() *RiskSceneInfo {
	if x != nil {
		return x.SceneInfo
	}
	return nil
}

func (x *RiskEvent) GetUserInfo() *RiskUserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *RiskEvent) GetOpInfo() *RiskOpInfo {
	if x != nil {
		return x.OpInfo
	}
	return nil
}

func (x *RiskEvent) GetAssetInfo() *RiskAssetInfo {
	if x != nil {
		return x.AssetInfo
	}
	return nil
}

func (x *RiskEvent) GetSysInfo() *RiskSysInfo {
	if x != nil {
		return x.SysInfo
	}
	return nil
}

func (x *RiskEvent) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

// 风控打击详情
type PunishDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RiskType         int32  `protobuf:"varint,1,opt,name=riskType,proto3" json:"riskType,omitempty"`                // 命中的风控类型, RiskType
	PunishReason     int32  `protobuf:"varint,2,opt,name=punishReason,proto3" json:"punishReason,omitempty"`        // 打击原因枚举 不同RiskType各自定义, 安全类: 透传安全错误码, 风控策略类: RiskPunishReason
	StrPunishReason  string `protobuf:"bytes,3,opt,name=strPunishReason,proto3" json:"strPunishReason,omitempty"`   // 打击原因
	StrPrompt        string `protobuf:"bytes,4,opt,name=strPrompt,proto3" json:"strPrompt,omitempty"`               // 打击提示语
	StrCommVerifyUrl string `protobuf:"bytes,5,opt,name=strCommVerifyUrl,proto3" json:"strCommVerifyUrl,omitempty"` // 安全验证链接
}

func (x *PunishDetail) Reset() {
	*x = PunishDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PunishDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PunishDetail) ProtoMessage() {}

func (x *PunishDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PunishDetail.ProtoReflect.Descriptor instead.
func (*PunishDetail) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{7}
}

func (x *PunishDetail) GetRiskType() int32 {
	if x != nil {
		return x.RiskType
	}
	return 0
}

func (x *PunishDetail) GetPunishReason() int32 {
	if x != nil {
		return x.PunishReason
	}
	return 0
}

func (x *PunishDetail) GetStrPunishReason() string {
	if x != nil {
		return x.StrPunishReason
	}
	return ""
}

func (x *PunishDetail) GetStrPrompt() string {
	if x != nil {
		return x.StrPrompt
	}
	return ""
}

func (x *PunishDetail) GetStrCommVerifyUrl() string {
	if x != nil {
		return x.StrCommVerifyUrl
	}
	return ""
}

// 风控打击结果
type RiskCheckResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result       uint32        `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`            //(0-通过 1-不通过)
	PunishType   int32         `protobuf:"varint,2,opt,name=punishType,proto3" json:"punishType,omitempty"`    //打击类型 emPunishType
	PunishDetail *PunishDetail `protobuf:"bytes,3,opt,name=punishDetail,proto3" json:"punishDetail,omitempty"` //打击具体信息
}

func (x *RiskCheckResult) Reset() {
	*x = RiskCheckResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskCheckResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskCheckResult) ProtoMessage() {}

func (x *RiskCheckResult) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskCheckResult.ProtoReflect.Descriptor instead.
func (*RiskCheckResult) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{8}
}

func (x *RiskCheckResult) GetResult() uint32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *RiskCheckResult) GetPunishType() int32 {
	if x != nil {
		return x.PunishType
	}
	return 0
}

func (x *RiskCheckResult) GetPunishDetail() *PunishDetail {
	if x != nil {
		return x.PunishDetail
	}
	return nil
}

// 风控计数
type RiskCountItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts    uint32          `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`                                                                                                // 周期起始时间戳
	Cnt   map[int32]int64 `protobuf:"bytes,2,rep,name=cnt,proto3" json:"cnt,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`     // 操作计数, key: RiskTrigType, 0表示全部
	Value map[int32]int64 `protobuf:"bytes,3,rep,name=value,proto3" json:"value,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 价值计数, key: RiskTrigType, 0表示全部
}

func (x *RiskCountItem) Reset() {
	*x = RiskCountItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskCountItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskCountItem) ProtoMessage() {}

func (x *RiskCountItem) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskCountItem.ProtoReflect.Descriptor instead.
func (*RiskCountItem) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{9}
}

func (x *RiskCountItem) GetTs() uint32 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *RiskCountItem) GetCnt() map[int32]int64 {
	if x != nil {
		return x.Cnt
	}
	return nil
}

func (x *RiskCountItem) GetValue() map[int32]int64 {
	if x != nil {
		return x.Value
	}
	return nil
}

// 时间维度的计数统计
type RiskTimeCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts         uint32           `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`                // 当前时间戳
	MinCount   []*RiskCountItem `protobuf:"bytes,2,rep,name=minCount,proto3" json:"minCount,omitempty"`     // 最近N分钟统计,
	HourCount  []*RiskCountItem `protobuf:"bytes,3,rep,name=hourCount,proto3" json:"hourCount,omitempty"`   // 最近N小时统计
	DayCount   []*RiskCountItem `protobuf:"bytes,4,rep,name=dayCount,proto3" json:"dayCount,omitempty"`     // 最近N天统计
	WeekCount  []*RiskCountItem `protobuf:"bytes,5,rep,name=weekCount,proto3" json:"weekCount,omitempty"`   // 最近N周统计
	MonthCount []*RiskCountItem `protobuf:"bytes,6,rep,name=monthCount,proto3" json:"monthCount,omitempty"` // 最近N月统计
}

func (x *RiskTimeCount) Reset() {
	*x = RiskTimeCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskTimeCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskTimeCount) ProtoMessage() {}

func (x *RiskTimeCount) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskTimeCount.ProtoReflect.Descriptor instead.
func (*RiskTimeCount) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{10}
}

func (x *RiskTimeCount) GetTs() uint32 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *RiskTimeCount) GetMinCount() []*RiskCountItem {
	if x != nil {
		return x.MinCount
	}
	return nil
}

func (x *RiskTimeCount) GetHourCount() []*RiskCountItem {
	if x != nil {
		return x.HourCount
	}
	return nil
}

func (x *RiskTimeCount) GetDayCount() []*RiskCountItem {
	if x != nil {
		return x.DayCount
	}
	return nil
}

func (x *RiskTimeCount) GetWeekCount() []*RiskCountItem {
	if x != nil {
		return x.WeekCount
	}
	return nil
}

func (x *RiskTimeCount) GetMonthCount() []*RiskCountItem {
	if x != nil {
		return x.MonthCount
	}
	return nil
}

// 风控统计信息
type RiskStat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InStat  *RiskTimeCount `protobuf:"bytes,1,opt,name=inStat,proto3" json:"inStat,omitempty"`   // 入账统计
	OutStat *RiskTimeCount `protobuf:"bytes,2,opt,name=outStat,proto3" json:"outStat,omitempty"` // 出账统计
}

func (x *RiskStat) Reset() {
	*x = RiskStat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskStat) ProtoMessage() {}

func (x *RiskStat) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskStat.ProtoReflect.Descriptor instead.
func (*RiskStat) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{11}
}

func (x *RiskStat) GetInStat() *RiskTimeCount {
	if x != nil {
		return x.InStat
	}
	return nil
}

func (x *RiskStat) GetOutStat() *RiskTimeCount {
	if x != nil {
		return x.OutStat
	}
	return nil
}

// 检查资产信息
type AssetCheckInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetType    int32  `protobuf:"varint,1,opt,name=assetType,proto3" json:"assetType,omitempty"`       // 资产类型(货币/福利/商品), eg. 短剧资产类型定义见 proto_risk_scene_dj.jce EmDJAssetType
	AssetId      string `protobuf:"bytes,2,opt,name=assetId,proto3" json:"assetId,omitempty"`            // 资产ID(福利ID/商品ID/货币可不填)
	AssetNum     int64  `protobuf:"varint,3,opt,name=assetNum,proto3" json:"assetNum,omitempty"`         // 资产总数量
	AssetValue   int64  `protobuf:"varint,4,opt,name=assetValue,proto3" json:"assetValue,omitempty"`     // 资产总价值(福利/货币, 可以不填。 其他场景必填!!!, 单位是货币最小单位)
	AssetBalance int64  `protobuf:"varint,5,opt,name=assetBalance,proto3" json:"assetBalance,omitempty"` // 资产余额(选填, 出账时余额必填)
}

func (x *AssetCheckInfo) Reset() {
	*x = AssetCheckInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetCheckInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetCheckInfo) ProtoMessage() {}

func (x *AssetCheckInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetCheckInfo.ProtoReflect.Descriptor instead.
func (*AssetCheckInfo) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{12}
}

func (x *AssetCheckInfo) GetAssetType() int32 {
	if x != nil {
		return x.AssetType
	}
	return 0
}

func (x *AssetCheckInfo) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *AssetCheckInfo) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *AssetCheckInfo) GetAssetValue() int64 {
	if x != nil {
		return x.AssetValue
	}
	return 0
}

func (x *AssetCheckInfo) GetAssetBalance() int64 {
	if x != nil {
		return x.AssetBalance
	}
	return 0
}

// 风控校验请求额外信息
type RiskCheckReqExtraInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cnt     int64  `protobuf:"varint,1,opt,name=cnt,proto3" json:"cnt,omitempty"`        //指定次数,默认值1(用户多次行为合并为一次风控检查调用的场景，需指定用户行为次数)
	EventId string `protobuf:"bytes,2,opt,name=eventId,proto3" json:"eventId,omitempty"` //幂等ID(有些同步规则, 需要校验时同时更新计数信息, 需要做幂等保护)
}

func (x *RiskCheckReqExtraInfo) Reset() {
	*x = RiskCheckReqExtraInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskCheckReqExtraInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskCheckReqExtraInfo) ProtoMessage() {}

func (x *RiskCheckReqExtraInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskCheckReqExtraInfo.ProtoReflect.Descriptor instead.
func (*RiskCheckReqExtraInfo) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{13}
}

func (x *RiskCheckReqExtraInfo) GetCnt() int64 {
	if x != nil {
		return x.Cnt
	}
	return 0
}

func (x *RiskCheckReqExtraInfo) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

// 通用安全校验信息
type CommSafetyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExtMap map[string]string `protobuf:"bytes,1,rep,name=extMap,proto3" json:"extMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CommSafetyInfo) Reset() {
	*x = CommSafetyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommSafetyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommSafetyInfo) ProtoMessage() {}

func (x *CommSafetyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommSafetyInfo.ProtoReflect.Descriptor instead.
func (*CommSafetyInfo) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{14}
}

func (x *CommSafetyInfo) GetExtMap() map[string]string {
	if x != nil {
		return x.ExtMap
	}
	return nil
}

// 自定义安全校验信息
type CustomSafetyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    string            `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	Category string            `protobuf:"bytes,2,opt,name=category,proto3" json:"category,omitempty"`
	ExtMap   map[string]string `protobuf:"bytes,3,rep,name=extMap,proto3" json:"extMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CustomSafetyInfo) Reset() {
	*x = CustomSafetyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomSafetyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomSafetyInfo) ProtoMessage() {}

func (x *CustomSafetyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomSafetyInfo.ProtoReflect.Descriptor instead.
func (*CustomSafetyInfo) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{15}
}

func (x *CustomSafetyInfo) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CustomSafetyInfo) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *CustomSafetyInfo) GetExtMap() map[string]string {
	if x != nil {
		return x.ExtMap
	}
	return nil
}

// 风控校验请求安全信息
type RiskCheckSafetyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommSafe   *CommSafetyInfo   `protobuf:"bytes,1,opt,name=commSafe,proto3" json:"commSafe,omitempty"`     // 通用安全校验(全平台统一安全策略校验)
	CustomSafe *CustomSafetyInfo `protobuf:"bytes,2,opt,name=customSafe,proto3" json:"customSafe,omitempty"` // 自定义安全校验(自定义安全策略校验)
}

func (x *RiskCheckSafetyInfo) Reset() {
	*x = RiskCheckSafetyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskCheckSafetyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskCheckSafetyInfo) ProtoMessage() {}

func (x *RiskCheckSafetyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskCheckSafetyInfo.ProtoReflect.Descriptor instead.
func (*RiskCheckSafetyInfo) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{16}
}

func (x *RiskCheckSafetyInfo) GetCommSafe() *CommSafetyInfo {
	if x != nil {
		return x.CommSafe
	}
	return nil
}

func (x *RiskCheckSafetyInfo) GetCustomSafe() *CustomSafetyInfo {
	if x != nil {
		return x.CustomSafe
	}
	return nil
}

// 透传字段 value
// 接入方需要把该结构体通过json序列化成string, 然后通过相关资产协议的map<string,string> mapExt字段透传
type RiskContext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SceneInfo *RiskSceneInfo `protobuf:"bytes,1,opt,name=sceneInfo,proto3" json:"sceneInfo,omitempty"` // 场景信息
	OpInfo    *RiskOpInfo    `protobuf:"bytes,2,opt,name=opInfo,proto3" json:"opInfo,omitempty"`       // 操作信息
}

func (x *RiskContext) Reset() {
	*x = RiskContext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskContext) ProtoMessage() {}

func (x *RiskContext) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskContext.ProtoReflect.Descriptor instead.
func (*RiskContext) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP(), []int{17}
}

func (x *RiskContext) GetSceneInfo() *RiskSceneInfo {
	if x != nil {
		return x.SceneInfo
	}
	return nil
}

func (x *RiskContext) GetOpInfo() *RiskOpInfo {
	if x != nil {
		return x.OpInfo
	}
	return nil
}

var File_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto protoreflect.FileDescriptor

var file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65,
	0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x6d, 0x6f, 0x65, 0x5f,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x22, 0x1f, 0x0a, 0x07, 0x43, 0x6f, 0x6d,
	0x6d, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x22, 0x97, 0x01, 0x0a, 0x0d, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03,
	0x62, 0x69, 0x7a, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x62, 0x69, 0x7a, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73,
	0x63, 0x65, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x75, 0x62, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x61, 0x63, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x63, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x78, 0x74, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x78, 0x74, 0x49, 0x64, 0x22, 0xa0, 0x01, 0x0a, 0x0c, 0x52, 0x69, 0x73, 0x6b, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x71, 0x75, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x71, 0x75, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x71, 0x69, 0x6d, 0x65, 0x69, 0x33, 0x36, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x71, 0x69, 0x6d, 0x65, 0x69, 0x33, 0x36, 0x22, 0x70, 0x0a, 0x0a, 0x52, 0x69, 0x73, 0x6b, 0x4f,
	0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x74, 0x72, 0x69, 0x67, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x74, 0x72, 0x69, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x54,
	0x69, 0x6d, 0x65, 0x4d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x70, 0x54,
	0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x70, 0x54, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x6f, 0x70, 0x54, 0x73, 0x22, 0xa7, 0x01, 0x0a, 0x0d, 0x52, 0x69,
	0x73, 0x6b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12,
	0x1e, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x22, 0x0a, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x22, 0x88, 0x01, 0x0a, 0x0b, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x79, 0x73, 0x45, 0x78, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x2e,
	0x53, 0x79, 0x73, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x73, 0x79, 0x73,
	0x45, 0x78, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x53, 0x79, 0x73, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb8,
	0x03, 0x0a, 0x09, 0x52, 0x69, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x6f, 0x65, 0x5f,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63,
	0x65, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x37, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x06, 0x6f,
	0x70, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x6f,
	0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x4f, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x6f, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a,
	0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d,
	0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x34, 0x0a, 0x07, 0x73, 0x79,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f,
	0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x79, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x73, 0x79, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x3c, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d,
	0x2e, 0x52, 0x69, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78,
	0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x1a, 0x39,
	0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc2, 0x01, 0x0a, 0x0c, 0x50, 0x75,
	0x6e, 0x69, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x69,
	0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x69,
	0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x75, 0x6e, 0x69, 0x73, 0x68,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x70, 0x75,
	0x6e, 0x69, 0x73, 0x68, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x74,
	0x72, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x72, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x50, 0x72, 0x6f, 0x6d, 0x70,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x50, 0x72, 0x6f, 0x6d,
	0x70, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x74,
	0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x22, 0x8a,
	0x01, 0x0a, 0x0f, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x75,
	0x6e, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x70, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x70, 0x75,
	0x6e, 0x69, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d,
	0x2e, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0c, 0x70,
	0x75, 0x6e, 0x69, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x89, 0x02, 0x0a, 0x0d,
	0x52, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a,
	0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x74, 0x73, 0x12, 0x37, 0x0a,
	0x03, 0x63, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65,
	0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x43, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x03, 0x63, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x74, 0x65, 0x6d, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x36, 0x0a, 0x08, 0x43, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x38, 0x0a,
	0x0a, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc9, 0x02, 0x0a, 0x0d, 0x52, 0x69, 0x73, 0x6b,
	0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x74, 0x73, 0x12, 0x38, 0x0a, 0x08, 0x6d, 0x69, 0x6e,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x6f,
	0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x09, 0x68, 0x6f, 0x75, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x09, 0x68, 0x6f, 0x75, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x38, 0x0a, 0x08, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d,
	0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x08, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x09, 0x77, 0x65, 0x65,
	0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d,
	0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73,
	0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x09, 0x77, 0x65, 0x65, 0x6b,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x6f, 0x65, 0x5f,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x78, 0x0a, 0x08, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x12,
	0x34, 0x0a, 0x06, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e,
	0x52, 0x69, 0x73, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x06, 0x69,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x12, 0x36, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x07, 0x6f, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x22, 0xa8, 0x01,
	0x0a, 0x0e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1c, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x43, 0x0a, 0x15, 0x52, 0x69, 0x73, 0x6b,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x63, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x8e, 0x01,
	0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x41, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x4d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x2e,
	0x45, 0x78, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x65, 0x78, 0x74,
	0x4d, 0x61, 0x70, 0x1a, 0x39, 0x0a, 0x0b, 0x45, 0x78, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc4,
	0x01, 0x0a, 0x10, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x43, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x4d, 0x61, 0x70, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x61, 0x66, 0x65,
	0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x45, 0x78, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x06, 0x65, 0x78, 0x74, 0x4d, 0x61, 0x70, 0x1a, 0x39, 0x0a, 0x0b, 0x45, 0x78,
	0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x91, 0x01, 0x0a, 0x13, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a,
	0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x53, 0x61, 0x66, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x63, 0x6f, 0x6d, 0x6d, 0x53, 0x61, 0x66, 0x65, 0x12, 0x3f, 0x0a, 0x0a, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x53, 0x61, 0x66, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d,
	0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x61, 0x66, 0x65, 0x22, 0x7c, 0x0a, 0x0b, 0x52, 0x69, 0x73,
	0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x3a, 0x0a, 0x09, 0x73, 0x63, 0x65, 0x6e,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x6f,
	0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x06, 0x6f, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x4f, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x06, 0x6f, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x2a, 0x96, 0x01, 0x0a, 0x0d, 0x52, 0x69, 0x73, 0x6b,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x69, 0x73,
	0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x10, 0x01, 0x12,
	0x18, 0x0a, 0x14, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x69, 0x73,
	0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x61, 0x73, 0x68, 0x6f, 0x75, 0x74, 0x10, 0x04,
	0x2a, 0xc4, 0x01, 0x0a, 0x0d, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x17, 0x0a,
	0x13, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x42,
	0x69, 0x7a, 0x49, 0x64, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x69,
	0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x10, 0x02, 0x12,
	0x1a, 0x0a, 0x16, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x52,
	0x69, 0x73, 0x6b, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x41, 0x63, 0x74,
	0x49, 0x64, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x69, 0x6d, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x50, 0x6c, 0x61, 0x79, 0x49, 0x64, 0x10, 0x05, 0x12, 0x17,
	0x0a, 0x13, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x45, 0x78, 0x74, 0x49, 0x64, 0x10, 0x06, 0x2a, 0x54, 0x0a, 0x0a, 0x52, 0x69, 0x73, 0x6b, 0x4f,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x69, 0x73, 0x6b, 0x4f, 0x70, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x15, 0x0a,
	0x11, 0x52, 0x69, 0x73, 0x6b, 0x4f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x10, 0x64, 0x12, 0x17, 0x0a, 0x12, 0x52, 0x69, 0x73, 0x6b, 0x4f, 0x70, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x10, 0xc8, 0x01, 0x2a, 0xbf, 0x01,
	0x0a, 0x0c, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x72, 0x69, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18,
	0x0a, 0x14, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x72, 0x69, 0x67, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x69, 0x73, 0x6b,
	0x54, 0x72, 0x69, 0x67, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x73, 0x65, 0x72, 0x5f, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x10, 0x64, 0x12, 0x1c, 0x0a, 0x17, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x72,
	0x69, 0x67, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x79, 0x73, 0x5f, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x10, 0xc8, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x72, 0x69, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x79, 0x73, 0x5f, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x10, 0xac, 0x02, 0x12, 0x1c, 0x0a, 0x17, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x72, 0x69, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x79, 0x73, 0x5f, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x10,
	0x90, 0x03, 0x12, 0x1b, 0x0a, 0x16, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x72, 0x69, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x53, 0x79, 0x73, 0x5f, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x10, 0xf4, 0x03, 0x2a,
	0x6d, 0x0a, 0x0a, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x12, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x50,
	0x75, 0x6e, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x65, 0x65, 0x64, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x65, 0x67, 0x72, 0x61, 0x64, 0x65, 0x10, 0x03, 0x2a, 0x69,
	0x0a, 0x08, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x69,
	0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x12, 0x13, 0x0a, 0x0f, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x61, 0x66,
	0x65, 0x74, 0x79, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x10, 0x02, 0x12, 0x19,
	0x0a, 0x15, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x10, 0x03, 0x2a, 0x8f, 0x03, 0x0a, 0x10, 0x52, 0x69,
	0x73, 0x6b, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1c,
	0x0a, 0x18, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e,
	0x52, 0x69, 0x73, 0x6b, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x5f, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x01,
	0x12, 0x22, 0x0a, 0x1e, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x56, 0x61, 0x6c, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x75, 0x6e, 0x69,
	0x73, 0x68, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6e, 0x74,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x69, 0x73, 0x6b, 0x50,
	0x75, 0x6e, 0x69, 0x73, 0x68, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x55, 0x73, 0x65, 0x72,
	0x56, 0x61, 0x6c, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x04, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x69,
	0x73, 0x6b, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x4c,
	0x61, 0x72, 0x67, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x10, 0x05, 0x12, 0x23, 0x0a,
	0x1f, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x5f, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x4c, 0x61, 0x72, 0x67, 0x65, 0x56, 0x61, 0x6c,
	0x10, 0x06, 0x12, 0x22, 0x0a, 0x1e, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x4c, 0x6f, 0x63, 0x6b, 0x10, 0x07, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x75,
	0x6e, 0x69, 0x73, 0x68, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x41, 0x6c, 0x6c, 0x4c, 0x6f,
	0x63, 0x6b, 0x10, 0x08, 0x12, 0x22, 0x0a, 0x1e, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x75, 0x6e, 0x69,
	0x73, 0x68, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x54,
	0x50, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x09, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x69, 0x73, 0x6b,
	0x50, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x54, 0x50, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x0a, 0x42, 0x66, 0x5a, 0x64, 0x63,
	0x6e, 0x62, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65,
	0x5f, 0x63, 0x6f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73,
	0x6b, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x67, 0x65, 0x6e, 0x2f, 0x6d,
	0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescOnce sync.Once
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescData = file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDesc
)

func file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescGZIP() []byte {
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescOnce.Do(func() {
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescData = protoimpl.X.CompressGZIP(file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescData)
	})
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDescData
}

var file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_goTypes = []interface{}{
	(RiskCheckType)(0),            // 0: moe_risk_comm.RiskCheckType
	(RiskDimension)(0),            // 1: moe_risk_comm.RiskDimension
	(RiskOpType)(0),               // 2: moe_risk_comm.RiskOpType
	(RiskTrigType)(0),             // 3: moe_risk_comm.RiskTrigType
	(PunishType)(0),               // 4: moe_risk_comm.PunishType
	(RiskType)(0),                 // 5: moe_risk_comm.RiskType
	(RiskPunishReason)(0),         // 6: moe_risk_comm.RiskPunishReason
	(*CommReq)(nil),               // 7: moe_risk_comm.CommReq
	(*RiskSceneInfo)(nil),         // 8: moe_risk_comm.RiskSceneInfo
	(*RiskUserInfo)(nil),          // 9: moe_risk_comm.RiskUserInfo
	(*RiskOpInfo)(nil),            // 10: moe_risk_comm.RiskOpInfo
	(*RiskAssetInfo)(nil),         // 11: moe_risk_comm.RiskAssetInfo
	(*RiskSysInfo)(nil),           // 12: moe_risk_comm.RiskSysInfo
	(*RiskEvent)(nil),             // 13: moe_risk_comm.RiskEvent
	(*PunishDetail)(nil),          // 14: moe_risk_comm.PunishDetail
	(*RiskCheckResult)(nil),       // 15: moe_risk_comm.RiskCheckResult
	(*RiskCountItem)(nil),         // 16: moe_risk_comm.RiskCountItem
	(*RiskTimeCount)(nil),         // 17: moe_risk_comm.RiskTimeCount
	(*RiskStat)(nil),              // 18: moe_risk_comm.RiskStat
	(*AssetCheckInfo)(nil),        // 19: moe_risk_comm.AssetCheckInfo
	(*RiskCheckReqExtraInfo)(nil), // 20: moe_risk_comm.RiskCheckReqExtraInfo
	(*CommSafetyInfo)(nil),        // 21: moe_risk_comm.CommSafetyInfo
	(*CustomSafetyInfo)(nil),      // 22: moe_risk_comm.CustomSafetyInfo
	(*RiskCheckSafetyInfo)(nil),   // 23: moe_risk_comm.RiskCheckSafetyInfo
	(*RiskContext)(nil),           // 24: moe_risk_comm.RiskContext
	nil,                           // 25: moe_risk_comm.RiskSysInfo.SysExtEntry
	nil,                           // 26: moe_risk_comm.RiskEvent.MapExtEntry
	nil,                           // 27: moe_risk_comm.RiskCountItem.CntEntry
	nil,                           // 28: moe_risk_comm.RiskCountItem.ValueEntry
	nil,                           // 29: moe_risk_comm.CommSafetyInfo.ExtMapEntry
	nil,                           // 30: moe_risk_comm.CustomSafetyInfo.ExtMapEntry
}
var file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_depIdxs = []int32{
	25, // 0: moe_risk_comm.RiskSysInfo.sysExt:type_name -> moe_risk_comm.RiskSysInfo.SysExtEntry
	8,  // 1: moe_risk_comm.RiskEvent.sceneInfo:type_name -> moe_risk_comm.RiskSceneInfo
	9,  // 2: moe_risk_comm.RiskEvent.userInfo:type_name -> moe_risk_comm.RiskUserInfo
	10, // 3: moe_risk_comm.RiskEvent.opInfo:type_name -> moe_risk_comm.RiskOpInfo
	11, // 4: moe_risk_comm.RiskEvent.assetInfo:type_name -> moe_risk_comm.RiskAssetInfo
	12, // 5: moe_risk_comm.RiskEvent.sysInfo:type_name -> moe_risk_comm.RiskSysInfo
	26, // 6: moe_risk_comm.RiskEvent.mapExt:type_name -> moe_risk_comm.RiskEvent.MapExtEntry
	14, // 7: moe_risk_comm.RiskCheckResult.punishDetail:type_name -> moe_risk_comm.PunishDetail
	27, // 8: moe_risk_comm.RiskCountItem.cnt:type_name -> moe_risk_comm.RiskCountItem.CntEntry
	28, // 9: moe_risk_comm.RiskCountItem.value:type_name -> moe_risk_comm.RiskCountItem.ValueEntry
	16, // 10: moe_risk_comm.RiskTimeCount.minCount:type_name -> moe_risk_comm.RiskCountItem
	16, // 11: moe_risk_comm.RiskTimeCount.hourCount:type_name -> moe_risk_comm.RiskCountItem
	16, // 12: moe_risk_comm.RiskTimeCount.dayCount:type_name -> moe_risk_comm.RiskCountItem
	16, // 13: moe_risk_comm.RiskTimeCount.weekCount:type_name -> moe_risk_comm.RiskCountItem
	16, // 14: moe_risk_comm.RiskTimeCount.monthCount:type_name -> moe_risk_comm.RiskCountItem
	17, // 15: moe_risk_comm.RiskStat.inStat:type_name -> moe_risk_comm.RiskTimeCount
	17, // 16: moe_risk_comm.RiskStat.outStat:type_name -> moe_risk_comm.RiskTimeCount
	29, // 17: moe_risk_comm.CommSafetyInfo.extMap:type_name -> moe_risk_comm.CommSafetyInfo.ExtMapEntry
	30, // 18: moe_risk_comm.CustomSafetyInfo.extMap:type_name -> moe_risk_comm.CustomSafetyInfo.ExtMapEntry
	21, // 19: moe_risk_comm.RiskCheckSafetyInfo.commSafe:type_name -> moe_risk_comm.CommSafetyInfo
	22, // 20: moe_risk_comm.RiskCheckSafetyInfo.customSafe:type_name -> moe_risk_comm.CustomSafetyInfo
	8,  // 21: moe_risk_comm.RiskContext.sceneInfo:type_name -> moe_risk_comm.RiskSceneInfo
	10, // 22: moe_risk_comm.RiskContext.opInfo:type_name -> moe_risk_comm.RiskOpInfo
	23, // [23:23] is the sub-list for method output_type
	23, // [23:23] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_init() }
func file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_init() {
	if File_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskSceneInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskUserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskOpInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskAssetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskSysInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PunishDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskCheckResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskCountItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskTimeCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskStat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetCheckInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskCheckReqExtraInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommSafetyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomSafetyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskCheckSafetyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskContext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_goTypes,
		DependencyIndexes: file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_depIdxs,
		EnumInfos:         file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_enumTypes,
		MessageInfos:      file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_msgTypes,
	}.Build()
	File_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto = out.File
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_rawDesc = nil
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_goTypes = nil
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_comm_proto_depIdxs = nil
}
