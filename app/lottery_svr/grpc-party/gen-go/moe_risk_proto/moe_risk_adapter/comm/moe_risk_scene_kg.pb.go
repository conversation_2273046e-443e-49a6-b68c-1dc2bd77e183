// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: moe_risk_proto/moe_risk_adapter/comm/moe_risk_scene_kg.proto

package comm

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 资产类型
type EmKGAssetType int32

const (
	EmKGAssetType_EmKGAssetType_Unknown      EmKGAssetType = 0 // 无效类型
	EmKGAssetType_EmKGAssetType_Coin         EmKGAssetType = 1 // KG金币
	EmKGAssetType_EmKGAssetType_Welfare      EmKGAssetType = 2 // KG福利
	EmKGAssetType_EmKGAssetType_Commodity    EmKGAssetType = 3 // KG商品
	EmKGAssetType_EmKGAssetType_Cash         EmKGAssetType = 4 // KG现金
	EmKGAssetType_EmKGAssetType_GameCurrency EmKGAssetType = 5 // KG通用游戏币
)

// Enum value maps for EmKGAssetType.
var (
	EmKGAssetType_name = map[int32]string{
		0: "EmKGAssetType_Unknown",
		1: "EmKGAssetType_Coin",
		2: "EmKGAssetType_Welfare",
		3: "EmKGAssetType_Commodity",
		4: "EmKGAssetType_Cash",
		5: "EmKGAssetType_GameCurrency",
	}
	EmKGAssetType_value = map[string]int32{
		"EmKGAssetType_Unknown":      0,
		"EmKGAssetType_Coin":         1,
		"EmKGAssetType_Welfare":      2,
		"EmKGAssetType_Commodity":    3,
		"EmKGAssetType_Cash":         4,
		"EmKGAssetType_GameCurrency": 5,
	}
)

func (x EmKGAssetType) Enum() *EmKGAssetType {
	p := new(EmKGAssetType)
	*p = x
	return p
}

func (x EmKGAssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmKGAssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_enumTypes[0].Descriptor()
}

func (EmKGAssetType) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_enumTypes[0]
}

func (x EmKGAssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmKGAssetType.Descriptor instead.
func (EmKGAssetType) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDescGZIP(), []int{0}
}

// 任务
type EmKGRiskSubSceneTask int32

const (
	EmKGRiskSubSceneTask_EmKGRiskSubScene_Task_Unknown EmKGRiskSubSceneTask = 0 // 无效类型
	// 注意!!! 业务自定义子场景从1001+开始
	EmKGRiskSubSceneTask_EmKGRiskSubScene_Task_Eat               EmKGRiskSubSceneTask = 1001 // 吃饭任务
	EmKGRiskSubSceneTask_EmKGRiskSubScene_Task_Sleep             EmKGRiskSubSceneTask = 1002 // 睡觉任务
	EmKGRiskSubSceneTask_EmKGRiskSubScene_Task_TreasureBox       EmKGRiskSubSceneTask = 1003 // 宝箱任务
	EmKGRiskSubSceneTask_EmKGRiskSubScene_Task_ExtClickIncentive EmKGRiskSubSceneTask = 1004 // 广告二次点击激励
	EmKGRiskSubSceneTask_EmKGRiskSubScene_Task_KgToDj            EmKGRiskSubSceneTask = 1005 // K歌导流短剧任务
)

// Enum value maps for EmKGRiskSubSceneTask.
var (
	EmKGRiskSubSceneTask_name = map[int32]string{
		0:    "EmKGRiskSubScene_Task_Unknown",
		1001: "EmKGRiskSubScene_Task_Eat",
		1002: "EmKGRiskSubScene_Task_Sleep",
		1003: "EmKGRiskSubScene_Task_TreasureBox",
		1004: "EmKGRiskSubScene_Task_ExtClickIncentive",
		1005: "EmKGRiskSubScene_Task_KgToDj",
	}
	EmKGRiskSubSceneTask_value = map[string]int32{
		"EmKGRiskSubScene_Task_Unknown":           0,
		"EmKGRiskSubScene_Task_Eat":               1001,
		"EmKGRiskSubScene_Task_Sleep":             1002,
		"EmKGRiskSubScene_Task_TreasureBox":       1003,
		"EmKGRiskSubScene_Task_ExtClickIncentive": 1004,
		"EmKGRiskSubScene_Task_KgToDj":            1005,
	}
)

func (x EmKGRiskSubSceneTask) Enum() *EmKGRiskSubSceneTask {
	p := new(EmKGRiskSubSceneTask)
	*p = x
	return p
}

func (x EmKGRiskSubSceneTask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmKGRiskSubSceneTask) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_enumTypes[1].Descriptor()
}

func (EmKGRiskSubSceneTask) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_enumTypes[1]
}

func (x EmKGRiskSubSceneTask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmKGRiskSubSceneTask.Descriptor instead.
func (EmKGRiskSubSceneTask) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDescGZIP(), []int{1}
}

// 兑换
type EmKGRiskSubSceneExchange int32

const (
	EmKGRiskSubSceneExchange_EmKGRiskSubScene_Exchange_Unknown EmKGRiskSubSceneExchange = 0 // 无效类型
	// 注意!!! 业务自定义子场景从1001+开始
	EmKGRiskSubSceneExchange_EmKGRiskSubScene_Exchange_Vip_Coin_Deduction EmKGRiskSubSceneExchange = 1001 //金币兑换VIP开通首月优惠
)

// Enum value maps for EmKGRiskSubSceneExchange.
var (
	EmKGRiskSubSceneExchange_name = map[int32]string{
		0:    "EmKGRiskSubScene_Exchange_Unknown",
		1001: "EmKGRiskSubScene_Exchange_Vip_Coin_Deduction",
	}
	EmKGRiskSubSceneExchange_value = map[string]int32{
		"EmKGRiskSubScene_Exchange_Unknown":            0,
		"EmKGRiskSubScene_Exchange_Vip_Coin_Deduction": 1001,
	}
)

func (x EmKGRiskSubSceneExchange) Enum() *EmKGRiskSubSceneExchange {
	p := new(EmKGRiskSubSceneExchange)
	*p = x
	return p
}

func (x EmKGRiskSubSceneExchange) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmKGRiskSubSceneExchange) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_enumTypes[2].Descriptor()
}

func (EmKGRiskSubSceneExchange) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_enumTypes[2]
}

func (x EmKGRiskSubSceneExchange) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmKGRiskSubSceneExchange.Descriptor instead.
func (EmKGRiskSubSceneExchange) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDescGZIP(), []int{2}
}

// 玩法
type EmKGRiskSubSceneGame int32

const (
	EmKGRiskSubSceneGame_EmKGRiskSubScene_Game_Unknown EmKGRiskSubSceneGame = 0 // 无效类型
	// 注意!!! 业务自定义子场景从1001+开始
	EmKGRiskSubSceneGame_EmKGRiskSubScene_Game_LuckyDraw          EmKGRiskSubSceneGame = 1001 // 抽奖
	EmKGRiskSubSceneGame_EmKGRiskSubScene_Game_TreasureHunt       EmKGRiskSubSceneGame = 1002 // 夺宝
	EmKGRiskSubSceneGame_EmKGRiskSubScene_Game_MultiTimesFun      EmKGRiskSubSceneGame = 1003 // 倍倍乐
	EmKGRiskSubSceneGame_EmKGRiskSubScene_Game_DailySignIn        EmKGRiskSubSceneGame = 1004 // 天天签到
	EmKGRiskSubSceneGame_EmKGRiskSubScene_Game_LuckyWheel         EmKGRiskSubSceneGame = 1005 // 幸运转盘
	EmKGRiskSubSceneGame_EmKGRiskSubScene_Game_AdGlobalBanner     EmKGRiskSubSceneGame = 1006 // 商广全局激励banner
	EmKGRiskSubSceneGame_EmKGRiskSubScene_Game_AdSurpriseBlindBox EmKGRiskSubSceneGame = 1007 // 商广惊喜好礼视频（盲盒）
	EmKGRiskSubSceneGame_EmKGRiskSubScene_Game_MusicSquare        EmKGRiskSubSceneGame = 1008 // 音乐广场
	EmKGRiskSubSceneGame_EmKGRiskSubScene_Game_YingCaiShen        EmKGRiskSubSceneGame = 1009 // 迎财神(拉霸)
)

// Enum value maps for EmKGRiskSubSceneGame.
var (
	EmKGRiskSubSceneGame_name = map[int32]string{
		0:    "EmKGRiskSubScene_Game_Unknown",
		1001: "EmKGRiskSubScene_Game_LuckyDraw",
		1002: "EmKGRiskSubScene_Game_TreasureHunt",
		1003: "EmKGRiskSubScene_Game_MultiTimesFun",
		1004: "EmKGRiskSubScene_Game_DailySignIn",
		1005: "EmKGRiskSubScene_Game_LuckyWheel",
		1006: "EmKGRiskSubScene_Game_AdGlobalBanner",
		1007: "EmKGRiskSubScene_Game_AdSurpriseBlindBox",
		1008: "EmKGRiskSubScene_Game_MusicSquare",
		1009: "EmKGRiskSubScene_Game_YingCaiShen",
	}
	EmKGRiskSubSceneGame_value = map[string]int32{
		"EmKGRiskSubScene_Game_Unknown":            0,
		"EmKGRiskSubScene_Game_LuckyDraw":          1001,
		"EmKGRiskSubScene_Game_TreasureHunt":       1002,
		"EmKGRiskSubScene_Game_MultiTimesFun":      1003,
		"EmKGRiskSubScene_Game_DailySignIn":        1004,
		"EmKGRiskSubScene_Game_LuckyWheel":         1005,
		"EmKGRiskSubScene_Game_AdGlobalBanner":     1006,
		"EmKGRiskSubScene_Game_AdSurpriseBlindBox": 1007,
		"EmKGRiskSubScene_Game_MusicSquare":        1008,
		"EmKGRiskSubScene_Game_YingCaiShen":        1009,
	}
)

func (x EmKGRiskSubSceneGame) Enum() *EmKGRiskSubSceneGame {
	p := new(EmKGRiskSubSceneGame)
	*p = x
	return p
}

func (x EmKGRiskSubSceneGame) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmKGRiskSubSceneGame) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_enumTypes[3].Descriptor()
}

func (EmKGRiskSubSceneGame) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_enumTypes[3]
}

func (x EmKGRiskSubSceneGame) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmKGRiskSubSceneGame.Descriptor instead.
func (EmKGRiskSubSceneGame) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDescGZIP(), []int{3}
}

// 活动
type EmKGRiskSubSceneActivity int32

const (
	EmKGRiskSubSceneActivity_EmKGRiskSubScene_Activity_Unknown EmKGRiskSubSceneActivity = 0 // 无效类型
	// 注意!!! 业务自定义子场景从1001+开始
	EmKGRiskSubSceneActivity_EmKGRiskSubScene_Activity_FriendInvite EmKGRiskSubSceneActivity = 1001 // 邀请好友
)

// Enum value maps for EmKGRiskSubSceneActivity.
var (
	EmKGRiskSubSceneActivity_name = map[int32]string{
		0:    "EmKGRiskSubScene_Activity_Unknown",
		1001: "EmKGRiskSubScene_Activity_FriendInvite",
	}
	EmKGRiskSubSceneActivity_value = map[string]int32{
		"EmKGRiskSubScene_Activity_Unknown":      0,
		"EmKGRiskSubScene_Activity_FriendInvite": 1001,
	}
)

func (x EmKGRiskSubSceneActivity) Enum() *EmKGRiskSubSceneActivity {
	p := new(EmKGRiskSubSceneActivity)
	*p = x
	return p
}

func (x EmKGRiskSubSceneActivity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmKGRiskSubSceneActivity) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_enumTypes[4].Descriptor()
}

func (EmKGRiskSubSceneActivity) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_enumTypes[4]
}

func (x EmKGRiskSubSceneActivity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmKGRiskSubSceneActivity.Descriptor instead.
func (EmKGRiskSubSceneActivity) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDescGZIP(), []int{4}
}

var File_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto protoreflect.FileDescriptor

var file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65,
	0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x6b, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11,
	0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x6b,
	0x67, 0x2a, 0xb2, 0x01, 0x0a, 0x0d, 0x45, 0x6d, 0x4b, 0x47, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x6d, 0x4b, 0x47, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x16,
	0x0a, 0x12, 0x45, 0x6d, 0x4b, 0x47, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x43, 0x6f, 0x69, 0x6e, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x6d, 0x4b, 0x47, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x57, 0x65, 0x6c, 0x66, 0x61, 0x72, 0x65, 0x10,
	0x02, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x6d, 0x4b, 0x47, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x10, 0x03, 0x12, 0x16,
	0x0a, 0x12, 0x45, 0x6d, 0x4b, 0x47, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x43, 0x61, 0x73, 0x68, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x6d, 0x4b, 0x47, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x10, 0x05, 0x2a, 0xf4, 0x01, 0x0a, 0x14, 0x45, 0x6d, 0x4b, 0x47, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12,
	0x21, 0x0a, 0x1d, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63,
	0x65, 0x6e, 0x65, 0x5f, 0x54, 0x61, 0x73, 0x6b, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x10, 0x00, 0x12, 0x1e, 0x0a, 0x19, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75,
	0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x54, 0x61, 0x73, 0x6b, 0x5f, 0x45, 0x61, 0x74, 0x10,
	0xe9, 0x07, 0x12, 0x20, 0x0a, 0x1b, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75,
	0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x54, 0x61, 0x73, 0x6b, 0x5f, 0x53, 0x6c, 0x65, 0x65,
	0x70, 0x10, 0xea, 0x07, 0x12, 0x26, 0x0a, 0x21, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x54, 0x61, 0x73, 0x6b, 0x5f, 0x54, 0x72,
	0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x42, 0x6f, 0x78, 0x10, 0xeb, 0x07, 0x12, 0x2c, 0x0a, 0x27,
	0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65,
	0x5f, 0x54, 0x61, 0x73, 0x6b, 0x5f, 0x45, 0x78, 0x74, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x49, 0x6e,
	0x63, 0x65, 0x6e, 0x74, 0x69, 0x76, 0x65, 0x10, 0xec, 0x07, 0x12, 0x21, 0x0a, 0x1c, 0x45, 0x6d,
	0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x54,
	0x61, 0x73, 0x6b, 0x5f, 0x4b, 0x67, 0x54, 0x6f, 0x44, 0x6a, 0x10, 0xed, 0x07, 0x2a, 0x74, 0x0a,
	0x18, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e,
	0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x45, 0x6d, 0x4b,
	0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x12, 0x31, 0x0a, 0x2c, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53,
	0x63, 0x65, 0x6e, 0x65, 0x5f, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x56, 0x69,
	0x70, 0x5f, 0x43, 0x6f, 0x69, 0x6e, 0x5f, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x10, 0xe9, 0x07, 0x2a, 0xab, 0x03, 0x0a, 0x14, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x1d,
	0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65,
	0x5f, 0x47, 0x61, 0x6d, 0x65, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12,
	0x24, 0x0a, 0x1f, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63,
	0x65, 0x6e, 0x65, 0x5f, 0x47, 0x61, 0x6d, 0x65, 0x5f, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x44, 0x72,
	0x61, 0x77, 0x10, 0xe9, 0x07, 0x12, 0x27, 0x0a, 0x22, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73,
	0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x47, 0x61, 0x6d, 0x65, 0x5f, 0x54,
	0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x48, 0x75, 0x6e, 0x74, 0x10, 0xea, 0x07, 0x12, 0x28,
	0x0a, 0x23, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65,
	0x6e, 0x65, 0x5f, 0x47, 0x61, 0x6d, 0x65, 0x5f, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x46, 0x75, 0x6e, 0x10, 0xeb, 0x07, 0x12, 0x26, 0x0a, 0x21, 0x45, 0x6d, 0x4b, 0x47,
	0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x47, 0x61, 0x6d,
	0x65, 0x5f, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x10, 0xec, 0x07,
	0x12, 0x25, 0x0a, 0x20, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53,
	0x63, 0x65, 0x6e, 0x65, 0x5f, 0x47, 0x61, 0x6d, 0x65, 0x5f, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x57,
	0x68, 0x65, 0x65, 0x6c, 0x10, 0xed, 0x07, 0x12, 0x29, 0x0a, 0x24, 0x45, 0x6d, 0x4b, 0x47, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x47, 0x61, 0x6d, 0x65,
	0x5f, 0x41, 0x64, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x10,
	0xee, 0x07, 0x12, 0x2d, 0x0a, 0x28, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75,
	0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x47, 0x61, 0x6d, 0x65, 0x5f, 0x41, 0x64, 0x53, 0x75,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x42, 0x6c, 0x69, 0x6e, 0x64, 0x42, 0x6f, 0x78, 0x10, 0xef,
	0x07, 0x12, 0x26, 0x0a, 0x21, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x47, 0x61, 0x6d, 0x65, 0x5f, 0x4d, 0x75, 0x73, 0x69, 0x63,
	0x53, 0x71, 0x75, 0x61, 0x72, 0x65, 0x10, 0xf0, 0x07, 0x12, 0x26, 0x0a, 0x21, 0x45, 0x6d, 0x4b,
	0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x47, 0x61,
	0x6d, 0x65, 0x5f, 0x59, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x69, 0x53, 0x68, 0x65, 0x6e, 0x10, 0xf1,
	0x07, 0x2a, 0x6e, 0x0a, 0x18, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x25, 0x0a,
	0x21, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e,
	0x65, 0x5f, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x10, 0x00, 0x12, 0x2b, 0x0a, 0x26, 0x45, 0x6d, 0x4b, 0x47, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x5f, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x10, 0xe9,
	0x07, 0x42, 0x66, 0x5a, 0x64, 0x63, 0x6e, 0x62, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2f, 0x6d,
	0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x67, 0x65, 0x6e, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x61, 0x64, 0x61,
	0x70, 0x74, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDescOnce sync.Once
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDescData = file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDesc
)

func file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDescGZIP() []byte {
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDescOnce.Do(func() {
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDescData = protoimpl.X.CompressGZIP(file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDescData)
	})
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDescData
}

var file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_goTypes = []interface{}{
	(EmKGAssetType)(0),            // 0: moe_risk_scene_kg.EmKGAssetType
	(EmKGRiskSubSceneTask)(0),     // 1: moe_risk_scene_kg.EmKGRiskSubSceneTask
	(EmKGRiskSubSceneExchange)(0), // 2: moe_risk_scene_kg.EmKGRiskSubSceneExchange
	(EmKGRiskSubSceneGame)(0),     // 3: moe_risk_scene_kg.EmKGRiskSubSceneGame
	(EmKGRiskSubSceneActivity)(0), // 4: moe_risk_scene_kg.EmKGRiskSubSceneActivity
}
var file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_init() }
func file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_init() {
	if File_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_goTypes,
		DependencyIndexes: file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_depIdxs,
		EnumInfos:         file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_enumTypes,
	}.Build()
	File_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto = out.File
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_rawDesc = nil
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_goTypes = nil
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_kg_proto_depIdxs = nil
}
