// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: moe_risk_proto/moe_risk_adapter/comm/moe_risk_scene_comm.proto

package comm

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// -------------------------------------------------(公共)风控主场景---------------------------------------------------
type EmRiskScene int32

const (
	EmRiskScene_EmRiskScene_Unknown EmRiskScene = 0 // 无效类型
	EmRiskScene_EmRiskScene_Task    EmRiskScene = 1 // 任务
	// EmRiskScene_Advert = 2;             // 广告
	EmRiskScene_EmRiskScene_Exchange EmRiskScene = 3   // 兑换
	EmRiskScene_EmRiskScene_Cashout  EmRiskScene = 4   // 提现
	EmRiskScene_EmRiskScene_Activity EmRiskScene = 5   // 活动
	EmRiskScene_EmRiskScene_Game     EmRiskScene = 6   // 玩法
	EmRiskScene_EmRiskScene_System   EmRiskScene = 7   // 系统触发
	EmRiskScene_EmRiskScene_Custom   EmRiskScene = 100 // 自定义(放一些业务自定义的统计)
)

// Enum value maps for EmRiskScene.
var (
	EmRiskScene_name = map[int32]string{
		0:   "EmRiskScene_Unknown",
		1:   "EmRiskScene_Task",
		3:   "EmRiskScene_Exchange",
		4:   "EmRiskScene_Cashout",
		5:   "EmRiskScene_Activity",
		6:   "EmRiskScene_Game",
		7:   "EmRiskScene_System",
		100: "EmRiskScene_Custom",
	}
	EmRiskScene_value = map[string]int32{
		"EmRiskScene_Unknown":  0,
		"EmRiskScene_Task":     1,
		"EmRiskScene_Exchange": 3,
		"EmRiskScene_Cashout":  4,
		"EmRiskScene_Activity": 5,
		"EmRiskScene_Game":     6,
		"EmRiskScene_System":   7,
		"EmRiskScene_Custom":   100,
	}
)

func (x EmRiskScene) Enum() *EmRiskScene {
	p := new(EmRiskScene)
	*p = x
	return p
}

func (x EmRiskScene) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmRiskScene) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[0].Descriptor()
}

func (EmRiskScene) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[0]
}

func (x EmRiskScene) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmRiskScene.Descriptor instead.
func (EmRiskScene) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescGZIP(), []int{0}
}

// -------------------------------------------------(公共)风控子场景---------------------------------------------------
// 任务
type EmRiskSubSceneTask int32

const (
	EmRiskSubSceneTask_EmRiskSubScene_Task_Unknown EmRiskSubSceneTask = 0 // 无效类型
	// 注意: 公共子场景号段1～1000; 业务自定义子场景从1001+开始
	EmRiskSubSceneTask_EmRiskSubScene_Task_Common EmRiskSubSceneTask = 1 // 通用任务玩法(eg.任务中台直接配置出来的任务)
)

// Enum value maps for EmRiskSubSceneTask.
var (
	EmRiskSubSceneTask_name = map[int32]string{
		0: "EmRiskSubScene_Task_Unknown",
		1: "EmRiskSubScene_Task_Common",
	}
	EmRiskSubSceneTask_value = map[string]int32{
		"EmRiskSubScene_Task_Unknown": 0,
		"EmRiskSubScene_Task_Common":  1,
	}
)

func (x EmRiskSubSceneTask) Enum() *EmRiskSubSceneTask {
	p := new(EmRiskSubSceneTask)
	*p = x
	return p
}

func (x EmRiskSubSceneTask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmRiskSubSceneTask) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[1].Descriptor()
}

func (EmRiskSubSceneTask) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[1]
}

func (x EmRiskSubSceneTask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmRiskSubSceneTask.Descriptor instead.
func (EmRiskSubSceneTask) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescGZIP(), []int{1}
}

// 广告
type EmRiskSubSceneAdvert int32

const (
	EmRiskSubSceneAdvert_EmRiskSubScene_Advert_Unknown EmRiskSubSceneAdvert = 0 // 无效类型
	// 注意: 公共子场景1～1000; 业务自定义子场景从1001+开始
	EmRiskSubSceneAdvert_EmRiskSubScene_Advert_TaskComplete EmRiskSubSceneAdvert = 1 // 通用任务领奖激励广告
	EmRiskSubSceneAdvert_EmRiskSubScene_Advert_Game         EmRiskSubSceneAdvert = 2 // 网赚玩法内的激励广告， 吃饭、睡觉
	EmRiskSubSceneAdvert_EmRiskSubScene_Advert_Feed         EmRiskSubSceneAdvert = 3 // feed信息流广告
)

// Enum value maps for EmRiskSubSceneAdvert.
var (
	EmRiskSubSceneAdvert_name = map[int32]string{
		0: "EmRiskSubScene_Advert_Unknown",
		1: "EmRiskSubScene_Advert_TaskComplete",
		2: "EmRiskSubScene_Advert_Game",
		3: "EmRiskSubScene_Advert_Feed",
	}
	EmRiskSubSceneAdvert_value = map[string]int32{
		"EmRiskSubScene_Advert_Unknown":      0,
		"EmRiskSubScene_Advert_TaskComplete": 1,
		"EmRiskSubScene_Advert_Game":         2,
		"EmRiskSubScene_Advert_Feed":         3,
	}
)

func (x EmRiskSubSceneAdvert) Enum() *EmRiskSubSceneAdvert {
	p := new(EmRiskSubSceneAdvert)
	*p = x
	return p
}

func (x EmRiskSubSceneAdvert) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmRiskSubSceneAdvert) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[2].Descriptor()
}

func (EmRiskSubSceneAdvert) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[2]
}

func (x EmRiskSubSceneAdvert) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmRiskSubSceneAdvert.Descriptor instead.
func (EmRiskSubSceneAdvert) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescGZIP(), []int{2}
}

// 兑换
type EmRiskSubSceneExchange int32

const (
	EmRiskSubSceneExchange_EmRiskSubScene_Exchange_Unknown EmRiskSubSceneExchange = 0 // 无效类型
	// 注意: 公共子场景1～1000; 业务自定义子场景从1001+开始
	EmRiskSubSceneExchange_EmRiskSubScene_Exchange_ExchangeCommodity EmRiskSubSceneExchange = 1 // 兑换商品
)

// Enum value maps for EmRiskSubSceneExchange.
var (
	EmRiskSubSceneExchange_name = map[int32]string{
		0: "EmRiskSubScene_Exchange_Unknown",
		1: "EmRiskSubScene_Exchange_ExchangeCommodity",
	}
	EmRiskSubSceneExchange_value = map[string]int32{
		"EmRiskSubScene_Exchange_Unknown":           0,
		"EmRiskSubScene_Exchange_ExchangeCommodity": 1,
	}
)

func (x EmRiskSubSceneExchange) Enum() *EmRiskSubSceneExchange {
	p := new(EmRiskSubSceneExchange)
	*p = x
	return p
}

func (x EmRiskSubSceneExchange) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmRiskSubSceneExchange) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[3].Descriptor()
}

func (EmRiskSubSceneExchange) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[3]
}

func (x EmRiskSubSceneExchange) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmRiskSubSceneExchange.Descriptor instead.
func (EmRiskSubSceneExchange) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescGZIP(), []int{3}
}

// 提现
type EmRiskSubSceneCashout int32

const (
	EmRiskSubSceneCashout_EmRiskSubScene_Cashout_Unknown EmRiskSubSceneCashout = 0 // 提现到微信
	// 注意: 公共子场景1～1000; 业务自定义子场景从1001+开始
	EmRiskSubSceneCashout_EmRiskSubScene_Cashout_WX EmRiskSubSceneCashout = 1 // 提现到微信
)

// Enum value maps for EmRiskSubSceneCashout.
var (
	EmRiskSubSceneCashout_name = map[int32]string{
		0: "EmRiskSubScene_Cashout_Unknown",
		1: "EmRiskSubScene_Cashout_WX",
	}
	EmRiskSubSceneCashout_value = map[string]int32{
		"EmRiskSubScene_Cashout_Unknown": 0,
		"EmRiskSubScene_Cashout_WX":      1,
	}
)

func (x EmRiskSubSceneCashout) Enum() *EmRiskSubSceneCashout {
	p := new(EmRiskSubSceneCashout)
	*p = x
	return p
}

func (x EmRiskSubSceneCashout) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmRiskSubSceneCashout) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[4].Descriptor()
}

func (EmRiskSubSceneCashout) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[4]
}

func (x EmRiskSubSceneCashout) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmRiskSubSceneCashout.Descriptor instead.
func (EmRiskSubSceneCashout) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescGZIP(), []int{4}
}

// 运营活动
type EmRiskSubSceneActivity int32

const (
	// 注意: 公共子场景1～1000; 业务自定义子场景从1001+开始
	EmRiskSubSceneActivity_EmRiskSubScene_Activity_Unknown EmRiskSubSceneActivity = 0 // 占位
)

// Enum value maps for EmRiskSubSceneActivity.
var (
	EmRiskSubSceneActivity_name = map[int32]string{
		0: "EmRiskSubScene_Activity_Unknown",
	}
	EmRiskSubSceneActivity_value = map[string]int32{
		"EmRiskSubScene_Activity_Unknown": 0,
	}
)

func (x EmRiskSubSceneActivity) Enum() *EmRiskSubSceneActivity {
	p := new(EmRiskSubSceneActivity)
	*p = x
	return p
}

func (x EmRiskSubSceneActivity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmRiskSubSceneActivity) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[5].Descriptor()
}

func (EmRiskSubSceneActivity) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[5]
}

func (x EmRiskSubSceneActivity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmRiskSubSceneActivity.Descriptor instead.
func (EmRiskSubSceneActivity) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescGZIP(), []int{5}
}

// 玩法
type EmRiskSubSceneGame int32

const (
	// 注意: 公共子场景1～1000; 业务自定义子场景从1001+开始
	EmRiskSubSceneGame_EmRiskSubScene_Game_Unknown EmRiskSubSceneGame = 0 // 占位
)

// Enum value maps for EmRiskSubSceneGame.
var (
	EmRiskSubSceneGame_name = map[int32]string{
		0: "EmRiskSubScene_Game_Unknown",
	}
	EmRiskSubSceneGame_value = map[string]int32{
		"EmRiskSubScene_Game_Unknown": 0,
	}
)

func (x EmRiskSubSceneGame) Enum() *EmRiskSubSceneGame {
	p := new(EmRiskSubSceneGame)
	*p = x
	return p
}

func (x EmRiskSubSceneGame) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmRiskSubSceneGame) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[6].Descriptor()
}

func (EmRiskSubSceneGame) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[6]
}

func (x EmRiskSubSceneGame) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmRiskSubSceneGame.Descriptor instead.
func (EmRiskSubSceneGame) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescGZIP(), []int{6}
}

// 系统触发
type EmRiskSubSceneSystem int32

const (
	EmRiskSubSceneSystem_EmRiskSubScene_System_Unknown EmRiskSubSceneSystem = 0 // 无效类型
	EmRiskSubSceneSystem_EmRiskSubScene_System_Expire  EmRiskSubSceneSystem = 1 // 系统触发金币过期清理
	EmRiskSubSceneSystem_EmRiskSubScene_System_Clean   EmRiskSubSceneSystem = 2 // 系统触发清空金币
)

// Enum value maps for EmRiskSubSceneSystem.
var (
	EmRiskSubSceneSystem_name = map[int32]string{
		0: "EmRiskSubScene_System_Unknown",
		1: "EmRiskSubScene_System_Expire",
		2: "EmRiskSubScene_System_Clean",
	}
	EmRiskSubSceneSystem_value = map[string]int32{
		"EmRiskSubScene_System_Unknown": 0,
		"EmRiskSubScene_System_Expire":  1,
		"EmRiskSubScene_System_Clean":   2,
	}
)

func (x EmRiskSubSceneSystem) Enum() *EmRiskSubSceneSystem {
	p := new(EmRiskSubSceneSystem)
	*p = x
	return p
}

func (x EmRiskSubSceneSystem) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmRiskSubSceneSystem) Descriptor() protoreflect.EnumDescriptor {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[7].Descriptor()
}

func (EmRiskSubSceneSystem) Type() protoreflect.EnumType {
	return &file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes[7]
}

func (x EmRiskSubSceneSystem) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmRiskSubSceneSystem.Descriptor instead.
func (EmRiskSubSceneSystem) EnumDescriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescGZIP(), []int{7}
}

var File_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto protoreflect.FileDescriptor

var file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65,
	0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x2a, 0xcf, 0x01, 0x0a, 0x0b, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x65, 0x6e, 0x65,
	0x12, 0x17, 0x0a, 0x13, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x6d, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x54, 0x61, 0x73, 0x6b, 0x10, 0x01, 0x12,
	0x18, 0x0a, 0x14, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x6d, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x43, 0x61, 0x73, 0x68, 0x6f, 0x75, 0x74,
	0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x65, 0x6e,
	0x65, 0x5f, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10,
	0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x47, 0x61, 0x6d, 0x65,
	0x10, 0x06, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x65, 0x6e,
	0x65, 0x5f, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x07, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x6d,
	0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x10, 0x64, 0x2a, 0x55, 0x0a, 0x12, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53,
	0x63, 0x65, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x6d, 0x52, 0x69,
	0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x54, 0x61, 0x73, 0x6b, 0x5f,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x6d, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x54, 0x61, 0x73, 0x6b,
	0x5f, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x10, 0x01, 0x2a, 0xa1, 0x01, 0x0a, 0x14, 0x45, 0x6d,
	0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x41, 0x64, 0x76, 0x65,
	0x72, 0x74, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53,
	0x63, 0x65, 0x6e, 0x65, 0x5f, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x55, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53,
	0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x54,
	0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x10, 0x01, 0x12, 0x1e, 0x0a,
	0x1a, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f,
	0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x47, 0x61, 0x6d, 0x65, 0x10, 0x02, 0x12, 0x1e, 0x0a,
	0x1a, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f,
	0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x46, 0x65, 0x65, 0x64, 0x10, 0x03, 0x2a, 0x6c, 0x0a,
	0x16, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x45, 0x6d, 0x52, 0x69, 0x73,
	0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x2d, 0x0a, 0x29,
	0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x10, 0x01, 0x2a, 0x5a, 0x0a, 0x15, 0x45,
	0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x43, 0x61, 0x73,
	0x68, 0x6f, 0x75, 0x74, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75,
	0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x43, 0x61, 0x73, 0x68, 0x6f, 0x75, 0x74, 0x5f, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x6d, 0x52, 0x69,
	0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x43, 0x61, 0x73, 0x68, 0x6f,
	0x75, 0x74, 0x5f, 0x57, 0x58, 0x10, 0x01, 0x2a, 0x3d, 0x0a, 0x16, 0x45, 0x6d, 0x52, 0x69, 0x73,
	0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x12, 0x23, 0x0a, 0x1f, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63,
	0x65, 0x6e, 0x65, 0x5f, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x2a, 0x35, 0x0a, 0x12, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x1b,
	0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x47,
	0x61, 0x6d, 0x65, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x2a, 0x7c, 0x0a,
	0x14, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x53,
	0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x6d, 0x52, 0x69,
	0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x5f, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x6d,
	0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x5f, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x10, 0x02, 0x42, 0x66, 0x5a, 0x64, 0x63,
	0x6e, 0x62, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65,
	0x5f, 0x63, 0x6f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73,
	0x6b, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x67, 0x65, 0x6e, 0x2f, 0x6d,
	0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescOnce sync.Once
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescData = file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDesc
)

func file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescGZIP() []byte {
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescOnce.Do(func() {
		file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescData = protoimpl.X.CompressGZIP(file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescData)
	})
	return file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDescData
}

var file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_goTypes = []interface{}{
	(EmRiskScene)(0),            // 0: moe_risk_scene.EmRiskScene
	(EmRiskSubSceneTask)(0),     // 1: moe_risk_scene.EmRiskSubSceneTask
	(EmRiskSubSceneAdvert)(0),   // 2: moe_risk_scene.EmRiskSubSceneAdvert
	(EmRiskSubSceneExchange)(0), // 3: moe_risk_scene.EmRiskSubSceneExchange
	(EmRiskSubSceneCashout)(0),  // 4: moe_risk_scene.EmRiskSubSceneCashout
	(EmRiskSubSceneActivity)(0), // 5: moe_risk_scene.EmRiskSubSceneActivity
	(EmRiskSubSceneGame)(0),     // 6: moe_risk_scene.EmRiskSubSceneGame
	(EmRiskSubSceneSystem)(0),   // 7: moe_risk_scene.EmRiskSubSceneSystem
}
var file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_init() }
func file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_init() {
	if File_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_goTypes,
		DependencyIndexes: file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_depIdxs,
		EnumInfos:         file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_enumTypes,
	}.Build()
	File_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto = out.File
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_rawDesc = nil
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_goTypes = nil
	file_moe_risk_proto_moe_risk_adapter_comm_moe_risk_scene_comm_proto_depIdxs = nil
}
