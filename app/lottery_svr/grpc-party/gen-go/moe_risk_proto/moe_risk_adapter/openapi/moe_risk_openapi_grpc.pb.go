// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: moe_risk_proto/moe_risk_adapter/openapi/moe_risk_openapi.proto

package openapi

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	OpenGameOpenApi_RiskReport_FullMethodName = "/moe_risk_openapi.OpenGameOpenApi/RiskReport"
	OpenGameOpenApi_RiskCheck_FullMethodName  = "/moe_risk_openapi.OpenGameOpenApi/RiskCheck"
	OpenGameOpenApi_RiskQuery_FullMethodName  = "/moe_risk_openapi.OpenGameOpenApi/RiskQuery"
)

// OpenGameOpenApiClient is the client API for OpenGameOpenApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OpenGameOpenApiClient interface {
	// 风控上报
	RiskReport(ctx context.Context, in *RiskReportReq, opts ...grpc.CallOption) (*RiskReportRsp, error)
	// 统一校验
	RiskCheck(ctx context.Context, in *RiskCheckReq, opts ...grpc.CallOption) (*RiskCheckRsp, error)
	// 风控查询
	RiskQuery(ctx context.Context, in *RiskQueryReq, opts ...grpc.CallOption) (*RiskQueryRsp, error)
}

type openGameOpenApiClient struct {
	cc grpc.ClientConnInterface
}

func NewOpenGameOpenApiClient(cc grpc.ClientConnInterface) OpenGameOpenApiClient {
	return &openGameOpenApiClient{cc}
}

func (c *openGameOpenApiClient) RiskReport(ctx context.Context, in *RiskReportReq, opts ...grpc.CallOption) (*RiskReportRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RiskReportRsp)
	err := c.cc.Invoke(ctx, OpenGameOpenApi_RiskReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *openGameOpenApiClient) RiskCheck(ctx context.Context, in *RiskCheckReq, opts ...grpc.CallOption) (*RiskCheckRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RiskCheckRsp)
	err := c.cc.Invoke(ctx, OpenGameOpenApi_RiskCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *openGameOpenApiClient) RiskQuery(ctx context.Context, in *RiskQueryReq, opts ...grpc.CallOption) (*RiskQueryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RiskQueryRsp)
	err := c.cc.Invoke(ctx, OpenGameOpenApi_RiskQuery_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OpenGameOpenApiServer is the server API for OpenGameOpenApi service.
// All implementations should embed UnimplementedOpenGameOpenApiServer
// for forward compatibility
type OpenGameOpenApiServer interface {
	// 风控上报
	RiskReport(context.Context, *RiskReportReq) (*RiskReportRsp, error)
	// 统一校验
	RiskCheck(context.Context, *RiskCheckReq) (*RiskCheckRsp, error)
	// 风控查询
	RiskQuery(context.Context, *RiskQueryReq) (*RiskQueryRsp, error)
}

// UnimplementedOpenGameOpenApiServer should be embedded to have forward compatible implementations.
type UnimplementedOpenGameOpenApiServer struct {
}

func (UnimplementedOpenGameOpenApiServer) RiskReport(context.Context, *RiskReportReq) (*RiskReportRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RiskReport not implemented")
}
func (UnimplementedOpenGameOpenApiServer) RiskCheck(context.Context, *RiskCheckReq) (*RiskCheckRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RiskCheck not implemented")
}
func (UnimplementedOpenGameOpenApiServer) RiskQuery(context.Context, *RiskQueryReq) (*RiskQueryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RiskQuery not implemented")
}

// UnsafeOpenGameOpenApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OpenGameOpenApiServer will
// result in compilation errors.
type UnsafeOpenGameOpenApiServer interface {
	mustEmbedUnimplementedOpenGameOpenApiServer()
}

func RegisterOpenGameOpenApiServer(s grpc.ServiceRegistrar, srv OpenGameOpenApiServer) {
	s.RegisterService(&OpenGameOpenApi_ServiceDesc, srv)
}

func _OpenGameOpenApi_RiskReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RiskReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OpenGameOpenApiServer).RiskReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OpenGameOpenApi_RiskReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OpenGameOpenApiServer).RiskReport(ctx, req.(*RiskReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OpenGameOpenApi_RiskCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RiskCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OpenGameOpenApiServer).RiskCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OpenGameOpenApi_RiskCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OpenGameOpenApiServer).RiskCheck(ctx, req.(*RiskCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OpenGameOpenApi_RiskQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RiskQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OpenGameOpenApiServer).RiskQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OpenGameOpenApi_RiskQuery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OpenGameOpenApiServer).RiskQuery(ctx, req.(*RiskQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

// OpenGameOpenApi_ServiceDesc is the grpc.ServiceDesc for OpenGameOpenApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OpenGameOpenApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moe_risk_openapi.OpenGameOpenApi",
	HandlerType: (*OpenGameOpenApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RiskReport",
			Handler:    _OpenGameOpenApi_RiskReport_Handler,
		},
		{
			MethodName: "RiskCheck",
			Handler:    _OpenGameOpenApi_RiskCheck_Handler,
		},
		{
			MethodName: "RiskQuery",
			Handler:    _OpenGameOpenApi_RiskQuery_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moe_risk_proto/moe_risk_adapter/openapi/moe_risk_openapi.proto",
}
