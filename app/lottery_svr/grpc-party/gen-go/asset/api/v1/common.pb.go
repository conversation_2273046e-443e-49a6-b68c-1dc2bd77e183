// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0--rc1
// source: asset/api/v1/common.proto

package corev1

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AssetAttr int32

const (
	AssetAttr_ASSET_ATTR_UNSPECIFIED AssetAttr = 0
	AssetAttr_ASSET_ATTR_PRESENT     AssetAttr = 1 // 赠送
	AssetAttr_ASSET_ATTR_PAY         AssetAttr = 2 // 支付
	AssetAttr_ASSET_ATTR_EXCHANGE    AssetAttr = 3 //兑换
)

// Enum value maps for AssetAttr.
var (
	AssetAttr_name = map[int32]string{
		0: "ASSET_ATTR_UNSPECIFIED",
		1: "ASSET_ATTR_PRESENT",
		2: "ASSET_ATTR_PAY",
		3: "ASSET_ATTR_EXCHANGE",
	}
	AssetAttr_value = map[string]int32{
		"ASSET_ATTR_UNSPECIFIED": 0,
		"ASSET_ATTR_PRESENT":     1,
		"ASSET_ATTR_PAY":         2,
		"ASSET_ATTR_EXCHANGE":    3,
	}
)

func (x AssetAttr) Enum() *AssetAttr {
	p := new(AssetAttr)
	*p = x
	return p
}

func (x AssetAttr) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetAttr) Descriptor() protoreflect.EnumDescriptor {
	return file_asset_api_v1_common_proto_enumTypes[0].Descriptor()
}

func (AssetAttr) Type() protoreflect.EnumType {
	return &file_asset_api_v1_common_proto_enumTypes[0]
}

func (x AssetAttr) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetAttr.Descriptor instead.
func (AssetAttr) EnumDescriptor() ([]byte, []int) {
	return file_asset_api_v1_common_proto_rawDescGZIP(), []int{0}
}

type AssetDetail_CodecType int32

const (
	AssetDetail_CODEC_TYPE_UNSPECIFIED  AssetDetail_CodecType = 0
	AssetDetail_CODEC_TYPE_EXPIRE_ARRAY AssetDetail_CodecType = 1 // 对应 AssetDetailExpireArray
)

// Enum value maps for AssetDetail_CodecType.
var (
	AssetDetail_CodecType_name = map[int32]string{
		0: "CODEC_TYPE_UNSPECIFIED",
		1: "CODEC_TYPE_EXPIRE_ARRAY",
	}
	AssetDetail_CodecType_value = map[string]int32{
		"CODEC_TYPE_UNSPECIFIED":  0,
		"CODEC_TYPE_EXPIRE_ARRAY": 1,
	}
)

func (x AssetDetail_CodecType) Enum() *AssetDetail_CodecType {
	p := new(AssetDetail_CodecType)
	*p = x
	return p
}

func (x AssetDetail_CodecType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetDetail_CodecType) Descriptor() protoreflect.EnumDescriptor {
	return file_asset_api_v1_common_proto_enumTypes[1].Descriptor()
}

func (AssetDetail_CodecType) Type() protoreflect.EnumType {
	return &file_asset_api_v1_common_proto_enumTypes[1]
}

func (x AssetDetail_CodecType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetDetail_CodecType.Descriptor instead.
func (AssetDetail_CodecType) EnumDescriptor() ([]byte, []int) {
	return file_asset_api_v1_common_proto_rawDescGZIP(), []int{5, 0}
}

type AssetDetail_CompressType int32

const (
	AssetDetail_COMPRESS_TYPE_UNSPECIFIED AssetDetail_CompressType = 0
	AssetDetail_COMPRESS_TYPE_GZIP        AssetDetail_CompressType = 1
)

// Enum value maps for AssetDetail_CompressType.
var (
	AssetDetail_CompressType_name = map[int32]string{
		0: "COMPRESS_TYPE_UNSPECIFIED",
		1: "COMPRESS_TYPE_GZIP",
	}
	AssetDetail_CompressType_value = map[string]int32{
		"COMPRESS_TYPE_UNSPECIFIED": 0,
		"COMPRESS_TYPE_GZIP":        1,
	}
)

func (x AssetDetail_CompressType) Enum() *AssetDetail_CompressType {
	p := new(AssetDetail_CompressType)
	*p = x
	return p
}

func (x AssetDetail_CompressType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetDetail_CompressType) Descriptor() protoreflect.EnumDescriptor {
	return file_asset_api_v1_common_proto_enumTypes[2].Descriptor()
}

func (AssetDetail_CompressType) Type() protoreflect.EnumType {
	return &file_asset_api_v1_common_proto_enumTypes[2]
}

func (x AssetDetail_CompressType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetDetail_CompressType.Descriptor instead.
func (AssetDetail_CompressType) EnumDescriptor() ([]byte, []int) {
	return file_asset_api_v1_common_proto_rawDescGZIP(), []int{5, 1}
}

type UserAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId  int32               `protobuf:"varint,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	AssetNum int64               `protobuf:"varint,2,opt,name=asset_num,json=assetNum,proto3" json:"asset_num,omitempty"`
	Details  []*UserAsset_Detail `protobuf:"bytes,3,rep,name=details,proto3" json:"details,omitempty"`
}

func (x *UserAsset) Reset() {
	*x = UserAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAsset) ProtoMessage() {}

func (x *UserAsset) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAsset.ProtoReflect.Descriptor instead.
func (*UserAsset) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_common_proto_rawDescGZIP(), []int{0}
}

func (x *UserAsset) GetAssetId() int32 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *UserAsset) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *UserAsset) GetDetails() []*UserAsset_Detail {
	if x != nil {
		return x.Details
	}
	return nil
}

type PresentAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId  int32 `protobuf:"varint,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	AssetNum int64 `protobuf:"varint,2,opt,name=asset_num,json=assetNum,proto3" json:"asset_num,omitempty"`
	ExpireTs int64 `protobuf:"varint,3,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"` // 过期时间 秒 0：不过期
}

func (x *PresentAsset) Reset() {
	*x = PresentAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PresentAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresentAsset) ProtoMessage() {}

func (x *PresentAsset) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresentAsset.ProtoReflect.Descriptor instead.
func (*PresentAsset) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_common_proto_rawDescGZIP(), []int{1}
}

func (x *PresentAsset) GetAssetId() int32 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *PresentAsset) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *PresentAsset) GetExpireTs() int64 {
	if x != nil {
		return x.ExpireTs
	}
	return 0
}

type ConsumeAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId  int32 `protobuf:"varint,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	AssetNum int64 `protobuf:"varint,2,opt,name=asset_num,json=assetNum,proto3" json:"asset_num,omitempty"`
}

func (x *ConsumeAsset) Reset() {
	*x = ConsumeAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeAsset) ProtoMessage() {}

func (x *ConsumeAsset) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeAsset.ProtoReflect.Descriptor instead.
func (*ConsumeAsset) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_common_proto_rawDescGZIP(), []int{2}
}

func (x *ConsumeAsset) GetAssetId() int32 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *ConsumeAsset) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

type CommitAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId         int32 `protobuf:"varint,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	ConsumeAssetNum int64 `protobuf:"varint,2,opt,name=consume_asset_num,json=consumeAssetNum,proto3" json:"consume_asset_num,omitempty"` // 资产消耗数量;
}

func (x *CommitAsset) Reset() {
	*x = CommitAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitAsset) ProtoMessage() {}

func (x *CommitAsset) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitAsset.ProtoReflect.Descriptor instead.
func (*CommitAsset) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_common_proto_rawDescGZIP(), []int{3}
}

func (x *CommitAsset) GetAssetId() int32 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *CommitAsset) GetConsumeAssetNum() int64 {
	if x != nil {
		return x.ConsumeAssetNum
	}
	return 0
}

type AfterDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pay      *AfterDetail_Account `protobuf:"bytes,1,opt,name=pay,proto3" json:"pay,omitempty"`           // 充值帐户
	Present  *AfterDetail_Account `protobuf:"bytes,2,opt,name=present,proto3" json:"present,omitempty"`   // 赠送账户
	Exchange *AfterDetail_Account `protobuf:"bytes,3,opt,name=exchange,proto3" json:"exchange,omitempty"` // 兑换账户
}

func (x *AfterDetail) Reset() {
	*x = AfterDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_common_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AfterDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AfterDetail) ProtoMessage() {}

func (x *AfterDetail) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_common_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AfterDetail.ProtoReflect.Descriptor instead.
func (*AfterDetail) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_common_proto_rawDescGZIP(), []int{4}
}

func (x *AfterDetail) GetPay() *AfterDetail_Account {
	if x != nil {
		return x.Pay
	}
	return nil
}

func (x *AfterDetail) GetPresent() *AfterDetail_Account {
	if x != nil {
		return x.Present
	}
	return nil
}

func (x *AfterDetail) GetExchange() *AfterDetail_Account {
	if x != nil {
		return x.Exchange
	}
	return nil
}

type AssetDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CodecType    AssetDetail_CodecType    `protobuf:"varint,1,opt,name=codec_type,json=codecType,proto3,enum=asset.api.v1.AssetDetail_CodecType" json:"codec_type,omitempty"`             // 编码类型
	CompressType AssetDetail_CompressType `protobuf:"varint,2,opt,name=compress_type,json=compressType,proto3,enum=asset.api.v1.AssetDetail_CompressType" json:"compress_type,omitempty"` // 压缩类型
	Data         []byte                   `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`                                                                                 // 数据
}

func (x *AssetDetail) Reset() {
	*x = AssetDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_common_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetDetail) ProtoMessage() {}

func (x *AssetDetail) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_common_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetDetail.ProtoReflect.Descriptor instead.
func (*AssetDetail) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_common_proto_rawDescGZIP(), []int{5}
}

func (x *AssetDetail) GetCodecType() AssetDetail_CodecType {
	if x != nil {
		return x.CodecType
	}
	return AssetDetail_CODEC_TYPE_UNSPECIFIED
}

func (x *AssetDetail) GetCompressType() AssetDetail_CompressType {
	if x != nil {
		return x.CompressType
	}
	return AssetDetail_COMPRESS_TYPE_UNSPECIFIED
}

func (x *AssetDetail) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type ModifiedAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId   int32     `protobuf:"varint,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	AssetNum  int64     `protobuf:"varint,2,opt,name=asset_num,json=assetNum,proto3" json:"asset_num,omitempty"`
	ExpireTs  int64     `protobuf:"varint,3,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"` // 过期时间
	AssetAttr AssetAttr `protobuf:"varint,4,opt,name=asset_attr,json=assetAttr,proto3,enum=asset.api.v1.AssetAttr" json:"asset_attr,omitempty"`
}

func (x *ModifiedAsset) Reset() {
	*x = ModifiedAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_common_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifiedAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifiedAsset) ProtoMessage() {}

func (x *ModifiedAsset) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_common_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifiedAsset.ProtoReflect.Descriptor instead.
func (*ModifiedAsset) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_common_proto_rawDescGZIP(), []int{6}
}

func (x *ModifiedAsset) GetAssetId() int32 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *ModifiedAsset) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *ModifiedAsset) GetExpireTs() int64 {
	if x != nil {
		return x.ExpireTs
	}
	return 0
}

func (x *ModifiedAsset) GetAssetAttr() AssetAttr {
	if x != nil {
		return x.AssetAttr
	}
	return AssetAttr_ASSET_ATTR_UNSPECIFIED
}

type AfterAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId   int32     `protobuf:"varint,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	AssetNum  int64     `protobuf:"varint,2,opt,name=asset_num,json=assetNum,proto3" json:"asset_num,omitempty"`
	ExpireTs  int64     `protobuf:"varint,3,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"` // 过期时间
	AssetAttr AssetAttr `protobuf:"varint,4,opt,name=asset_attr,json=assetAttr,proto3,enum=asset.api.v1.AssetAttr" json:"asset_attr,omitempty"`
}

func (x *AfterAsset) Reset() {
	*x = AfterAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_common_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AfterAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AfterAsset) ProtoMessage() {}

func (x *AfterAsset) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_common_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AfterAsset.ProtoReflect.Descriptor instead.
func (*AfterAsset) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_common_proto_rawDescGZIP(), []int{7}
}

func (x *AfterAsset) GetAssetId() int32 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *AfterAsset) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *AfterAsset) GetExpireTs() int64 {
	if x != nil {
		return x.ExpireTs
	}
	return 0
}

func (x *AfterAsset) GetAssetAttr() AssetAttr {
	if x != nil {
		return x.AssetAttr
	}
	return AssetAttr_ASSET_ATTR_UNSPECIFIED
}

type UserAsset_Detail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetNum int64 `protobuf:"varint,1,opt,name=asset_num,json=assetNum,proto3" json:"asset_num,omitempty"`
	ExpireTs int64 `protobuf:"varint,2,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
}

func (x *UserAsset_Detail) Reset() {
	*x = UserAsset_Detail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_common_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAsset_Detail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAsset_Detail) ProtoMessage() {}

func (x *UserAsset_Detail) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_common_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAsset_Detail.ProtoReflect.Descriptor instead.
func (*UserAsset_Detail) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_common_proto_rawDescGZIP(), []int{0, 0}
}

func (x *UserAsset_Detail) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *UserAsset_Detail) GetExpireTs() int64 {
	if x != nil {
		return x.ExpireTs
	}
	return 0
}

type AfterDetail_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetNum int64 `protobuf:"varint,1,opt,name=asset_num,json=assetNum,proto3" json:"asset_num,omitempty"`
	ExpireTs int64 `protobuf:"varint,2,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
}

func (x *AfterDetail_Item) Reset() {
	*x = AfterDetail_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_common_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AfterDetail_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AfterDetail_Item) ProtoMessage() {}

func (x *AfterDetail_Item) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_common_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AfterDetail_Item.ProtoReflect.Descriptor instead.
func (*AfterDetail_Item) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_common_proto_rawDescGZIP(), []int{4, 0}
}

func (x *AfterDetail_Item) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *AfterDetail_Item) GetExpireTs() int64 {
	if x != nil {
		return x.ExpireTs
	}
	return 0
}

type AfterDetail_Account struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetNum int64               `protobuf:"varint,1,opt,name=asset_num,json=assetNum,proto3" json:"asset_num,omitempty"` // 不过期
	Expires  []*AfterDetail_Item `protobuf:"bytes,2,rep,name=expires,proto3" json:"expires,omitempty"`
}

func (x *AfterDetail_Account) Reset() {
	*x = AfterDetail_Account{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_common_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AfterDetail_Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AfterDetail_Account) ProtoMessage() {}

func (x *AfterDetail_Account) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_common_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AfterDetail_Account.ProtoReflect.Descriptor instead.
func (*AfterDetail_Account) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_common_proto_rawDescGZIP(), []int{4, 1}
}

func (x *AfterDetail_Account) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *AfterDetail_Account) GetExpires() []*AfterDetail_Item {
	if x != nil {
		return x.Expires
	}
	return nil
}

var File_asset_api_v1_common_proto protoreflect.FileDescriptor

var file_asset_api_v1_common_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc1, 0x01, 0x0a, 0x09, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x38, 0x0a,
	0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x07,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x42, 0x0a, 0x06, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1b,
	0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x73, 0x22, 0x63, 0x0a, 0x0c, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x73,
	0x22, 0x46, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x22, 0x54, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d,
	0x69, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x22, 0xe2,
	0x02, 0x0a, 0x0b, 0x41, 0x66, 0x74, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x33,
	0x0a, 0x03, 0x70, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x66, 0x74, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x03,
	0x70, 0x61, 0x79, 0x12, 0x3b, 0x0a, 0x07, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x66, 0x74, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x07, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74,
	0x12, 0x3d, 0x0a, 0x08, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x66, 0x74, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x08, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x1a,
	0x40, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54,
	0x73, 0x1a, 0x60, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x38, 0x0a, 0x07, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x66, 0x74, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x73, 0x22, 0xbf, 0x02, 0x0a, 0x0b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x42, 0x0a, 0x0a, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x63, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x63, 0x6f,
	0x64, 0x65, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26,
	0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x44, 0x0a, 0x09, 0x43, 0x6f, 0x64, 0x65,
	0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x43, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x4f, 0x44, 0x45, 0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x5f, 0x41, 0x52, 0x52, 0x41, 0x59, 0x10, 0x01, 0x22, 0x45,
	0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d,
	0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x50, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a,
	0x12, 0x43, 0x4f, 0x4d, 0x50, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47,
	0x5a, 0x49, 0x50, 0x10, 0x01, 0x22, 0x9c, 0x01, 0x0a, 0x0d, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12,
	0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x73, 0x12, 0x36, 0x0a, 0x0a,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x17, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x74, 0x74, 0x72, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x41, 0x74, 0x74, 0x72, 0x22, 0x99, 0x01, 0x0a, 0x0a, 0x41, 0x66, 0x74, 0x65, 0x72, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x73, 0x12, 0x36, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x41, 0x74, 0x74, 0x72, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x41, 0x74, 0x74, 0x72,
	0x2a, 0x6c, 0x0a, 0x09, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x74, 0x74, 0x72, 0x12, 0x1a, 0x0a,
	0x16, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x5f, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54, 0x10,
	0x01, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x5f,
	0x50, 0x41, 0x59, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x41,
	0x54, 0x54, 0x52, 0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x03, 0x42, 0x4b,
	0x5a, 0x49, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x6d, 0x65,
	0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x63, 0x6f, 0x72,
	0x70, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x62, 0x6f, 0x73, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x6f, 0x72, 0x65, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_asset_api_v1_common_proto_rawDescOnce sync.Once
	file_asset_api_v1_common_proto_rawDescData = file_asset_api_v1_common_proto_rawDesc
)

func file_asset_api_v1_common_proto_rawDescGZIP() []byte {
	file_asset_api_v1_common_proto_rawDescOnce.Do(func() {
		file_asset_api_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_asset_api_v1_common_proto_rawDescData)
	})
	return file_asset_api_v1_common_proto_rawDescData
}

var file_asset_api_v1_common_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_asset_api_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_asset_api_v1_common_proto_goTypes = []interface{}{
	(AssetAttr)(0),                // 0: asset.api.v1.AssetAttr
	(AssetDetail_CodecType)(0),    // 1: asset.api.v1.AssetDetail.CodecType
	(AssetDetail_CompressType)(0), // 2: asset.api.v1.AssetDetail.CompressType
	(*UserAsset)(nil),             // 3: asset.api.v1.UserAsset
	(*PresentAsset)(nil),          // 4: asset.api.v1.PresentAsset
	(*ConsumeAsset)(nil),          // 5: asset.api.v1.ConsumeAsset
	(*CommitAsset)(nil),           // 6: asset.api.v1.CommitAsset
	(*AfterDetail)(nil),           // 7: asset.api.v1.AfterDetail
	(*AssetDetail)(nil),           // 8: asset.api.v1.AssetDetail
	(*ModifiedAsset)(nil),         // 9: asset.api.v1.ModifiedAsset
	(*AfterAsset)(nil),            // 10: asset.api.v1.AfterAsset
	(*UserAsset_Detail)(nil),      // 11: asset.api.v1.UserAsset.Detail
	(*AfterDetail_Item)(nil),      // 12: asset.api.v1.AfterDetail.Item
	(*AfterDetail_Account)(nil),   // 13: asset.api.v1.AfterDetail.Account
}
var file_asset_api_v1_common_proto_depIdxs = []int32{
	11, // 0: asset.api.v1.UserAsset.details:type_name -> asset.api.v1.UserAsset.Detail
	13, // 1: asset.api.v1.AfterDetail.pay:type_name -> asset.api.v1.AfterDetail.Account
	13, // 2: asset.api.v1.AfterDetail.present:type_name -> asset.api.v1.AfterDetail.Account
	13, // 3: asset.api.v1.AfterDetail.exchange:type_name -> asset.api.v1.AfterDetail.Account
	1,  // 4: asset.api.v1.AssetDetail.codec_type:type_name -> asset.api.v1.AssetDetail.CodecType
	2,  // 5: asset.api.v1.AssetDetail.compress_type:type_name -> asset.api.v1.AssetDetail.CompressType
	0,  // 6: asset.api.v1.ModifiedAsset.asset_attr:type_name -> asset.api.v1.AssetAttr
	0,  // 7: asset.api.v1.AfterAsset.asset_attr:type_name -> asset.api.v1.AssetAttr
	12, // 8: asset.api.v1.AfterDetail.Account.expires:type_name -> asset.api.v1.AfterDetail.Item
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_asset_api_v1_common_proto_init() }
func file_asset_api_v1_common_proto_init() {
	if File_asset_api_v1_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_asset_api_v1_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PresentAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_common_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_common_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AfterDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_common_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_common_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifiedAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_common_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AfterAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_common_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAsset_Detail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_common_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AfterDetail_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_common_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AfterDetail_Account); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_asset_api_v1_common_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_asset_api_v1_common_proto_goTypes,
		DependencyIndexes: file_asset_api_v1_common_proto_depIdxs,
		EnumInfos:         file_asset_api_v1_common_proto_enumTypes,
		MessageInfos:      file_asset_api_v1_common_proto_msgTypes,
	}.Build()
	File_asset_api_v1_common_proto = out.File
	file_asset_api_v1_common_proto_rawDesc = nil
	file_asset_api_v1_common_proto_goTypes = nil
	file_asset_api_v1_common_proto_depIdxs = nil
}
