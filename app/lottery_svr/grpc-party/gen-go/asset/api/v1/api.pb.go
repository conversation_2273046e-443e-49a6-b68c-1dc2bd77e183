// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0--rc1
// source: asset/api/v1/api.proto

package corev1

import (
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AssetReduceStrategy int32

const (
	AssetReduceStrategy_ASSET_REDUCE_STRATEGY_DEFAULT       AssetReduceStrategy = 0 //
	AssetReduceStrategy_ASSET_REDUCE_STRATEGY_NORMAL        AssetReduceStrategy = 1 //充值账户->兑换账户->赠送账户
	AssetReduceStrategy_ASSET_REDUCE_STRATEGY_STRICT_PAY    AssetReduceStrategy = 2 //充值账户
	AssetReduceStrategy_ASSET_REDUCE_STRATEGY_PRIOR_PRESENT AssetReduceStrategy = 3 //赠送账户->充值帐户
)

// Enum value maps for AssetReduceStrategy.
var (
	AssetReduceStrategy_name = map[int32]string{
		0: "ASSET_REDUCE_STRATEGY_DEFAULT",
		1: "ASSET_REDUCE_STRATEGY_NORMAL",
		2: "ASSET_REDUCE_STRATEGY_STRICT_PAY",
		3: "ASSET_REDUCE_STRATEGY_PRIOR_PRESENT",
	}
	AssetReduceStrategy_value = map[string]int32{
		"ASSET_REDUCE_STRATEGY_DEFAULT":       0,
		"ASSET_REDUCE_STRATEGY_NORMAL":        1,
		"ASSET_REDUCE_STRATEGY_STRICT_PAY":    2,
		"ASSET_REDUCE_STRATEGY_PRIOR_PRESENT": 3,
	}
)

func (x AssetReduceStrategy) Enum() *AssetReduceStrategy {
	p := new(AssetReduceStrategy)
	*p = x
	return p
}

func (x AssetReduceStrategy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetReduceStrategy) Descriptor() protoreflect.EnumDescriptor {
	return file_asset_api_v1_api_proto_enumTypes[0].Descriptor()
}

func (AssetReduceStrategy) Type() protoreflect.EnumType {
	return &file_asset_api_v1_api_proto_enumTypes[0]
}

func (x AssetReduceStrategy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetReduceStrategy.Descriptor instead.
func (AssetReduceStrategy) EnumDescriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{0}
}

// 查询资产
type QueryAssetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid       int32   `protobuf:"varint,1,opt,name=bid,proto3" json:"bid,omitempty"`                                  // 业务线ID
	AppId     string  `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                  // appid
	AccountId string  `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`      // 账户 id
	UserId    string  `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`               // 用户 ID
	AssetIds  []int32 `protobuf:"varint,5,rep,packed,name=asset_ids,json=assetIds,proto3" json:"asset_ids,omitempty"` // 资产ID
}

func (x *QueryAssetRequest) Reset() {
	*x = QueryAssetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAssetRequest) ProtoMessage() {}

func (x *QueryAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAssetRequest.ProtoReflect.Descriptor instead.
func (*QueryAssetRequest) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{0}
}

func (x *QueryAssetRequest) GetBid() int32 {
	if x != nil {
		return x.Bid
	}
	return 0
}

func (x *QueryAssetRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryAssetRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *QueryAssetRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *QueryAssetRequest) GetAssetIds() []int32 {
	if x != nil {
		return x.AssetIds
	}
	return nil
}

type QueryAssetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*UserAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"` // 用户资产
}

func (x *QueryAssetResponse) Reset() {
	*x = QueryAssetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAssetResponse) ProtoMessage() {}

func (x *QueryAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAssetResponse.ProtoReflect.Descriptor instead.
func (*QueryAssetResponse) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{1}
}

func (x *QueryAssetResponse) GetAssets() []*UserAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

// 赠送资产
type PresentAssetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid           int32           `protobuf:"varint,1,opt,name=bid,proto3" json:"bid,omitempty"`                                         // 业务线ID
	AppId         string          `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                         // appid
	AccountId     string          `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`             // 账户 ID
	UserId        string          `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                      // 用户 ID
	OrderId       string          `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`                   // 事务 ID (40 字节)
	PresentAssets []*PresentAsset `protobuf:"bytes,6,rep,name=present_assets,json=presentAssets,proto3" json:"present_assets,omitempty"` // 赠送资产
	OrderTime     int64           `protobuf:"varint,7,opt,name=order_time,json=orderTime,proto3" json:"order_time,omitempty"`            // 请求时间
	Remark        string          `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark,omitempty"`                                    // 备注 (128 字节)
	Ext           string          `protobuf:"bytes,9,opt,name=ext,proto3" json:"ext,omitempty"`
}

func (x *PresentAssetRequest) Reset() {
	*x = PresentAssetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PresentAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresentAssetRequest) ProtoMessage() {}

func (x *PresentAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresentAssetRequest.ProtoReflect.Descriptor instead.
func (*PresentAssetRequest) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{2}
}

func (x *PresentAssetRequest) GetBid() int32 {
	if x != nil {
		return x.Bid
	}
	return 0
}

func (x *PresentAssetRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PresentAssetRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *PresentAssetRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PresentAssetRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *PresentAssetRequest) GetPresentAssets() []*PresentAsset {
	if x != nil {
		return x.PresentAssets
	}
	return nil
}

func (x *PresentAssetRequest) GetOrderTime() int64 {
	if x != nil {
		return x.OrderTime
	}
	return 0
}

func (x *PresentAssetRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *PresentAssetRequest) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

type PresentAssetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*OperateAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"` // 受影响的用户资产余额
}

func (x *PresentAssetResponse) Reset() {
	*x = PresentAssetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PresentAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresentAssetResponse) ProtoMessage() {}

func (x *PresentAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresentAssetResponse.ProtoReflect.Descriptor instead.
func (*PresentAssetResponse) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{3}
}

func (x *PresentAssetResponse) GetAssets() []*OperateAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

// 消耗资产
type ConsumeAssetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid            int32               `protobuf:"varint,1,opt,name=bid,proto3" json:"bid,omitempty"`                                                                                   // 业务线ID
	AppId          string              `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                                                                   // appid
	AccountId      string              `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                                                       // 账户 ID
	UserId         string              `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                                                // 用户 ID
	OrderId        string              `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`                                                             // 订单 ID (40 字节)
	ReduceStrategy AssetReduceStrategy `protobuf:"varint,6,opt,name=reduce_strategy,json=reduceStrategy,proto3,enum=asset.api.v1.AssetReduceStrategy" json:"reduce_strategy,omitempty"` //消耗策略
	ConsumeAssets  []*ConsumeAsset     `protobuf:"bytes,7,rep,name=consume_assets,json=consumeAssets,proto3" json:"consume_assets,omitempty"`                                           // 消耗资产
	OrderTime      int64               `protobuf:"varint,8,opt,name=order_time,json=orderTime,proto3" json:"order_time,omitempty"`                                                      // 请求时间
	Remark         string              `protobuf:"bytes,9,opt,name=remark,proto3" json:"remark,omitempty"`                                                                              // 备注 (128 字节)
	Ext            string              `protobuf:"bytes,10,opt,name=ext,proto3" json:"ext,omitempty"`
}

func (x *ConsumeAssetRequest) Reset() {
	*x = ConsumeAssetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeAssetRequest) ProtoMessage() {}

func (x *ConsumeAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeAssetRequest.ProtoReflect.Descriptor instead.
func (*ConsumeAssetRequest) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{4}
}

func (x *ConsumeAssetRequest) GetBid() int32 {
	if x != nil {
		return x.Bid
	}
	return 0
}

func (x *ConsumeAssetRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ConsumeAssetRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *ConsumeAssetRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ConsumeAssetRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *ConsumeAssetRequest) GetReduceStrategy() AssetReduceStrategy {
	if x != nil {
		return x.ReduceStrategy
	}
	return AssetReduceStrategy_ASSET_REDUCE_STRATEGY_DEFAULT
}

func (x *ConsumeAssetRequest) GetConsumeAssets() []*ConsumeAsset {
	if x != nil {
		return x.ConsumeAssets
	}
	return nil
}

func (x *ConsumeAssetRequest) GetOrderTime() int64 {
	if x != nil {
		return x.OrderTime
	}
	return 0
}

func (x *ConsumeAssetRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *ConsumeAssetRequest) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

type ConsumeAssetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*OperateAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"` // 受影响的用户资产余额
}

func (x *ConsumeAssetResponse) Reset() {
	*x = ConsumeAssetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeAssetResponse) ProtoMessage() {}

func (x *ConsumeAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeAssetResponse.ProtoReflect.Descriptor instead.
func (*ConsumeAssetResponse) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{5}
}

func (x *ConsumeAssetResponse) GetAssets() []*OperateAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

// 消耗资产
type RefundConsumeAssetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid            int32  `protobuf:"varint,1,opt,name=bid,proto3" json:"bid,omitempty"`                                              // 业务线ID
	AppId          string `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                              // appid
	AccountId      string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                  // 账户 ID
	UserId         string `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                           // 用户 ID
	ConsumeOrderId string `protobuf:"bytes,5,opt,name=consume_order_id,json=consumeOrderId,proto3" json:"consume_order_id,omitempty"` // 原扣费订单号 (40 字节)
	OrderTime      int64  `protobuf:"varint,6,opt,name=order_time,json=orderTime,proto3" json:"order_time,omitempty"`                 // 请求时间
	Remark         string `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark,omitempty"`                                         // 备注 (128 字节)
	Ext            string `protobuf:"bytes,8,opt,name=ext,proto3" json:"ext,omitempty"`
}

func (x *RefundConsumeAssetRequest) Reset() {
	*x = RefundConsumeAssetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundConsumeAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundConsumeAssetRequest) ProtoMessage() {}

func (x *RefundConsumeAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundConsumeAssetRequest.ProtoReflect.Descriptor instead.
func (*RefundConsumeAssetRequest) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{6}
}

func (x *RefundConsumeAssetRequest) GetBid() int32 {
	if x != nil {
		return x.Bid
	}
	return 0
}

func (x *RefundConsumeAssetRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *RefundConsumeAssetRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *RefundConsumeAssetRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RefundConsumeAssetRequest) GetConsumeOrderId() string {
	if x != nil {
		return x.ConsumeOrderId
	}
	return ""
}

func (x *RefundConsumeAssetRequest) GetOrderTime() int64 {
	if x != nil {
		return x.OrderTime
	}
	return 0
}

func (x *RefundConsumeAssetRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *RefundConsumeAssetRequest) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

type RefundConsumeAssetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*OperateAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"` // 受影响的用户资产余额
}

func (x *RefundConsumeAssetResponse) Reset() {
	*x = RefundConsumeAssetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundConsumeAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundConsumeAssetResponse) ProtoMessage() {}

func (x *RefundConsumeAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundConsumeAssetResponse.ProtoReflect.Descriptor instead.
func (*RefundConsumeAssetResponse) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{7}
}

func (x *RefundConsumeAssetResponse) GetAssets() []*OperateAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

// 预消耗资产
type PreConsumeAssetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid               int32               `protobuf:"varint,1,opt,name=bid,proto3" json:"bid,omitempty"`                                                                                   // 业务线ID
	AppId             string              `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                                                                   // appid
	AccountId         string              `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                                                       // 账户 ID
	UserId            string              `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                                                // 用户 ID
	PreConsumeOrderId string              `protobuf:"bytes,5,opt,name=pre_consume_order_id,json=preConsumeOrderId,proto3" json:"pre_consume_order_id,omitempty"`                           // 预扣订单号 (40 字节)
	ReduceStrategy    AssetReduceStrategy `protobuf:"varint,6,opt,name=reduce_strategy,json=reduceStrategy,proto3,enum=asset.api.v1.AssetReduceStrategy" json:"reduce_strategy,omitempty"` //预消耗策略
	ConsumeAssets     []*ConsumeAsset     `protobuf:"bytes,7,rep,name=consume_assets,json=consumeAssets,proto3" json:"consume_assets,omitempty"`                                           // 消耗资产
	Remark            string              `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark,omitempty"`                                                                              // 备注 (128 字节)
	AutoCommitTime    int64               `protobuf:"varint,9,opt,name=auto_commit_time,json=autoCommitTime,proto3" json:"auto_commit_time,omitempty"`                                     // 自动确认时间 (秒) 超过时间自动确认
	OrderTime         int64               `protobuf:"varint,10,opt,name=order_time,json=orderTime,proto3" json:"order_time,omitempty"`                                                     // 请求时间
	Ext               string              `protobuf:"bytes,11,opt,name=ext,proto3" json:"ext,omitempty"`
}

func (x *PreConsumeAssetRequest) Reset() {
	*x = PreConsumeAssetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreConsumeAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreConsumeAssetRequest) ProtoMessage() {}

func (x *PreConsumeAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreConsumeAssetRequest.ProtoReflect.Descriptor instead.
func (*PreConsumeAssetRequest) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{8}
}

func (x *PreConsumeAssetRequest) GetBid() int32 {
	if x != nil {
		return x.Bid
	}
	return 0
}

func (x *PreConsumeAssetRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PreConsumeAssetRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *PreConsumeAssetRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PreConsumeAssetRequest) GetPreConsumeOrderId() string {
	if x != nil {
		return x.PreConsumeOrderId
	}
	return ""
}

func (x *PreConsumeAssetRequest) GetReduceStrategy() AssetReduceStrategy {
	if x != nil {
		return x.ReduceStrategy
	}
	return AssetReduceStrategy_ASSET_REDUCE_STRATEGY_DEFAULT
}

func (x *PreConsumeAssetRequest) GetConsumeAssets() []*ConsumeAsset {
	if x != nil {
		return x.ConsumeAssets
	}
	return nil
}

func (x *PreConsumeAssetRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *PreConsumeAssetRequest) GetAutoCommitTime() int64 {
	if x != nil {
		return x.AutoCommitTime
	}
	return 0
}

func (x *PreConsumeAssetRequest) GetOrderTime() int64 {
	if x != nil {
		return x.OrderTime
	}
	return 0
}

func (x *PreConsumeAssetRequest) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

type PreConsumeAssetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*OperateAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"` // 受影响的用户资产余额
}

func (x *PreConsumeAssetResponse) Reset() {
	*x = PreConsumeAssetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreConsumeAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreConsumeAssetResponse) ProtoMessage() {}

func (x *PreConsumeAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreConsumeAssetResponse.ProtoReflect.Descriptor instead.
func (*PreConsumeAssetResponse) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{9}
}

func (x *PreConsumeAssetResponse) GetAssets() []*OperateAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

// 回滚预消耗资产
type RollbackConsumeAssetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid               int32  `protobuf:"varint,1,opt,name=bid,proto3" json:"bid,omitempty"`                                                         // 业务线ID
	AppId             string `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                                         // appid
	AccountId         string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                             // 账户 ID
	UserId            string `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                      // 用户 ID
	PreConsumeOrderId string `protobuf:"bytes,5,opt,name=pre_consume_order_id,json=preConsumeOrderId,proto3" json:"pre_consume_order_id,omitempty"` // 预扣订单号 (40 字节)
	OrderTime         int64  `protobuf:"varint,6,opt,name=order_time,json=orderTime,proto3" json:"order_time,omitempty"`                            // 请求时间
	Remark            string `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark,omitempty"`                                                    // 备注 (128 字节)
	Ext               string `protobuf:"bytes,8,opt,name=ext,proto3" json:"ext,omitempty"`
}

func (x *RollbackConsumeAssetRequest) Reset() {
	*x = RollbackConsumeAssetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RollbackConsumeAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackConsumeAssetRequest) ProtoMessage() {}

func (x *RollbackConsumeAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackConsumeAssetRequest.ProtoReflect.Descriptor instead.
func (*RollbackConsumeAssetRequest) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{10}
}

func (x *RollbackConsumeAssetRequest) GetBid() int32 {
	if x != nil {
		return x.Bid
	}
	return 0
}

func (x *RollbackConsumeAssetRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *RollbackConsumeAssetRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *RollbackConsumeAssetRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RollbackConsumeAssetRequest) GetPreConsumeOrderId() string {
	if x != nil {
		return x.PreConsumeOrderId
	}
	return ""
}

func (x *RollbackConsumeAssetRequest) GetOrderTime() int64 {
	if x != nil {
		return x.OrderTime
	}
	return 0
}

func (x *RollbackConsumeAssetRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *RollbackConsumeAssetRequest) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

type RollbackConsumeAssetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*OperateAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"` // 受影响的用户资产余额
}

func (x *RollbackConsumeAssetResponse) Reset() {
	*x = RollbackConsumeAssetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RollbackConsumeAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackConsumeAssetResponse) ProtoMessage() {}

func (x *RollbackConsumeAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackConsumeAssetResponse.ProtoReflect.Descriptor instead.
func (*RollbackConsumeAssetResponse) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{11}
}

func (x *RollbackConsumeAssetResponse) GetAssets() []*OperateAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

// 确认预消耗资产
type CommitConsumeAssetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid               int32          `protobuf:"varint,1,opt,name=bid,proto3" json:"bid,omitempty"`                                                         // 业务线ID
	AppId             string         `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                                         // appid
	AccountId         string         `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                             // 账户 ID
	UserId            string         `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                      // 用户 ID
	PreConsumeOrderId string         `protobuf:"bytes,5,opt,name=pre_consume_order_id,json=preConsumeOrderId,proto3" json:"pre_consume_order_id,omitempty"` // 预扣订单号 (40 字节)
	CommitAssets      []*CommitAsset `protobuf:"bytes,6,rep,name=commitAssets,proto3" json:"commitAssets,omitempty"`                                        //确认资产
	OrderTime         int64          `protobuf:"varint,7,opt,name=order_time,json=orderTime,proto3" json:"order_time,omitempty"`                            // 请求时间
	Remark            string         `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark,omitempty"`                                                    // 备注 (128 字节)
	Ext               string         `protobuf:"bytes,9,opt,name=ext,proto3" json:"ext,omitempty"`
}

func (x *CommitConsumeAssetRequest) Reset() {
	*x = CommitConsumeAssetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitConsumeAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitConsumeAssetRequest) ProtoMessage() {}

func (x *CommitConsumeAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitConsumeAssetRequest.ProtoReflect.Descriptor instead.
func (*CommitConsumeAssetRequest) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{12}
}

func (x *CommitConsumeAssetRequest) GetBid() int32 {
	if x != nil {
		return x.Bid
	}
	return 0
}

func (x *CommitConsumeAssetRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CommitConsumeAssetRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CommitConsumeAssetRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CommitConsumeAssetRequest) GetPreConsumeOrderId() string {
	if x != nil {
		return x.PreConsumeOrderId
	}
	return ""
}

func (x *CommitConsumeAssetRequest) GetCommitAssets() []*CommitAsset {
	if x != nil {
		return x.CommitAssets
	}
	return nil
}

func (x *CommitConsumeAssetRequest) GetOrderTime() int64 {
	if x != nil {
		return x.OrderTime
	}
	return 0
}

func (x *CommitConsumeAssetRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *CommitConsumeAssetRequest) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

type CommitConsumeAssetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*OperateAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"` // 受影响的用户资产余额
}

func (x *CommitConsumeAssetResponse) Reset() {
	*x = CommitConsumeAssetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitConsumeAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitConsumeAssetResponse) ProtoMessage() {}

func (x *CommitConsumeAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitConsumeAssetResponse.ProtoReflect.Descriptor instead.
func (*CommitConsumeAssetResponse) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{13}
}

func (x *CommitConsumeAssetResponse) GetAssets() []*OperateAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

type OperateAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId   int32 `protobuf:"varint,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	ChangeNum int64 `protobuf:"varint,2,opt,name=change_num,json=changeNum,proto3" json:"change_num,omitempty"`
	AfterNum  int64 `protobuf:"varint,3,opt,name=after_num,json=afterNum,proto3" json:"after_num,omitempty"`
	// 变更用户余额信息 （包含子账户）
	ModifiedAssets []*ModifiedAsset `protobuf:"bytes,4,rep,name=modified_assets,json=modifiedAssets,proto3" json:"modified_assets,omitempty"`
	// 变更后用户余额信息（包含子账户）
	AfterAssets []*AfterAsset `protobuf:"bytes,5,rep,name=after_assets,json=afterAssets,proto3" json:"after_assets,omitempty"`
}

func (x *OperateAsset) Reset() {
	*x = OperateAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_api_v1_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperateAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperateAsset) ProtoMessage() {}

func (x *OperateAsset) ProtoReflect() protoreflect.Message {
	mi := &file_asset_api_v1_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperateAsset.ProtoReflect.Descriptor instead.
func (*OperateAsset) Descriptor() ([]byte, []int) {
	return file_asset_api_v1_api_proto_rawDescGZIP(), []int{14}
}

func (x *OperateAsset) GetAssetId() int32 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *OperateAsset) GetChangeNum() int64 {
	if x != nil {
		return x.ChangeNum
	}
	return 0
}

func (x *OperateAsset) GetAfterNum() int64 {
	if x != nil {
		return x.AfterNum
	}
	return 0
}

func (x *OperateAsset) GetModifiedAssets() []*ModifiedAsset {
	if x != nil {
		return x.ModifiedAssets
	}
	return nil
}

func (x *OperateAsset) GetAfterAssets() []*AfterAsset {
	if x != nil {
		return x.AfterAssets
	}
	return nil
}

var File_asset_api_v1_api_proto protoreflect.FileDescriptor

var file_asset_api_v1_api_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x1a, 0x19, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91, 0x01,
	0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64,
	0x73, 0x22, 0x45, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x22, 0x9d, 0x02, 0x0a, 0x13, 0x50, 0x72, 0x65,
	0x73, 0x65, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x62,
	0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0e,
	0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x52, 0x0d, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x78, 0x74, 0x22, 0x4a, 0x0a, 0x14, 0x50, 0x72, 0x65, 0x73,
	0x65, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x32, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x22, 0xe9, 0x02, 0x0a, 0x13, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x62, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4a, 0x0a, 0x0f, 0x72, 0x65, 0x64, 0x75,
	0x63, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x64, 0x75, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x64, 0x75, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x10,
	0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x78, 0x74,
	0x22, 0x4a, 0x0a, 0x14, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x22, 0xef, 0x01, 0x0a,
	0x19, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x10, 0x0a, 0x03,
	0x65, 0x78, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x78, 0x74, 0x22, 0x50,
	0x0a, 0x1a, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x06,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x22, 0xac, 0x03, 0x0a, 0x16, 0x50, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x62,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x14,
	0x70, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x72, 0x65, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4a, 0x0a,
	0x0f, 0x72, 0x65, 0x64, 0x75, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x64, 0x75, 0x63,
	0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x64, 0x75, 0x63,
	0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0d, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x63, 0x6f, 0x6d,
	0x6d, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x61, 0x75, 0x74, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x65, 0x78, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x78, 0x74, 0x22,
	0x4d, 0x0a, 0x17, 0x50, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x22, 0xf8,
	0x01, 0x0a, 0x1b, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x62, 0x69, 0x64,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x2f, 0x0a, 0x14, 0x70, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70,
	0x72, 0x65, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x78, 0x74, 0x22, 0x52, 0x0a, 0x1c, 0x52, 0x6f, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x22, 0xb5, 0x02,
	0x0a, 0x19, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x62,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x14,
	0x70, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x72, 0x65, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a,
	0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0c,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x65, 0x78, 0x74, 0x22, 0x50, 0x0a, 0x1a, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52,
	0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x22, 0xe8, 0x01, 0x0a, 0x0c, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4e,
	0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x66, 0x74, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12,
	0x44, 0x0a, 0x0f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0e, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x3b, 0x0a, 0x0c, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x66, 0x74, 0x65, 0x72,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x0b, 0x61, 0x66, 0x74, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x2a, 0xa9, 0x01, 0x0a, 0x13, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x64, 0x75,
	0x63, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x52, 0x45, 0x44, 0x55, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x52, 0x41, 0x54,
	0x45, 0x47, 0x59, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x20, 0x0a,
	0x1c, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x52, 0x45, 0x44, 0x55, 0x43, 0x45, 0x5f, 0x53, 0x54,
	0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x5f, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12,
	0x24, 0x0a, 0x20, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x52, 0x45, 0x44, 0x55, 0x43, 0x45, 0x5f,
	0x53, 0x54, 0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x5f,
	0x50, 0x41, 0x59, 0x10, 0x02, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x52,
	0x45, 0x44, 0x55, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x5f, 0x50,
	0x52, 0x49, 0x4f, 0x52, 0x5f, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x32, 0xbb,
	0x07, 0x0a, 0x0a, 0x41, 0x70, 0x69, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6d, 0x0a,
	0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x1f, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1c,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x62, 0x6f, 0x73, 0x73, 0x2f, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x78, 0x0a, 0x0c,
	0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x21, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x73,
	0x65, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x22, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16,
	0x2f, 0x62, 0x6f, 0x73, 0x73, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x78, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x21, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x21, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f, 0x62, 0x6f, 0x73, 0x73, 0x2f,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x12, 0x85, 0x01, 0x0a, 0x0f, 0x50, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x12, 0x24, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x62,
	0x6f, 0x73, 0x73, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x14, 0x52, 0x6f, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x12, 0x29, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24,
	0x3a, 0x01, 0x2a, 0x22, 0x1f, 0x2f, 0x62, 0x6f, 0x73, 0x73, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x12, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x27, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69,
	0x74, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x28,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f, 0x62, 0x6f, 0x73, 0x73,
	0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12,
	0x27, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f,
	0x62, 0x6f, 0x73, 0x73, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x42, 0x4b, 0x5a, 0x49,
	0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x63, 0x6f, 0x72, 0x70, 0x2f,
	0x74, 0x6d, 0x65, 0x5f, 0x62, 0x6f, 0x73, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x3b, 0x63, 0x6f, 0x72, 0x65, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_asset_api_v1_api_proto_rawDescOnce sync.Once
	file_asset_api_v1_api_proto_rawDescData = file_asset_api_v1_api_proto_rawDesc
)

func file_asset_api_v1_api_proto_rawDescGZIP() []byte {
	file_asset_api_v1_api_proto_rawDescOnce.Do(func() {
		file_asset_api_v1_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_asset_api_v1_api_proto_rawDescData)
	})
	return file_asset_api_v1_api_proto_rawDescData
}

var file_asset_api_v1_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_asset_api_v1_api_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_asset_api_v1_api_proto_goTypes = []interface{}{
	(AssetReduceStrategy)(0),             // 0: asset.api.v1.AssetReduceStrategy
	(*QueryAssetRequest)(nil),            // 1: asset.api.v1.QueryAssetRequest
	(*QueryAssetResponse)(nil),           // 2: asset.api.v1.QueryAssetResponse
	(*PresentAssetRequest)(nil),          // 3: asset.api.v1.PresentAssetRequest
	(*PresentAssetResponse)(nil),         // 4: asset.api.v1.PresentAssetResponse
	(*ConsumeAssetRequest)(nil),          // 5: asset.api.v1.ConsumeAssetRequest
	(*ConsumeAssetResponse)(nil),         // 6: asset.api.v1.ConsumeAssetResponse
	(*RefundConsumeAssetRequest)(nil),    // 7: asset.api.v1.RefundConsumeAssetRequest
	(*RefundConsumeAssetResponse)(nil),   // 8: asset.api.v1.RefundConsumeAssetResponse
	(*PreConsumeAssetRequest)(nil),       // 9: asset.api.v1.PreConsumeAssetRequest
	(*PreConsumeAssetResponse)(nil),      // 10: asset.api.v1.PreConsumeAssetResponse
	(*RollbackConsumeAssetRequest)(nil),  // 11: asset.api.v1.RollbackConsumeAssetRequest
	(*RollbackConsumeAssetResponse)(nil), // 12: asset.api.v1.RollbackConsumeAssetResponse
	(*CommitConsumeAssetRequest)(nil),    // 13: asset.api.v1.CommitConsumeAssetRequest
	(*CommitConsumeAssetResponse)(nil),   // 14: asset.api.v1.CommitConsumeAssetResponse
	(*OperateAsset)(nil),                 // 15: asset.api.v1.OperateAsset
	(*UserAsset)(nil),                    // 16: asset.api.v1.UserAsset
	(*PresentAsset)(nil),                 // 17: asset.api.v1.PresentAsset
	(*ConsumeAsset)(nil),                 // 18: asset.api.v1.ConsumeAsset
	(*CommitAsset)(nil),                  // 19: asset.api.v1.CommitAsset
	(*ModifiedAsset)(nil),                // 20: asset.api.v1.ModifiedAsset
	(*AfterAsset)(nil),                   // 21: asset.api.v1.AfterAsset
}
var file_asset_api_v1_api_proto_depIdxs = []int32{
	16, // 0: asset.api.v1.QueryAssetResponse.assets:type_name -> asset.api.v1.UserAsset
	17, // 1: asset.api.v1.PresentAssetRequest.present_assets:type_name -> asset.api.v1.PresentAsset
	15, // 2: asset.api.v1.PresentAssetResponse.assets:type_name -> asset.api.v1.OperateAsset
	0,  // 3: asset.api.v1.ConsumeAssetRequest.reduce_strategy:type_name -> asset.api.v1.AssetReduceStrategy
	18, // 4: asset.api.v1.ConsumeAssetRequest.consume_assets:type_name -> asset.api.v1.ConsumeAsset
	15, // 5: asset.api.v1.ConsumeAssetResponse.assets:type_name -> asset.api.v1.OperateAsset
	15, // 6: asset.api.v1.RefundConsumeAssetResponse.assets:type_name -> asset.api.v1.OperateAsset
	0,  // 7: asset.api.v1.PreConsumeAssetRequest.reduce_strategy:type_name -> asset.api.v1.AssetReduceStrategy
	18, // 8: asset.api.v1.PreConsumeAssetRequest.consume_assets:type_name -> asset.api.v1.ConsumeAsset
	15, // 9: asset.api.v1.PreConsumeAssetResponse.assets:type_name -> asset.api.v1.OperateAsset
	15, // 10: asset.api.v1.RollbackConsumeAssetResponse.assets:type_name -> asset.api.v1.OperateAsset
	19, // 11: asset.api.v1.CommitConsumeAssetRequest.commitAssets:type_name -> asset.api.v1.CommitAsset
	15, // 12: asset.api.v1.CommitConsumeAssetResponse.assets:type_name -> asset.api.v1.OperateAsset
	20, // 13: asset.api.v1.OperateAsset.modified_assets:type_name -> asset.api.v1.ModifiedAsset
	21, // 14: asset.api.v1.OperateAsset.after_assets:type_name -> asset.api.v1.AfterAsset
	1,  // 15: asset.api.v1.ApiService.QueryAsset:input_type -> asset.api.v1.QueryAssetRequest
	3,  // 16: asset.api.v1.ApiService.PresentAsset:input_type -> asset.api.v1.PresentAssetRequest
	5,  // 17: asset.api.v1.ApiService.ConsumeAsset:input_type -> asset.api.v1.ConsumeAssetRequest
	9,  // 18: asset.api.v1.ApiService.PreConsumeAsset:input_type -> asset.api.v1.PreConsumeAssetRequest
	11, // 19: asset.api.v1.ApiService.RollbackConsumeAsset:input_type -> asset.api.v1.RollbackConsumeAssetRequest
	13, // 20: asset.api.v1.ApiService.CommitConsumeAsset:input_type -> asset.api.v1.CommitConsumeAssetRequest
	7,  // 21: asset.api.v1.ApiService.RefundConsumeAsset:input_type -> asset.api.v1.RefundConsumeAssetRequest
	2,  // 22: asset.api.v1.ApiService.QueryAsset:output_type -> asset.api.v1.QueryAssetResponse
	4,  // 23: asset.api.v1.ApiService.PresentAsset:output_type -> asset.api.v1.PresentAssetResponse
	6,  // 24: asset.api.v1.ApiService.ConsumeAsset:output_type -> asset.api.v1.ConsumeAssetResponse
	10, // 25: asset.api.v1.ApiService.PreConsumeAsset:output_type -> asset.api.v1.PreConsumeAssetResponse
	12, // 26: asset.api.v1.ApiService.RollbackConsumeAsset:output_type -> asset.api.v1.RollbackConsumeAssetResponse
	14, // 27: asset.api.v1.ApiService.CommitConsumeAsset:output_type -> asset.api.v1.CommitConsumeAssetResponse
	8,  // 28: asset.api.v1.ApiService.RefundConsumeAsset:output_type -> asset.api.v1.RefundConsumeAssetResponse
	22, // [22:29] is the sub-list for method output_type
	15, // [15:22] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_asset_api_v1_api_proto_init() }
func file_asset_api_v1_api_proto_init() {
	if File_asset_api_v1_api_proto != nil {
		return
	}
	file_asset_api_v1_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_asset_api_v1_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryAssetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryAssetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PresentAssetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PresentAssetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeAssetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeAssetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundConsumeAssetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundConsumeAssetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreConsumeAssetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreConsumeAssetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RollbackConsumeAssetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RollbackConsumeAssetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitConsumeAssetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitConsumeAssetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_api_v1_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperateAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_asset_api_v1_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_asset_api_v1_api_proto_goTypes,
		DependencyIndexes: file_asset_api_v1_api_proto_depIdxs,
		EnumInfos:         file_asset_api_v1_api_proto_enumTypes,
		MessageInfos:      file_asset_api_v1_api_proto_msgTypes,
	}.Build()
	File_asset_api_v1_api_proto = out.File
	file_asset_api_v1_api_proto_rawDesc = nil
	file_asset_api_v1_api_proto_goTypes = nil
	file_asset_api_v1_api_proto_depIdxs = nil
}
