// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.27.0--rc1
// source: asset/api/v1/api.proto

package corev1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ApiService_QueryAsset_FullMethodName           = "/asset.api.v1.ApiService/QueryAsset"
	ApiService_PresentAsset_FullMethodName         = "/asset.api.v1.ApiService/PresentAsset"
	ApiService_ConsumeAsset_FullMethodName         = "/asset.api.v1.ApiService/ConsumeAsset"
	ApiService_PreConsumeAsset_FullMethodName      = "/asset.api.v1.ApiService/PreConsumeAsset"
	ApiService_RollbackConsumeAsset_FullMethodName = "/asset.api.v1.ApiService/RollbackConsumeAsset"
	ApiService_CommitConsumeAsset_FullMethodName   = "/asset.api.v1.ApiService/CommitConsumeAsset"
	ApiService_RefundConsumeAsset_FullMethodName   = "/asset.api.v1.ApiService/RefundConsumeAsset"
)

// ApiServiceClient is the client API for ApiService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ApiServiceClient interface {
	QueryAsset(ctx context.Context, in *QueryAssetRequest, opts ...grpc.CallOption) (*QueryAssetResponse, error)
	PresentAsset(ctx context.Context, in *PresentAssetRequest, opts ...grpc.CallOption) (*PresentAssetResponse, error)
	ConsumeAsset(ctx context.Context, in *ConsumeAssetRequest, opts ...grpc.CallOption) (*ConsumeAssetResponse, error)
	PreConsumeAsset(ctx context.Context, in *PreConsumeAssetRequest, opts ...grpc.CallOption) (*PreConsumeAssetResponse, error)
	RollbackConsumeAsset(ctx context.Context, in *RollbackConsumeAssetRequest, opts ...grpc.CallOption) (*RollbackConsumeAssetResponse, error)
	CommitConsumeAsset(ctx context.Context, in *CommitConsumeAssetRequest, opts ...grpc.CallOption) (*CommitConsumeAssetResponse, error)
	RefundConsumeAsset(ctx context.Context, in *RefundConsumeAssetRequest, opts ...grpc.CallOption) (*RefundConsumeAssetResponse, error)
}

type apiServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewApiServiceClient(cc grpc.ClientConnInterface) ApiServiceClient {
	return &apiServiceClient{cc}
}

func (c *apiServiceClient) QueryAsset(ctx context.Context, in *QueryAssetRequest, opts ...grpc.CallOption) (*QueryAssetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryAssetResponse)
	err := c.cc.Invoke(ctx, ApiService_QueryAsset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiServiceClient) PresentAsset(ctx context.Context, in *PresentAssetRequest, opts ...grpc.CallOption) (*PresentAssetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PresentAssetResponse)
	err := c.cc.Invoke(ctx, ApiService_PresentAsset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiServiceClient) ConsumeAsset(ctx context.Context, in *ConsumeAssetRequest, opts ...grpc.CallOption) (*ConsumeAssetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConsumeAssetResponse)
	err := c.cc.Invoke(ctx, ApiService_ConsumeAsset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiServiceClient) PreConsumeAsset(ctx context.Context, in *PreConsumeAssetRequest, opts ...grpc.CallOption) (*PreConsumeAssetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PreConsumeAssetResponse)
	err := c.cc.Invoke(ctx, ApiService_PreConsumeAsset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiServiceClient) RollbackConsumeAsset(ctx context.Context, in *RollbackConsumeAssetRequest, opts ...grpc.CallOption) (*RollbackConsumeAssetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RollbackConsumeAssetResponse)
	err := c.cc.Invoke(ctx, ApiService_RollbackConsumeAsset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiServiceClient) CommitConsumeAsset(ctx context.Context, in *CommitConsumeAssetRequest, opts ...grpc.CallOption) (*CommitConsumeAssetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommitConsumeAssetResponse)
	err := c.cc.Invoke(ctx, ApiService_CommitConsumeAsset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiServiceClient) RefundConsumeAsset(ctx context.Context, in *RefundConsumeAssetRequest, opts ...grpc.CallOption) (*RefundConsumeAssetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefundConsumeAssetResponse)
	err := c.cc.Invoke(ctx, ApiService_RefundConsumeAsset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApiServiceServer is the server API for ApiService service.
// All implementations must embed UnimplementedApiServiceServer
// for forward compatibility.
type ApiServiceServer interface {
	QueryAsset(context.Context, *QueryAssetRequest) (*QueryAssetResponse, error)
	PresentAsset(context.Context, *PresentAssetRequest) (*PresentAssetResponse, error)
	ConsumeAsset(context.Context, *ConsumeAssetRequest) (*ConsumeAssetResponse, error)
	PreConsumeAsset(context.Context, *PreConsumeAssetRequest) (*PreConsumeAssetResponse, error)
	RollbackConsumeAsset(context.Context, *RollbackConsumeAssetRequest) (*RollbackConsumeAssetResponse, error)
	CommitConsumeAsset(context.Context, *CommitConsumeAssetRequest) (*CommitConsumeAssetResponse, error)
	RefundConsumeAsset(context.Context, *RefundConsumeAssetRequest) (*RefundConsumeAssetResponse, error)
	mustEmbedUnimplementedApiServiceServer()
}

// UnimplementedApiServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedApiServiceServer struct{}

func (UnimplementedApiServiceServer) QueryAsset(context.Context, *QueryAssetRequest) (*QueryAssetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryAsset not implemented")
}
func (UnimplementedApiServiceServer) PresentAsset(context.Context, *PresentAssetRequest) (*PresentAssetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PresentAsset not implemented")
}
func (UnimplementedApiServiceServer) ConsumeAsset(context.Context, *ConsumeAssetRequest) (*ConsumeAssetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeAsset not implemented")
}
func (UnimplementedApiServiceServer) PreConsumeAsset(context.Context, *PreConsumeAssetRequest) (*PreConsumeAssetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreConsumeAsset not implemented")
}
func (UnimplementedApiServiceServer) RollbackConsumeAsset(context.Context, *RollbackConsumeAssetRequest) (*RollbackConsumeAssetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RollbackConsumeAsset not implemented")
}
func (UnimplementedApiServiceServer) CommitConsumeAsset(context.Context, *CommitConsumeAssetRequest) (*CommitConsumeAssetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommitConsumeAsset not implemented")
}
func (UnimplementedApiServiceServer) RefundConsumeAsset(context.Context, *RefundConsumeAssetRequest) (*RefundConsumeAssetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefundConsumeAsset not implemented")
}
func (UnimplementedApiServiceServer) mustEmbedUnimplementedApiServiceServer() {}
func (UnimplementedApiServiceServer) testEmbeddedByValue()                    {}

// UnsafeApiServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApiServiceServer will
// result in compilation errors.
type UnsafeApiServiceServer interface {
	mustEmbedUnimplementedApiServiceServer()
}

func RegisterApiServiceServer(s grpc.ServiceRegistrar, srv ApiServiceServer) {
	// If the following call pancis, it indicates UnimplementedApiServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ApiService_ServiceDesc, srv)
}

func _ApiService_QueryAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServiceServer).QueryAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiService_QueryAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServiceServer).QueryAsset(ctx, req.(*QueryAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiService_PresentAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PresentAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServiceServer).PresentAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiService_PresentAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServiceServer).PresentAsset(ctx, req.(*PresentAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiService_ConsumeAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConsumeAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServiceServer).ConsumeAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiService_ConsumeAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServiceServer).ConsumeAsset(ctx, req.(*ConsumeAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiService_PreConsumeAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreConsumeAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServiceServer).PreConsumeAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiService_PreConsumeAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServiceServer).PreConsumeAsset(ctx, req.(*PreConsumeAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiService_RollbackConsumeAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RollbackConsumeAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServiceServer).RollbackConsumeAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiService_RollbackConsumeAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServiceServer).RollbackConsumeAsset(ctx, req.(*RollbackConsumeAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiService_CommitConsumeAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommitConsumeAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServiceServer).CommitConsumeAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiService_CommitConsumeAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServiceServer).CommitConsumeAsset(ctx, req.(*CommitConsumeAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiService_RefundConsumeAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundConsumeAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServiceServer).RefundConsumeAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiService_RefundConsumeAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServiceServer).RefundConsumeAsset(ctx, req.(*RefundConsumeAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ApiService_ServiceDesc is the grpc.ServiceDesc for ApiService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ApiService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "asset.api.v1.ApiService",
	HandlerType: (*ApiServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryAsset",
			Handler:    _ApiService_QueryAsset_Handler,
		},
		{
			MethodName: "PresentAsset",
			Handler:    _ApiService_PresentAsset_Handler,
		},
		{
			MethodName: "ConsumeAsset",
			Handler:    _ApiService_ConsumeAsset_Handler,
		},
		{
			MethodName: "PreConsumeAsset",
			Handler:    _ApiService_PreConsumeAsset_Handler,
		},
		{
			MethodName: "RollbackConsumeAsset",
			Handler:    _ApiService_RollbackConsumeAsset_Handler,
		},
		{
			MethodName: "CommitConsumeAsset",
			Handler:    _ApiService_CommitConsumeAsset_Handler,
		},
		{
			MethodName: "RefundConsumeAsset",
			Handler:    _ApiService_RefundConsumeAsset_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "asset/api/v1/api.proto",
}
