package impl

import (
	"cnb.tmeoa.com/tme_bmp/component/app/rank_reconcile_svr/internal/service"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/rank_reconcile"
	"context"
)

// ZIncrby
func (impl *RankReconcileImpl) ZIncrby(ctx context.Context, req *rank_reconcile.ZIncrbyReq) (*rank_reconcile.ZIncrbyRsp, error) {
	return &rank_reconcile.ZIncrbyRsp{}, service.GetService().CalProcess(ctx, req.ConfID, rank_reconcile.ScoreType_ZIncrby, req.AddScoreList)
}
