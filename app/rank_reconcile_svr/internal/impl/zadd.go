package impl

import (
	"cnb.tmeoa.com/tme_bmp/component/app/rank_reconcile_svr/internal/service"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/rank_reconcile"
	"context"
)

// ZAdd 接口
func (impl *RankReconcileImpl) ZAdd(ctx context.Context, req *rank_reconcile.ZAddReq) (*rank_reconcile.ZAddRsp, error) {
	return &rank_reconcile.ZAddRsp{}, service.GetService().CalProcess(ctx, req.ConfID, rank_reconcile.ScoreType_ZAdd, req.ZAddItem)
}
