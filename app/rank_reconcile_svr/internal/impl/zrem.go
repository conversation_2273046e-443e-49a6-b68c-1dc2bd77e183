package impl

import (
	"cnb.tmeoa.com/tme_bmp/component/app/rank_reconcile_svr/internal/service"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/rank_reconcile"
	"context"
)

func (impl *RankReconcileImpl) ZRem(ctx context.Context, req *rank_reconcile.ZRemReq) (*rank_reconcile.ZRemRsp, error) {
	return &rank_reconcile.ZRemRsp{}, service.GetService().CalProcess(ctx, req.ConfID, rank_reconcile.ScoreType_ZRem, req.ZRemItem)
}
