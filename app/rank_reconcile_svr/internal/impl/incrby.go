package impl

import (
	"cnb.tmeoa.com/tme_bmp/component/app/rank_reconcile_svr/internal/service"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/rank_reconcile"
	"context"
)

// Incrby
func (impl *RankReconcileImpl) Incrby(ctx context.Context, req *rank_reconcile.IncrbyReq) (*rank_reconcile.IncrbyRsp, error) {
	return &rank_reconcile.IncrbyRsp{}, service.GetService().CalProcess(ctx, req.ConfID, rank_reconcile.ScoreType_Incrby, req.AddScoreList)
}
