package service

import (
	"context"
	"fmt"

	"cnb.tmeoa.com/tme_bmp/component/app/rank_reconcile_svr/internal/dao"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/rank_reconcile"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	queuePulsar "cnb.tmeoa.com/tme_bmp/tme_bmp_plib/queue/pulsar"
)

// AsyncWrite 异步写(发消息)
func (s *Service) AsyncWrite(ctx context.Context, reconciliationConfig rank_reconcile.ReconciliationConfig,
	Dao dao.RedisDao, scoreType rank_reconcile.ScoreType, args interface{}) error {
	msg := rank_reconcile.RankKeyAddScoreMsg{
		ConfID:      reconciliationConfig.ConfID,
		ScoreType:   scoreType,
		WriteBackup: !reconciliationConfig.SwitchBackupToMaster,
	}
	switch scoreType {
	case rank_reconcile.ScoreType_ZIncrby:
		itemList, ok := args.([]rank_reconcile.RankKeyZIncrbyItem)
		if !ok {
			log.Error("type error", "scoreType", scoreType, "args", args)
			return fmt.Errorf("type error")
		}
		for index := range itemList {
			msg.ZIncrbyList = append(msg.ZIncrbyList, &itemList[index])
		}
	case rank_reconcile.ScoreType_Incrby:
		itemList, ok := args.([]rank_reconcile.KeyIncrbyItem)
		if !ok {
			log.Error("type error", "scoreType", scoreType, "args", args)
			return fmt.Errorf("type error")
		}
		for index := range itemList {
			msg.IncrbyList = append(msg.IncrbyList, &itemList[index])
		}
	case rank_reconcile.ScoreType_ZAdd:
		item, ok := args.(rank_reconcile.ZAddItem)
		if !ok {
			log.Error("type error", "scoreType", scoreType, "args", args)
			return fmt.Errorf("type error")
		}
		msg.ZAddItem = &item
	case rank_reconcile.ScoreType_ZRem:
		item, ok := args.(rank_reconcile.ZRemItem)
		if !ok {
			log.Error("type error", "scoreType", scoreType, "args", args)
			return fmt.Errorf("type error")
		}
		msg.ZRemItem = &item
	default:
		return fmt.Errorf("scoreType=%d invalid", scoreType)
	}
	//发消息
	err := queuePulsar.SendPulsarProtoMessage(ctx, &msg, fmt.Sprintf("pulsar_%d", reconciliationConfig.ConfID), "todo")
	if err != nil {
		log.Error("send pulsar message fail", "error", err, "msg", msg)
		return err
	}
	log.Info("send pulsar message succ", "msg", msg)
	return nil
}

// SyncWrite 同步写(异步写最终也调这个方法,如果异步写是发消息)
func (s *Service) SyncWrite(ctx context.Context, reconciliationConfig rank_reconcile.ReconciliationConfig,
	Dao dao.RedisDao, scoreType rank_reconcile.ScoreType, args interface{}) error {
	switch scoreType {
	case rank_reconcile.ScoreType_ZIncrby:
		item, ok := args.([]rank_reconcile.RankKeyZIncrbyItem)
		if !ok {
			log.Error("type error", "scoreType", scoreType, "args", args)
			return fmt.Errorf("type error")
		}
		return Dao.ZIncrby(ctx, item)
	case rank_reconcile.ScoreType_Incrby:
		item, ok := args.([]rank_reconcile.KeyIncrbyItem)
		if !ok {
			log.Error("type error", "scoreType", scoreType, "args", args)
			return fmt.Errorf("type error")
		}
		return Dao.Incrby(ctx, item)
	case rank_reconcile.ScoreType_ZAdd:
		item, ok := args.(rank_reconcile.ZAddItem)
		if !ok {
			log.Error("type error", "scoreType", scoreType, "args", args)
			return fmt.Errorf("type error")
		}
		return Dao.ZAdd(ctx, item)
	case rank_reconcile.ScoreType_ZRem:
		item, ok := args.(rank_reconcile.ZRemItem)
		if !ok {
			log.Error("type error", "scoreType", scoreType, "args", args)
			return fmt.Errorf("type error")
		}
		return Dao.ZRem(ctx, item)
	}
	return fmt.Errorf("scoreType=%d invalid", scoreType)
}

// getAndInitDaoCli 获取并初始化存储客户端
func (s *Service) getAndInitDaoCli(config *rank_reconcile.CkvPlusConfig) (dao.RedisDao, error) {
	l5 := fmt.Sprintf("%d:%d", config.GetMod(), config.GetCmd())
	if st, exists := s.daoMap[l5]; exists {
		return st, nil
	}
	s.daoInitLock.Lock()
	defer s.daoInitLock.Unlock()
	if st, exists := s.daoMap[l5]; exists {
		return st, nil
	}
	st, err := dao.NewRedisDao(config)
	if err != nil {
		log.Error("init dao fail", "err", err, "config", config)
		return st, err
	}
	s.daoMap[l5] = st
	return st, nil
}

// CalProcess 计算主流程
func (s *Service) CalProcess(ctx context.Context, confID int32, scoreType rank_reconcile.ScoreType, args interface{}) error {
	reconciliationConfig, exists := s.configCache.GetOne(int64(confID))
	if !exists {
		return fmt.Errorf("confID=%d not exists", confID)
	}
	masterConfig, backupConfig := reconciliationConfig.WriteMaster, reconciliationConfig.WriteBackup
	if reconciliationConfig.SwitchBackupToMaster { //主备切换
		masterConfig, backupConfig = backupConfig, masterConfig
	}
	//写主
	switch reconciliationConfig.WriteMasterType {
	case rank_reconcile.WriteMasterType_WriteMasterTypeNot: //不写主
	case rank_reconcile.WriteMasterType_WriteMasterTypeSync: //同步写主
		Dao, err := s.getAndInitDaoCli(masterConfig)
		if err != nil {
			return err
		}
		return s.SyncWrite(ctx, reconciliationConfig, Dao, scoreType, args)
	default:
		return fmt.Errorf("unsupport writeMasterType=%d", reconciliationConfig.WriteMasterType)
	}
	//写备
	switch reconciliationConfig.WriteBackupType {
	case rank_reconcile.WriteBackupType_WriteBackupTypeNot: //不写备
	case rank_reconcile.WriteBackupType_WriteBackupTypeAsyn: //异步写备, 发消息
		Dao, err := s.getAndInitDaoCli(backupConfig)
		if err != nil {
			return err
		}
		return s.AsyncWrite(ctx, reconciliationConfig, Dao, scoreType, args)
	default:
		return fmt.Errorf("unsupport WriteBackupType=%d", reconciliationConfig.WriteBackupType)
	}
	return nil
}
