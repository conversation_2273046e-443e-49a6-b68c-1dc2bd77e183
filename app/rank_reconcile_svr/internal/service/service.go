package service

import (
	"fmt"
	"sync"
	"time"

	"cnb.tmeoa.com/tme_bmp/component/app/rank_reconcile_svr/internal/config"
	"cnb.tmeoa.com/tme_bmp/component/app/rank_reconcile_svr/internal/dao"
	config_cache "cnb.tmeoa.com/tme_bmp/component/common/config"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/rank_reconcile"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/redis"
)

// Service struct.
type Service struct {
	config      *config.Config
	configCache *config_cache.ConfigCache[rank_reconcile.ReconciliationConfig]
	redisCli    *redis.Client
	mongoDao    dao.MongoDao //存储
	daoInitLock sync.Mutex
	daoMap      map[string]dao.RedisDao //l5->redis
}

var s *Service

// New creates a new Service.
func New(c *config.Config) error {
	//redisCli
	redisCli, err := redis.NewClient(c.RedisConfig)
	if err != nil {
		return fmt.Errorf("redisCli err=%v, config=%+v", err, c.RedisConfig)
	}
	// new mongoDao
	mongoDao, err := dao.NewMongoDao(c)
	if err != nil {
		return err
	}
	//配置缓存
	configService := config_cache.NewConfigService[rank_reconcile.ReconciliationConfig](redisCli, "todo", "todo")
	configCache := config_cache.NewConfigCache(10*time.Second, &configService)
	s = &Service{
		config:      c,
		configCache: configCache,
		redisCli:    redisCli,
		mongoDao:    mongoDao,
	}
	return nil
}

func GetService() *Service {
	return s
}

func (s *Service) GetConfigCache() *config_cache.ConfigCache[rank_reconcile.ReconciliationConfig] {
	return s.configCache
}

func (s *Service) GetRedisCli() *redis.Client {
	return s.redisCli
}

func (s *Service) GetMongoDao() dao.MongoDao {
	return s.mongoDao
}
