package config

import (
	"flag"

	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/karaoke"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log/accesslog"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log/paniclog"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/redis"
	"github.com/BurntSushi/toml"
)

var (
	configPath string
)

func init() {
	flag.StringVar(&configPath, "config", "../conf/config.toml", "config file path")
}

// Config is the configuration for the service.
type Config struct {
	Server      *karaoke.ServerConfig
	Log         *log.Config
	AccessLog   *accesslog.Config
	PanicLog    *paniclog.Config
	RedisConfig *redis.Config
	MongoConfig struct {
		Path        string `default:"../conf/config.toml"`
		ServiceName string `default:"mongo.bmp.quick_gift_record"`
	}
	PulsarConfig *PulsarConfig
}

type PulsarConfig struct {
	ConsumeService string
}

// Load loads the configuration.
func Load() (*Config, error) {
	c := &Config{}
	_, err := toml.DecodeFile(configPath, c)
	return c, err
}
