package mq

import (
	"context"

	"cnb.tmeoa.com/tme_bmp/component/app/rank_reconcile_svr/internal/config"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/activity_standard_msg"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	queuePulsar "cnb.tmeoa.com/tme_bmp/tme_bmp_plib/queue/pulsar"
	"github.com/apache/pulsar-client-go/pulsar"
	"google.golang.org/protobuf/proto"
)

var ser *Service

type Service struct {
}

// 送礼消息
func (mq *Service) consumeGiftMsg(message pulsar.Message) error {
	standardGiftMsg := &activity_standard_msg.StandardGiftMsg{}
	err := proto.Unmarshal(message.Payload(), standardGiftMsg)
	if err != nil {
		log.Errorf("invalid payload, msg=%+v, err=%v", message, err)
		return nil
	}
	log.Infof("giftMsg=%+v", standardGiftMsg)
	// 插入Mongo
	return nil
}

func (mq *Service) PulsarConsumeFunc(message pulsar.Message) error {
	switch message.Properties()["type"] {
	case activity_standard_msg.MsgType_MsgTypeStandardGift.String(): //礼物消息类型
		return mq.consumeGiftMsg(message)
	}
	return nil
}

func Init(c *config.Config) {
	queuePulsar.LoadConfig("../conf/pulsar.toml")
	log.Info("Start consume Pulsar...")
	mq := &Service{}

	queuePulsar.MustConsume(c.PulsarConfig.ConsumeService, func(ctx context.Context, message pulsar.Message, args ...interface{}) int {
		if err := mq.PulsarConsumeFunc(message); err != nil {
			return 1
		}
		return 0
	}, nil)
}
