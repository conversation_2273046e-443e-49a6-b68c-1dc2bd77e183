package dao

import (
	"context"

	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/database/mgo"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
)

func dataBaseName() string {
	return "standard_msg"
}

func collectName() string {
	return "standard_gift_record"
}

type StandardGiftRecord struct {
	UserId         int64  `bson:"user_id" json:"user_id"`
	AnchorId       int64  `bson:"anchor_id" json:"anchor_id"`
	GuestId        int64  `bson:"guest_id" json:"guest_id"`
	Price          int32  `bson:"price" json:"price"`
	Num            int32  `bson:"num" json:"num"`
	GiftId         int32  `bson:"gift_id" json:"gift_id"`
	Ts             int64  `bson:"ts" json:"ts"`
	BillNo         string `bson:"bill_no" json:"bill_no"`
	TrackGroupType int32  `bson:"track_group_type" json:"track_group_type"`
	GiftType       int32  `bson:"gift_type" json:"gift_type"`
	IsAnonymous    bool   `bson:"is_anonymous" json:"is_anonymous"`
	StrRoomId      string `bson:"str_room_id" json:"str_room_id"`
	StrShowId      string `bson:"str_show_id" json:"str_show_id"`
}

type mongoDao struct {
	mongo *mgo.Client
}

// UpsertRecord 新增or修改记录
func (s *mongoDao) UpsertRecord(ctx *context.Context, record *StandardGiftRecord) error {
	info, err := s.mongo.Upsert(*ctx, dataBaseName(), collectName(), record, record)
	if err != nil {
		log.Errorf("Upsert fail err:%+v record:%+v", err, record)
		return err
	}
	log.Infof("success Upsert record:%+v info:%+v", record, info)
	return nil
}
