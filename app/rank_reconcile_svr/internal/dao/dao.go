package dao

import (
	"context"
	"fmt"

	"cnb.tmeoa.com/tme_bmp/component/app/rank_reconcile_svr/internal/config"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/rank_reconcile"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/database/mgo"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/database/redis"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
)

// RedisDao is an abstract interface to the persistence mechanism.
type RedisDao interface {
	ZIncrby(ctx context.Context, itemList []rank_reconcile.RankKeyZIncrbyItem) error
	Incrby(ctx context.Context, itemList []rank_reconcile.KeyIncrbyItem) error
	ZAdd(ctx context.Context, item rank_reconcile.ZAddItem) error
	ZRem(ctx context.Context, item rank_reconcile.ZRemItem) error
}

// MongoDao is an abstract interface to the persistence mechanism.
type MongoDao interface {
	UpsertRecord(ctx *context.Context, record *StandardGiftRecord) error
}

// NewRedisDao returns an instance of Dao.
func NewRedisDao(config *rank_reconcile.CkvPlusConfig) (RedisDao, error) {
	redisConfig := &redis.Config{
		Mod:        config.GetMod(),
		Cmd:        config.GetCmd(),
		Password:   config.GetPasswd(),
		ServerName: "todo_rank_reconcile_svr",
	}
	redisCli, err := redis.NewClient(redisConfig)
	if err != nil {
		log.Error("new redisDao fail", "err", err, "config", config)
		return nil, err
	}
	return &redisDao{redisCli: redisCli}, nil
}

// NewMongoDao returns an instance of MongoDao.
func NewMongoDao(c *config.Config) (MongoDao, error) {
	mgo.ConfigPath = c.MongoConfig.Path
	mongo, err := mgo.NewClient(c.MongoConfig.ServiceName)
	if err != nil {
		log.Fatalf("mgo init fail err:%+v", err)
		return nil, err
	}
	return &mongoDao{
		mongo: mongo,
	}, nil
}

// NewRedisCli 新建redis Cli
func NewRedisCli(redisConf *redis.Config) (*redis.Client, error) {
	redisCli, err := redis.NewClient(redisConf)
	if err != nil {
		return redisCli, fmt.Errorf("redisCli err=%v, config=%+v", err, redisConf)
	}
	return redisCli, nil
}
