package dao

import (
	"context"

	"cnb.tmeoa.com/tme_bmp/component/common/lua"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go/pb/component/rank_reconcile"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/redis"
	v8 "github.com/go-redis/redis/v8"
	"golang.org/x/sync/errgroup"
)

type redisDao struct {
	redisCli *redis.Client
}

// ZIncrby 批量操作
func (s *redisDao) ZIncrby(ctx context.Context, itemList []rank_reconcile.RankKeyZIncrbyItem) error {
	eg := errgroup.Group{}
	for index := range itemList {
		item := itemList[index]
		eg.Go(func() error {
			return s.zincrby(ctx, item)
		})
	}
	return eg.Wait()
}

// zincrby 单条zincrby
func (s *redisDao) zincrby(ctx context.Context, item rank_reconcile.RankKeyZIncrbyItem) error {
	curScore, err := lua.NxZincrByWithScore(ctx, s.redisCli, item.GetBillNo(),
		item.GetRankKey(), item.GetAddScore(), item.GetToUid())
	if err != nil {
		log.Error("zincrby fail", "err", err, "item", item)
		return err
	}
	log.Info("zincrby succ", "item", item, "curScore", curScore)
	return nil
}

// Incrby 批量
func (s *redisDao) Incrby(ctx context.Context, itemList []rank_reconcile.KeyIncrbyItem) error {
	eg := errgroup.Group{}
	for index := range itemList {
		item := itemList[index]
		eg.Go(func() error {
			return s.incrby(ctx, item)
		})
	}
	return eg.Wait()
}

// incrby 单条incrby
func (s *redisDao) incrby(ctx context.Context, item rank_reconcile.KeyIncrbyItem) error {
	curScore, err := lua.NxIncrBy(ctx, s.redisCli, item.GetBillNo(), item.GetKey(), item.GetAddScore())
	if err != nil {
		log.Error("incrby fail", "err", err, "item", item)
		return err
	}
	log.Info("incrby succ", "item", item, "curScore", curScore)
	return nil
}

// Zadd
func (s *redisDao) ZAdd(ctx context.Context, item rank_reconcile.ZAddItem) error {
	if len(item.ZList) == 0 {
		return nil
	}
	agrs := v8.ZAddArgs{NX: true}
	for _, zItem := range item.ZList {
		agrs.Members = append(agrs.Members, v8.Z{Score: float64(zItem.GetScore()), Member: zItem.GetMember()})
	}
	num, err := s.redisCli.ZAdd(ctx, item.GetRankKey(), agrs).Result()
	if err != nil {
		log.Error("zadd fail", "err", err, "item", item)
		return err
	}
	log.Info("zincrby succ", "num", num, "item", item)
	return nil
}

// Zrem
func (s *redisDao) ZRem(ctx context.Context, item rank_reconcile.ZRemItem) error {
	//todo
	return nil
}
