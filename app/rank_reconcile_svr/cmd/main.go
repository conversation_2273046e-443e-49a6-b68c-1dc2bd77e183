package main

import (
	"flag"

	"cnb.tmeoa.com/tme_bmp/component/app/rank_reconcile_svr/internal/impl"

	"cnb.tmeoa.com/tme_bmp/component/app/rank_reconcile_svr/internal/config"
	"cnb.tmeoa.com/tme_bmp/component/app/rank_reconcile_svr/internal/mq"
	"cnb.tmeoa.com/tme_bmp/component/app/rank_reconcile_svr/internal/service"
	"cnb.tmeoa.com/tme_bmp/component/app/rank_reconcile_svr/internal/timer"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/grpc/interceptor/logging"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/grpc/interceptor/metric"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/grpc/interceptor/recovery"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/karaoke"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/karaoke/transport/grpc"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log/accesslog"
	"cnb.tmeoa.com/tme_bmp/tme_bmp_plib/log/paniclog"
	rpc "google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

func main() {
	flag.Parse()

	c, err := config.Load()
	if err != nil {
		panic(err)
	}

	log.Init(c.Log)
	accesslog.Init(c.AccessLog)
	paniclog.Init(c.PanicLog)

	err = service.New(c)
	if err != nil {
		panic(err)
	}
	g := grpc.NewServer(
		grpc.Address(c.Server.GRPC.Addr),
		grpc.Options(rpc.ChainUnaryInterceptor(
			recovery.UnaryServerInterceptor(),
			logging.UnaryServerInterceptor(),
			metric.UnaryServerInterceptor(),
		)),
	)
	// 注册接口
	impl.NewRankReconcileImpl()
	reflection.Register(g.Server)
	//pulsar消息
	mq.Init(c)
	//定时器
	timer.Init()

	k := karaoke.New(karaoke.Server(g))
	if err := k.Run(); err != nil {
		panic(err)
	}
}
