projectName = "rank_reconcile_svr"
packageName = "rank_reconcile_svr"

versionDir = "git.woa.com/tme/version"
gitTag = $(shell if git describe --tags --abbrev=0 2>/dev/null != "" ;then git describe --tags --abbrev=0; else git log --pretty=format:'%h' -n 1; fi)
gitCommit = $(shell git log --pretty=format:'%H' -n 1)
gitBranch = $(shell git symbolic-ref --short -q HEAD)
gitTreeState = $(shell if git status|grep -q 'clean';then echo clean; else echo dirty; fi)
buildAuthor = $(shell git config user.name)
buildDate = $(shell TZ=Asia/Shanghai date +%FT%T%z)
goVersion = $(shell go version | awk -F" " '{print $$3}')
platform = $(shell go version | awk -F" " '{print $$4}')

LDFlags="-w -X ${versionDir}.name=${projectName} -X ${versionDir}.gitTag=${gitTag} -X ${versionDir}.gitCommit=${gitCommit} \
		-X ${versionDir}.gitTreeState=${gitTreeState} -X ${versionDir}.buildAuthor=${buildAuthor} \
		-X ${versionDir}.buildDate=${buildDate} -X ${versionDir}.goVersion=${goVersion} \
		-X ${versionDir}.platform=${platform} -X ${versionDir}.gitBranch=${gitBranch}"

all: gotool
	@go build -o bin/${projectName} -v -ldflags ${LDFlags} -tags netgo .

clean:
	rm -f ${projectName}

gotool:
	gofmt -w .
	go vet .

bench:
	@echo null

help:
	@echo "make - compile the source code"
	@echo "make clean - remove binary file and vim swp files"
	@echo "make gotool - run go tool 'fmt' and 'vet'"
	@echo "make bench - run bench mark"

.PHONY: clean gotool help jce test bench

devops:
ifeq ($(id), )
	# Usage:
	#   make devops id=你的Devops环境对应的id，比如：DevopsBase环境的是：11061，http://music.isd.com/deploy/env_group/11061/
	#   到这里下载epc工具，现在支持mac版本/linux版本：https://iwiki.woa.com/pages/viewpage.action?pageId=665674522
else
	epc devops -d $(id) -p $(projectName) -f bin/$(projectName) -r ture -m true
endif
