module cnb.tmeoa.com/tme_bmp/component

go 1.23.3

toolchain go1.23.8

replace cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go => ../tme_bmp_pb_go

require (
	cnb.tmeoa.com/going/l5 v0.3.0
	cnb.tmeoa.com/tme_bmp/tme_bmp_pb_go v0.0.0-00010101000000-000000000000
	cnb.tmeoa.com/tme_bmp/tme_bmp_plib v0.54.0
	github.com/BurntSushi/toml v1.1.0
	github.com/apache/pulsar-client-go v0.10.0
	github.com/dromara/carbon/v2 v2.6.2
	github.com/go-redis/redis/v8 v8.11.5
	github.com/go-sql-driver/mysql v1.6.0
	github.com/golang/protobuf v1.5.4
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.10.0
	github.com/pkg/errors v0.9.1
	github.com/prometheus/client_golang v1.14.0
	github.com/samber/lo v1.49.1
	github.com/shopspring/decimal v1.4.0
	github.com/spf13/cast v1.4.1
	golang.org/x/sync v0.10.0
	google.golang.org/genproto v0.0.0-20220317150908-0efb43f6373e
	google.golang.org/grpc v1.71.0
	google.golang.org/protobuf v1.36.4
	gorm.io/driver/mysql v1.3.4
	gorm.io/gorm v1.25.7
)

require (
	cnb.tmeoa.com/going/cmlb v0.2.0 // indirect
	cnb.tmeoa.com/going/going-naming-selectors/cmlb v0.0.0-20240904065342-f926529f2a70 // indirect
	cnb.tmeoa.com/going/going-naming-selectors/cmlbtest v0.0.0-20240904065342-f926529f2a70 // indirect
	cnb.tmeoa.com/going/going-naming-selectors/gl5 v0.0.0-20240904065342-f926529f2a70 // indirect
	cnb.tmeoa.com/going/going-naming-selectors/kugourpc v0.0.0-20250304022641-22dd42624b2a // indirect
	cnb.tmeoa.com/going/going-naming-selectors/l5 v0.0.0-20241210092215-c260595df740 // indirect
	cnb.tmeoa.com/going/kugourpc v1.0.1 // indirect
	cnb.tmeoa.com/going/l5-cgo v0.3.0 // indirect
	cnb.tmeoa.com/going/utils v0.0.0-20240902041806-96dad2d66820 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/gomodule/redigo v1.8.8 // indirect
	golang.org/x/exp v0.0.0-20250106191152-7588d65b2ba8 // indirect
	k8s.io/apimachinery v0.32.0 // indirect
	k8s.io/klog/v2 v2.130.1 // indirect
	k8s.io/utils v0.0.0-20241104100929-3ea5e8cea738 // indirect
)

require (
	cnb.tmeoa.com/going/addressing v1.0.2 // indirect
	cnb.tmeoa.com/going/go-neat-dc v1.4.0 // indirect
	cnb.tmeoa.com/going/naming v1.0.0 // indirect
	github.com/99designs/go-keychain v0.0.0-20191008050251-8e49817e8af4 // indirect
	github.com/99designs/keyring v1.2.1 // indirect
	github.com/AthenZ/athenz v1.10.39 // indirect
	github.com/DataDog/zstd v1.5.0 // indirect
	github.com/ardielle/ardielle-go v1.5.2 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bits-and-blooms/bitset v1.4.0 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/danieljoos/wincred v1.1.2 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dvsekhvalnov/jose2go v1.6.0 // indirect
	github.com/frankban/quicktest v1.14.6 // indirect
	github.com/globalsign/mgo v0.0.0-20181015135952-eeefdecb41b8 // indirect
	github.com/godbus/dbus v0.0.0-20190726142602-4481cbc300e2 // indirect
	github.com/golang-jwt/jwt v3.2.1+incompatible // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/gsterjov/go-libsecret v0.0.0-20161001094733-a6f4afe4910c // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/kelseyhightower/envconfig v1.4.0 // indirect
	github.com/klauspost/compress v1.17.9 // indirect
	github.com/linkedin/goavro/v2 v2.9.8 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.4 // indirect
	github.com/mtibben/percent v0.2.1 // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/pierrec/lz4 v2.6.1+incompatible // indirect
	github.com/prometheus/client_model v0.3.0 // indirect
	github.com/prometheus/common v0.37.0 // indirect
	github.com/prometheus/procfs v0.8.0 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/uber/jaeger-client-go v2.30.0+incompatible // indirect
	github.com/uber/jaeger-lib v2.4.1+incompatible // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.8.0 // indirect
	go.uber.org/zap v1.21.0 // indirect
	golang.org/x/mod v0.22.0 // indirect
	golang.org/x/net v0.34.0 // indirect
	golang.org/x/oauth2 v0.25.0 // indirect
	golang.org/x/sys v0.29.0 // indirect
	golang.org/x/term v0.28.0 // indirect
	golang.org/x/text v0.21.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
